<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UK Schools Lead Generation Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-graduation-cap"></i>
                    <h1>UK Schools Lead Generation</h1>
                </div>
                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="totalSchools">0</span>
                        <span class="stat-label">Schools</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="totalContacts">0</span>
                        <span class="stat-label">Contacts</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="totalEmails">0</span>
                        <span class="stat-label">Email Suggestions</span>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                    <button class="btn btn-secondary" onclick="exportData()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Loading State -->
            <div id="loadingState" class="loading-state">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <p>Loading school data from Apollo.io...</p>
            </div>

            <!-- Dashboard Content -->
            <div id="dashboardContent" class="dashboard-content" style="display: none;">
                <!-- Summary Cards -->
                <section class="summary-section">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-school"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="schoolsProcessed">0</h3>
                                <p>Schools Processed</p>
                                <span class="card-subtitle" id="schoolsSubtitle">Multi Academy Trusts & Independent Schools</span>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="contactsFound">0</h3>
                                <p>Contacts Found</p>
                                <span class="card-subtitle" id="contactsSubtitle">Decision Makers & Administrators</span>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="emailSuggestions">0</h3>
                                <p>Email Suggestions</p>
                                <span class="card-subtitle" id="emailSubtitle">Generated from UK School Patterns</span>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="successRate">0%</h3>
                                <p>Success Rate</p>
                                <span class="card-subtitle" id="successSubtitle">Apollo Enrichment Success</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Filters and Search -->
                <section class="filters-section">
                    <div class="filters-container">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="searchInput" placeholder="Search schools, contacts, or domains...">
                        </div>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="all">All</button>
                            <button class="filter-btn" data-filter="MAT">MATs</button>
                            <button class="filter-btn" data-filter="Independent">Independent</button>
                            <button class="filter-btn" data-filter="Grammar">Grammar</button>
                            <button class="filter-btn" data-filter="enriched">Enriched</button>
                        </div>
                    </div>
                </section>

                <!-- Tabs -->
                <section class="tabs-section">
                    <div class="tabs">
                        <button class="tab-btn active" data-tab="schools">
                            <i class="fas fa-school"></i> Schools
                        </button>
                        <button class="tab-btn" data-tab="contacts">
                            <i class="fas fa-users"></i> Contacts
                        </button>
                        <button class="tab-btn" data-tab="emails">
                            <i class="fas fa-envelope"></i> Email Suggestions
                        </button>
                        <button class="tab-btn" data-tab="analytics">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </button>
                    </div>
                </section>

                <!-- Tab Content -->
                <section class="content-section">
                    <!-- Schools Tab -->
                    <div id="schoolsTab" class="tab-content active">
                        <div class="content-header">
                            <h2>UK Schools & Multi Academy Trusts</h2>
                            <p>Real data collected from Apollo.io organization enrichment</p>
                        </div>
                        <div id="schoolsGrid" class="schools-grid">
                            <!-- Schools will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Contacts Tab -->
                    <div id="contactsTab" class="tab-content">
                        <div class="content-header">
                            <h2>Education Contacts</h2>
                            <p>Decision makers and administrators from Apollo CRM</p>
                        </div>
                        <div id="contactsGrid" class="contacts-grid">
                            <!-- Contacts will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Email Suggestions Tab -->
                    <div id="emailsTab" class="tab-content">
                        <div class="content-header">
                            <h2>Email Suggestions</h2>
                            <p>Generated email addresses based on UK school patterns</p>
                        </div>
                        <div id="emailsGrid" class="emails-grid">
                            <!-- Email suggestions will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div id="analyticsTab" class="tab-content">
                        <div class="content-header">
                            <h2>Collection Analytics</h2>
                            <p>Performance metrics and data quality insights</p>
                        </div>
                        <div id="analyticsContent" class="analytics-content">
                            <!-- Analytics will be populated by JavaScript -->
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>Last updated: <span id="lastUpdated">Never</span></p>
                <p>Data source: Apollo.io Professional API</p>
                <p>API calls used: <span id="apiCallsUsed">0</span></p>
            </div>
        </footer>
    </div>

    <!-- Modals -->
    <div id="schoolModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalSchoolName">School Details</h3>
                <button class="modal-close" onclick="closeModal('schoolModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalSchoolContent">
                <!-- School details will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
