@import "../css/variables.module.scss";

.excalidraw {
  &.excalidraw-modal-container {
    position: absolute;
    z-index: var(--zIndex-modal);
  }

  .Modal {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    padding: calc(var(--space-factor) * 10);

    display: flex;
    flex-direction: column;

    .Island {
      padding: 2.5rem;
      border: 0;
      box-shadow: none;
      border-radius: 0;
    }

    &.animations-disabled {
      .Modal__background {
        animation: none;
      }

      .Modal__content {
        animation: none;
        opacity: 1;
      }
    }
  }

  .Modal__background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-color: rgba(#121212, 0.2);

    animation: Modal__background__fade-in 0.1s linear forwards;
  }

  .Modal__content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: var(--max-width);
    max-height: 100%;

    opacity: 0;
    transform: translateY(10px);
    animation: Modal__content_fade-in 0.025s ease-out 0s forwards;

    position: relative;
    overflow-y: auto;

    // for modals, reset blurry bg
    background: var(--island-bg-color);

    border: 1px solid var(--dialog-border-color);
    box-shadow: var(--modal-shadow);
    border-radius: 0.75rem;
    box-sizing: border-box;

    &:focus {
      outline: none;
    }
  }

  @keyframes Modal__background__fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes Modal__content_fade-in {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .Modal__close {
    color: var(--icon-fill-color);
    margin: 0;
    padding: 0.375rem;
    position: absolute;
    top: 1rem;
    right: 1rem;
    border: 0;
    background-color: transparent;
    line-height: 0;
    cursor: pointer;

    svg {
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  .Dialog--fullscreen {
    .Modal {
      padding: 0;
    }

    .Modal__content {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      max-width: 100%;
      border: 0;
      border-radius: 0;
    }
  }
}
