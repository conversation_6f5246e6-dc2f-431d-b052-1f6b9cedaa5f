#!/usr/bin/env node

import { config } from '../config/index.js';
import axios from 'axios';

async function testApollo() {
  console.log('🧪 Testing Apollo.io API...');
  console.log('API Key configured:', !!config.apollo.apiKey);
  console.log('Base URL:', config.apollo.baseUrl);
  
  const client = axios.create({
    baseURL: config.apollo.baseUrl,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'X-Api-Key': config.apollo.apiKey
    }
  });

  // Test different endpoints to see what's available
  const endpoints = [
    { name: 'Account Info', path: '/auth/health', params: {} },
    { name: 'Email Finder', path: '/email_finder', params: { domain: 'example.com' } },
    { name: 'People Search', path: '/people/search', params: { q_keywords: 'test', per_page: 1 } },
    { name: 'Mixed People Search', path: '/mixed_people/search', params: { q_keywords: 'test', per_page: 1 } }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\nTesting ${endpoint.name} (${endpoint.path})...`);

      const response = await client.get(endpoint.path, { params: endpoint.params });

      console.log(`✅ ${endpoint.name} - SUCCESS!`);
      console.log('Response status:', response.status);
      console.log('Credits remaining:', response.headers['x-daily-requests-left'] || 'Unknown');

      if (response.data) {
        console.log('Response keys:', Object.keys(response.data));
      }

      // If we found a working endpoint, break
      break;

    } catch (error) {
      console.log(`❌ ${endpoint.name} - FAILED`);
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data?.error || error.message);
    }
  }
}

testApollo();
