{"labels": {"paste": "Colar", "pasteAsPlaintext": "Colar como texto simples", "pasteCharts": "Colar g<PERSON>", "selectAll": "Selecionar tudo", "multiSelect": "Adicionar elemento à seleção", "moveCanvas": "Mover tela", "cut": "Cortar", "copy": "Copiar", "copyAsPng": "Copiar para a área de transferência como PNG", "copyAsSvg": "Copiar para a área de transferência como SVG", "copyText": "Copiar para Área de Transferência como texto", "copySource": "", "convertToCode": "", "bringForward": "Trazer para o primeiro plano", "sendToBack": "Enviar para o plano de fundo", "bringToFront": "Trazer para o primeiro plano", "sendBackward": "Enviar para trás", "delete": "<PERSON><PERSON><PERSON>", "copyStyles": "Copiar os estilos", "pasteStyles": "Colar os estilos", "stroke": "Contornos", "background": "Fundo", "fill": "Preenchimento", "strokeWidth": "Espessura do traço", "strokeStyle": "Estilo de traço", "strokeStyle_solid": "<PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dashed": "<PERSON><PERSON><PERSON>", "strokeStyle_dotted": "Pontil<PERSON><PERSON>", "sloppiness": "Desleixo", "opacity": "Opacidade", "textAlign": "Alinhamento do texto", "edges": "Ares<PERSON>", "sharp": "<PERSON><PERSON><PERSON><PERSON>", "round": "Redondo", "arrowheads": "Pontas", "arrowhead_none": "<PERSON><PERSON><PERSON><PERSON>", "arrowhead_arrow": "<PERSON><PERSON>", "arrowhead_bar": "Barr<PERSON>", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Triângulo", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "<PERSON><PERSON><PERSON>", "fontFamily": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "addWatermark": "Adicionar \"Feito com Excalidraw\"", "handDrawn": "Manus<PERSON>rito", "normal": "Normal", "code": "Código", "small": "Pequeno", "medium": "Médio", "large": "Grande", "veryLarge": "<PERSON>ito grande", "solid": "<PERSON><PERSON><PERSON><PERSON>", "hachure": "Eclosão", "zigzag": "ziguezague", "crossHatch": "Sombreado", "thin": "Fino", "bold": "<PERSON><PERSON><PERSON><PERSON>", "left": "E<PERSON>rda", "center": "Centralizar", "right": "<PERSON><PERSON><PERSON>", "extraBold": "<PERSON><PERSON> es<PERSON>so", "architect": "Arquitecto", "artist": "Artista", "cartoonist": "Caricaturista", "fileTitle": "Nome do ficheiro", "colorPicker": "<PERSON><PERSON>or de cores", "canvasColors": "Usado na tela", "canvasBackground": "Fundo da área de desenho", "drawingCanvas": "<PERSON><PERSON>", "layers": "Camadas", "actions": "Ações", "language": "Idioma", "liveCollaboration": "Colaboração ao vivo...", "duplicateSelection": "Duplicar", "untitled": "<PERSON><PERSON> tí<PERSON>lo", "name": "Nome", "yourName": "O seu nome", "madeWithExcalidraw": "Feito com Excalidraw", "group": "Agrupar se<PERSON>", "ungroup": "Desagrupar se<PERSON>ção", "collaborators": "Colaboradores", "showGrid": "<PERSON><PERSON> g<PERSON>", "addToLibrary": "Adicionar à biblioteca", "removeFromLibrary": "Remover da biblioteca", "libraryLoadingMessage": "A carregar a biblioteca…", "libraries": "Procurar bibliotecas", "loadingScene": "A carregar a cena…", "align": "Alinhamento", "alignTop": "<PERSON><PERSON><PERSON> ao topo", "alignBottom": "Alinhar ao fundo", "alignLeft": "Alinhar à esquerda", "alignRight": "Alinhar à direita", "centerVertically": "Centrar verticalmente", "centerHorizontally": "Centrar horizontalmente", "distributeHorizontally": "Distribuir horizontalmente", "distributeVertically": "Distribuir verticalmente", "flipHorizontal": "Inverter horizontalmente", "flipVertical": "Inverter verticalmente", "viewMode": "Modo de visualização", "share": "Partilhar", "showStroke": "Mostrar seletor de cores do traço", "showBackground": "Mostrar seletor de cores do fundo", "toggleTheme": "Alternar tema", "personalLib": "Biblioteca pessoal", "excalidrawLib": "Biblioteca do Excalidraw", "decreaseFontSize": "Reduzir o tamanho do tipo de letra", "increaseFontSize": "Aumentar o tamanho do tipo de letra", "unbindText": "Desvincular texto", "bindText": "Ligar texto ao recipiente", "createContainerFromText": "Envolver texto num recipiente", "link": {"edit": "Editar ligação", "editEmbed": "", "create": "Criar ligação", "createEmbed": "", "label": "Ligação", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "<PERSON><PERSON> l<PERSON>", "exit": "<PERSON>r do <PERSON> de linha"}, "elementLock": {"lock": "Bloquear", "unlock": "Desb<PERSON>que<PERSON>", "lockAll": "Bloquear todos", "unlockAll": "Desbloquear todos"}, "statusPublished": "Publicado", "sidebarLock": "Manter a barra lateral aberta", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "Ainda não foram adicionados nenhuns itens...", "hint_emptyLibrary": "Seleccione um item na tela para adicioná-lo aqui, ou então instale uma biblioteca do repositório público abaixo.", "hint_emptyPrivateLibrary": "Seleccione um item na tela para adicioná-lo aqui."}, "buttons": {"clearReset": "Limpar a área de desenho e redefinir a cor de fundo", "exportJSON": "Exportar para ficheiro", "exportImage": "Exportar imagem...", "export": "Guardar para...", "copyToClipboard": "Copiar para o clipboard", "save": "Guardar no ficheiro atual", "saveAs": "Guardar como", "load": "Abrir", "getShareableLink": "Obter um link de partilha", "close": "<PERSON><PERSON><PERSON>", "selectLanguage": "Selecionar idioma", "scrollBackToContent": "Voltar ao conteúdo", "zoomIn": "Aumentar zoom", "zoomOut": "Diminuir zoom", "resetZoom": "Redefinir zoom", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "resetLibrary": "Repor a biblioteca", "createNewRoom": "Criar nova sala", "fullScreen": "Ecrã inteiro", "darkMode": "<PERSON><PERSON> es<PERSON>ro", "lightMode": "<PERSON>do claro", "zenMode": "Modo zen", "objectsSnapMode": "", "exitZenMode": "Sair do modo zen", "cancel": "<PERSON><PERSON><PERSON>", "clear": "Limpar", "remove": "Remover", "embed": "", "publishLibrary": "Publicar", "submit": "Enviar", "confirm": "Confirmar", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "<PERSON>to irá limpar toda a área de desenho. Tem a certeza?", "couldNotCreateShareableLink": "Não foi possível criar um link partilhável.", "couldNotCreateShareableLinkTooBig": "Não foi possível criar um link partilhável: a cena é muito grande", "couldNotLoadInvalidFile": "Não foi possível carregar o ficheiro inválido", "importBackendFailed": "A importação do servidor falhou.", "cannotExportEmptyCanvas": "Não é possível exportar uma área de desenho vazia.", "couldNotCopyToClipboard": "Não foi possível copiar para a área de transferência.", "decryptFailed": "Não foi possível desencriptar os dados.", "uploadedSecurly": "O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros não podem ler o conteúdo.", "loadSceneOverridePrompt": "Se carregar um desenho externo substituirá o conteúdo existente. Quer continuar?", "collabStopOverridePrompt": "Ao interromper a sessão irá substituir o último desenho guardado. Tem a certeza?\n\n(<PERSON>aso queira manter o último desenho, simplesmente feche a janela do navegador.)", "errorAddingToLibrary": "Não foi possível adicionar o item à biblioteca", "errorRemovingFromLibrary": "Não foi possível remover o item da biblioteca", "confirmAddLibrary": "<PERSON><PERSON> adici<PERSON> {{numShapes}} forma(s) à sua biblioteca. Tem a certeza?", "imageDoesNotContainScene": "Esta imagem parece não conter dados de cenas. Ativou a incorporação da cena durante a exportação?", "cannotRestoreFromImage": "Não foi possível restaurar a cena deste ficheiro de imagem", "invalidSceneUrl": "Não foi possível importar a cena a partir do URL fornecido. Ou está mal formado ou não contém dados JSON do Excalidraw válidos.", "resetLibrary": "Isto irá limpar a sua biblioteca. Tem a certeza?", "removeItemsFromsLibrary": "Apagar {{count}} item(ns) da biblioteca?", "invalidEncryptionKey": "Chave de encriptação deve ter 22 caracteres. A colaboração ao vivo está desativada.", "collabOfflineWarning": "Sem ligação à internet disponível.\nAs suas alterações não serão salvas!"}, "errors": {"unsupportedFileType": "Tipo de ficheiro não suportado.", "imageInsertError": "Não foi possível inserir a imagem, tente novamente mais tarde...", "fileTooBig": "O ficheiro é muito grande. O tamanho máximo permitido é {{maxSize}}.", "svgImageInsertError": "Não foi possível inserir a imagem SVG. A marcação SVG parece inválida.", "failedToFetchImage": "", "invalidSVGString": "SVG inválido.", "cannotResolveCollabServer": "Não foi possível fazer a ligação ao servidor colaborativo. Por favor, volte a carregar a página e tente novamente.", "importLibraryError": "Não foi possível carregar a biblioteca", "collabSaveFailed": "Não foi possível guardar na base de dados de backend. Se os problemas persistirem, guarde o ficheiro localmente para garantir que não perde o seu trabalho.", "collabSaveFailed_sizeExceeded": "Não foi possível guardar na base de dados de backend, o ecrã parece estar muito grande. Deve guardar o ficheiro localmente para garantir que não perde o seu trabalho.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "Se<PERSON><PERSON>", "image": "Inserir imagem", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "diamond": "Losango", "ellipse": "Elipse", "arrow": "Fle<PERSON>", "line": "<PERSON><PERSON>", "freedraw": "<PERSON><PERSON><PERSON>", "text": "Texto", "library": "Biblioteca", "lock": "Manter a ferramenta selecionada ativa após desenhar", "penMode": "<PERSON>do <PERSON> - impedir toque", "link": "Acrescentar/ Adicionar ligação para uma forma seleccionada", "eraser": "Borracha", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "Mão (ferramenta de movimento da tela)", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "Ações da área de des<PERSON>ho", "selectedShapeActions": "Ações das formas selecionadas", "shapes": "Formas"}, "hints": {"canvasPanning": "Para mover a tela, carregue na roda do rato ou na barra de espaço enquanto arrasta, ou use a ferramenta da mão", "linearElement": "Clique para iniciar v<PERSON><PERSON>s pontos, arraste para uma única linha", "freeDraw": "Clique e arraste, large quando terminar", "text": "Dica: também pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de seleção", "embeddable": "", "text_selected": "Clique duas vezes ou pressione a tecla Enter para editar o texto", "text_editing": "Pressione a tecla Escape ou CtrlOrCmd+ENTER para terminar a edição", "linearElementMulti": "Clique no último ponto ou pressione Escape ou Enter para terminar", "lockAngle": "Pode restringir o ângulo mantendo premida a tecla SHIFT", "resize": "Pode restringir as proporções mantendo a tecla SHIFT premida enquanto redimensiona,\nmantenha a tecla ALT premida para redimensionar a partir do centro", "resizeImage": "Pode redimensionar livremente mantendo pressionada a tecla SHIFT,\nmantenha pressionada a tecla ALT para redimensionar do centro", "rotate": "Pode restringir os ângulos mantendo a tecla SHIFT premida enquanto roda", "lineEditor_info": "Pressione CtrlOrCmd e faça um duplo-clique ou pressione CtrlOrCmd + Enter para editar pontos", "lineEditor_pointSelected": "Carregue na tecla Delete para remover o(s) ponto(s), CtrlOuCmd+D para duplicar, ou arraste para mover", "lineEditor_nothingSelected": "Seleccione um ponto para editar (carregue em SHIFT para seleccionar vários),\nou carregue em Alt e clique para acrescentar novos pontos", "placeImage": "Clique para colocar a imagem ou clique e arraste para definir o seu tamanho manualmente", "publishLibrary": "Publique a sua própria biblioteca", "bindTextToElement": "Carregue Enter para acrescentar texto", "deepBoxSelect": "Mantenha a tecla CtrlOrCmd carregada para selecção profunda, impedindo o arrastamento", "eraserRevert": "Carregue também em Alt para reverter os elementos marcados para serem apagados", "firefox_clipboard_write": "Esta função pode provavelmente ser ativada definindo a opção \"dom.events.asyncClipboard.clipboardItem\" como \"true\". Para alterar os sinalizadores do navegador no Firefox, visite a página \"about:config\".", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Não é possível mostrar uma pré-visualização", "canvasTooBig": "A área de desenho pode ser muito grande.", "canvasTooBigTip": "Dica: tente aproximar um pouco os elementos mais distantes."}, "errorSplash": {"headingMain": "Foi encontrado um erro. Tente <button>recarregar a página.</button>", "clearCanvasMessage": "Se a recarga não funcionar, tente <button>a limpar a área de desenho.</button>", "clearCanvasCaveat": " <PERSON><PERSON> em perda de trabalho ", "trackedToSentry": "O erro com o identificador {{eventId}} foi rastreado no nosso sistema.", "openIssueMessage": "Fomos muito cautelosos para não incluir suas informações de cena no erro. Se sua cena não for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informações abaixo, copiando e colando no relatório de erros no GitHub.", "sceneContent": "<PERSON><PERSON><PERSON><PERSON>:"}, "roomDialog": {"desc_intro": "Pode convidar pessoas para colaborarem na sua cena atual.", "desc_privacy": "<PERSON>ão se preocupe, a sessão usa criptografia de ponta-a-ponta, por isso o que desenhar permanecerá privado. Nem mesmo o nosso servidor poderá ver o que cria.", "button_startSession": "<PERSON><PERSON><PERSON>", "button_stopSession": "<PERSON><PERSON>", "desc_inProgressIntro": "A sessão de colaboração ao vivo está agora em andamento.", "desc_shareLink": "Partilhe este link com qualquer pessoa com quem queira colaborar:", "desc_exitSession": "Interrompendo a sessão irá desconectar-se da sala, mas poderá continuar a trabalhar com a cena localmente. Note que isso não afetará outras pessoas e elas ainda poderão colaborar nas versões deles.", "shareTitle": "Participe numa sessão de colaboração ao vivo no Excalidraw"}, "errorDialog": {"title": "Erro"}, "exportDialog": {"disk_title": "Guardar no disco", "disk_details": "Exportar os dados da cena para um ficheiro do qual poderá importar mais tarde.", "disk_button": "Guardar num ficheiro", "link_title": "<PERSON>", "link_details": "Exportar como um link de apenas leitura.", "link_button": "Exportar para link", "excalidrawplus_description": "Guardar a cena no seu espaço de trabalho Excalidraw+", "excalidrawplus_button": "Exportar", "excalidrawplus_exportError": "Não foi possível exportar para o Excalidraw+ neste momento..."}, "helpDialog": {"blog": "<PERSON><PERSON> o nosso blogue", "click": "clicar", "deepSelect": "Selecção profunda", "deepBoxSelect": "Selecção profunda dentro da caixa, impedindo que seja arrastada", "curvedArrow": "Seta curva", "curvedLine": "Linha curva", "documentation": "Documentação", "doubleClick": "clique duplo", "drag": "arrastar", "editor": "Editor", "editLineArrowPoints": "Editar pontos de linha/seta", "editText": "Editar texto / adicionar etiqueta", "github": "Encontrou algum problema? Informe-nos", "howto": "Siga os nossos guias", "or": "ou", "preventBinding": "Prevenir fixação de seta", "tools": "Ferramentas", "shortcuts": "Atalhos de teclado", "textFinish": "<PERSON><PERSON><PERSON> edi<PERSON> (editor texto)", "textNewLine": "Adicionar nova linha (editor de texto)", "title": "<PERSON><PERSON><PERSON>", "view": "Visualizar", "zoomToFit": "Ajustar para todos os elementos caberem", "zoomToSelection": "Ampliar a seleção", "toggleElementLock": "Trancar/destrancar sele<PERSON>", "movePageUpDown": "Mover página para cima / baixo", "movePageLeftRight": "Mover página para esquerda / direita"}, "clearCanvasDialog": {"title": "Apagar tela"}, "publishDialog": {"title": "Publicar biblioteca", "itemName": "Nome do item", "authorName": "Nome do autor", "githubUsername": "Nome de utilizador do GitHub", "twitterUsername": "Nome de utilizador no Twitter", "libraryName": "Nome da biblioteca", "libraryDesc": "Descrição da biblioteca", "website": "Página web", "placeholder": {"authorName": "Introduza o seu nome ou nome de utilizador", "libraryName": "Nome da sua biblioteca", "libraryDesc": "Descrição da sua biblioteca para ajudar as pessoas a entender a utilização dela", "githubHandle": "Identificador do GitHub (opcional), para que possa editar a biblioteca depois desta ser enviada para revisão", "twitterHandle": "Nome do Twitter (opcional), para que saibamos quem merece os créditos na promoção via Twitter", "website": "Ligação para a sua página pessoal ou qualquer outra (opcional)"}, "errors": {"required": "Obrigatório", "website": "Introduza um URL válido"}, "noteDescription": "Envie a sua biblioteca para ser incluída no <link>repositório de bibliotecas públicas</link>para outras pessoas a poderem usar nos seus próprios desenhos.", "noteGuidelines": "A biblioteca precisa ser aprovada manualmente primeiro. Por favor, leia <link>orientações</link> antes de enviar. Vai precisar de uma conta no GitHub para comunicar e fazer alterações se solicitado, mas não é estritamente necessária.", "noteLicense": "Ao enviar, concorda que a biblioteca será publicada sob a <link>Licença MIT, </link>o que significa, de forma resumida, que qualquer pessoa pode utilizá-la sem restrições.", "noteItems": "Cada item da biblioteca deve ter o seu próprio nome para que este seja pesquisável com filtros. Os seguintes itens da biblioteca serão incluídos:", "atleastOneLibItem": "Por favor, seleccione pelo menos um item da biblioteca para começar", "republishWarning": "Nota: alguns dos itens seleccionados estão marcados como já publicados/enviados. Só deve reenviar itens ao actualizar uma biblioteca existente ou submissão."}, "publishSuccessDialog": {"title": "Biblioteca enviada", "content": "<PERSON><PERSON><PERSON> {{authorName}}. A sua biblioteca foi enviada para análise. Pode acompanhar o status<link>aqui</link>"}, "confirmDialog": {"resetLibrary": "Repor a biblioteca", "removeItemsFromLib": "Remover os itens seleccionados da biblioteca"}, "imageExportDialog": {"header": "Exportar imagem", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "<PERSON><PERSON> embutida", "scale": "", "padding": "Espaçamento"}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "Exportar em PNG", "exportToSvg": "Exportar em SVG", "copyPngToClipboard": ""}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "Os seus desenhos são encriptados de ponta-a-ponta, por isso os servidores do Excalidraw nunca os verão.", "link": "Publicação de blogue na encriptação ponta-a-ponta no Excalidraw"}, "stats": {"angle": "<PERSON><PERSON><PERSON>", "element": "Elemento", "elements": "Elementos", "height": "Altura", "scene": "<PERSON><PERSON>", "selected": "Selecionado", "storage": "Armazenamento", "title": "Estatísticas para nerds", "total": "Total", "version": "Vers<PERSON>", "versionCopy": "Clique para copiar", "versionNotAvailable": "Versão não disponível", "width": "<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Acrescentado à biblioteca", "copyStyles": "Estilos copiados.", "copyToClipboard": "Copiado para a área de transferência.", "copyToClipboardAsPng": "{{exportSelection}} copiado para a área de transferência como PNG\n({{exportColorScheme}})", "fileSaved": "<PERSON><PERSON><PERSON> guardado.", "fileSavedToFilename": "Guardado como {filename}", "canvas": "<PERSON><PERSON>", "selection": "se<PERSON>ção", "pasteAsSingleElement": "Usar {{shortcut}} para colar como um único elemento,\nou colar num editor de texto existente", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Transparente", "black": "Preto", "white": "Branco", "red": "Vermelho", "pink": "<PERSON>", "grape": "<PERSON><PERSON>", "violet": "<PERSON><PERSON>", "gray": "Cinza", "blue": "Azul", "cyan": "", "teal": "", "green": "Verde", "yellow": "<PERSON><PERSON>", "orange": "<PERSON><PERSON>", "bronze": "Bronze"}, "welcomeScreen": {"app": {"center_heading": "Todos os dados são guardados no seu navegador local.", "center_heading_plus": "Queria antes ir para o Excalidraw+?", "menuHint": "Exportar, preferências, idiomas..."}, "defaults": {"menuHint": "Exportar, preferências e outros...", "center_heading": "Diagramas. Feito. Simples.", "toolbarHint": "Escolha uma ferramenta e comece a desenhar!", "helpHint": "Atalhos e ajuda"}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "Cores", "shades": "Tons", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "Guardar no disco", "button": "Guardar no disco", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "Carregar a partir de ficheiro", "button": "Carregar a partir de ficheiro", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}