{"labels": {"paste": "Qoyıw", "pasteAsPlaintext": "Ápiwayı tekst retinde qoyıw", "pasteCharts": "Diagrammalardı qoyıw", "selectAll": "Barlıǵın ta<PERSON>law", "multiSelect": "", "moveCanvas": "", "cut": "Qıyıw", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copyAsPng": "Almasıw buferine PNG retinde k<PERSON><PERSON>ip alıw", "copyAsSvg": "Almasıw buferine SVG retinde kóshirip alıw", "copyText": "Almasıw buferine tekst retinde kós<PERSON>ip alıw", "copySource": "", "convertToCode": "", "bringForward": "", "sendToBack": "", "bringToFront": "", "sendBackward": "", "delete": "<PERSON><PERSON><PERSON><PERSON>", "copyStyles": "", "pasteStyles": "", "stroke": "Jiyek", "background": "Fon", "fill": "", "strokeWidth": "", "strokeStyle": "", "strokeStyle_solid": "", "strokeStyle_dashed": "", "strokeStyle_dotted": "", "sloppiness": "", "opacity": "", "textAlign": "", "edges": "<PERSON><PERSON><PERSON><PERSON>", "sharp": "", "round": "", "arrowheads": "", "arrowhead_none": "<PERSON><PERSON>", "arrowhead_arrow": "<PERSON><PERSON>", "arrowhead_bar": "", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Shrift ólshemi", "fontFamily": "Shrift toplamı", "addWatermark": "", "handDrawn": "", "normal": "", "code": "Kod", "small": "", "medium": "<PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON><PERSON>", "veryLarge": "<PERSON><PERSON><PERSON><PERSON>", "solid": "", "hachure": "", "zigzag": "Zigzag", "crossHatch": "", "thin": "<PERSON><PERSON><PERSON><PERSON>", "bold": "Qalıń", "left": "", "center": "", "right": "", "extraBold": "", "architect": "", "artist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cartoonist": "", "fileTitle": "<PERSON><PERSON>", "colorPicker": "<PERSON><PERSON><PERSON>", "canvasColors": "", "canvasBackground": "", "drawingCanvas": "", "layers": "Qatlamlar", "actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "Til", "liveCollaboration": "", "duplicateSelection": "<PERSON><PERSON><PERSON>", "untitled": "Atamasız", "name": "Ataması", "yourName": "Atıń<PERSON>z", "madeWithExcalidraw": "Excalidraw j<PERSON>minde islengen", "group": "", "ungroup": "", "collaborators": "Qatnasıwshılar", "showGrid": "", "addToLibrary": "Kitapxanaǵa qosıw", "removeFromLibrary": "Kitapxanadan alıp taslaw", "libraryLoadingMessage": "<PERSON><PERSON><PERSON><PERSON>…", "libraries": "Kitapxanalardı kóriw", "loadingScene": "<PERSON><PERSON><PERSON>…", "align": "", "alignTop": "", "alignBottom": "", "alignLeft": "", "alignRight": "", "centerVertically": "", "centerHorizontally": "", "distributeHorizontally": "", "distributeVertically": "", "flipHorizontal": "", "flipVertical": "", "viewMode": "<PERSON><PERSON><PERSON><PERSON>", "share": "Bólisiw", "showStroke": "", "showBackground": "", "toggleTheme": "Temanı ózgertiw", "personalLib": "<PERSON><PERSON>", "excalidrawLib": "Excalidraw kitapxanası", "decreaseFontSize": "Shrift <PERSON><PERSON><PERSON><PERSON> k<PERSON>w", "increaseFontSize": "Shrift <PERSON><PERSON><PERSON>", "unbindText": "", "bindText": "", "createContainerFromText": "", "link": {"edit": "Siltemeni ózgertiw", "editEmbed": "", "create": "Siltemeni jaratıw", "createEmbed": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "Qatardı ózgertiw", "exit": "Qatardı ózgertiw redaktorınan shıǵıw"}, "elementLock": {"lock": "Qulıplaw", "unlock": "Qulıptan shıǵarıw", "lockAll": "Barlıǵın qulıplaw", "unlockAll": "Barlıǵın qulıptan shıǵarıw"}, "statusPublished": "", "sidebarLock": "", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "", "hint_emptyLibrary": "", "hint_emptyPrivateLibrary": ""}, "buttons": {"clearReset": "", "exportJSON": "", "exportImage": "S<PERSON><PERSON>retti eksportlaw...", "export": "<PERSON><PERSON><PERSON> sa<PERSON>...", "copyToClipboard": "Almasıw buferine k<PERSON> al<PERSON>ı", "save": "Ámeldegi faylǵa saqlaw", "saveAs": "<PERSON><PERSON><PERSON>", "load": "Ashıw", "getShareableLink": "", "close": "Jabıw", "selectLanguage": "<PERSON><PERSON><PERSON>", "scrollBackToContent": "", "zoomIn": "", "zoomOut": "", "resetZoom": "", "menu": "<PERSON><PERSON>", "done": "Ta<PERSON>ın", "edit": "Ózgertiw", "undo": "", "redo": "", "resetLibrary": "", "createNewRoom": "", "fullScreen": "<PERSON><PERSON><PERSON>q <PERSON>n", "darkMode": "Qarańǵı tema", "lightMode": "Jaq<PERSON>ı tema", "zenMode": "", "objectsSnapMode": "", "exitZenMode": "", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "Tazalaw", "remove": "<PERSON><PERSON><PERSON><PERSON>", "embed": "", "publishLibrary": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Jiberiw", "confirm": "Tastıyıqlaw", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "", "couldNotCreateShareableLink": "", "couldNotCreateShareableLinkTooBig": "", "couldNotLoadInvalidFile": "", "importBackendFailed": "", "cannotExportEmptyCanvas": "", "couldNotCopyToClipboard": "Almasıw buferine k<PERSON>ip alıw <PERSON>ge aspadı.", "decryptFailed": "", "uploadedSecurly": "", "loadSceneOverridePrompt": "", "collabStopOverridePrompt": "", "errorAddingToLibrary": "", "errorRemovingFromLibrary": "", "confirmAddLibrary": "", "imageDoesNotContainScene": "", "cannotRestoreFromImage": "", "invalidSceneUrl": "", "resetLibrary": "", "removeItemsFromsLibrary": "", "invalidEncryptionKey": "", "collabOfflineWarning": ""}, "errors": {"unsupportedFileType": "", "imageInsertError": "", "fileTooBig": "", "svgImageInsertError": "", "failedToFetchImage": "", "invalidSVGString": "Jaramsız SVG.", "cannotResolveCollabServer": "", "importLibraryError": "Kitapxananı júklew ámelge aspadı", "collabSaveFailed": "", "collabSaveFailed_sizeExceeded": "", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "", "image": "<PERSON><PERSON><PERSON><PERSON> qoy<PERSON>w", "rectangle": "<PERSON><PERSON><PERSON> m<PERSON>", "diamond": "", "ellipse": "", "arrow": "", "line": "Sızıq", "freedraw": "Sızıw", "text": "Tekst", "library": "Kitapxana", "lock": "", "penMode": "", "link": "", "eraser": "<PERSON><PERSON><PERSON><PERSON>", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "", "selectedShapeActions": "", "shapes": "<PERSON><PERSON><PERSON><PERSON>"}, "hints": {"canvasPanning": "", "linearElement": "", "freeDraw": "", "text": "", "embeddable": "", "text_selected": "", "text_editing": "", "linearElementMulti": "", "lockAngle": "", "resize": "", "resizeImage": "", "rotate": "", "lineEditor_info": "", "lineEditor_pointSelected": "", "lineEditor_nothingSelected": "", "placeImage": "", "publishLibrary": "", "bindTextToElement": "Tekst qosıw ushın Enter túymesin basıń", "deepBoxSelect": "", "eraserRevert": "", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Aldınnan kóriwdi kórsetiw múmkin emes", "canvasTooBig": "", "canvasTooBigTip": ""}, "errorSplash": {"headingMain": "", "clearCanvasMessage": "", "clearCanvasCaveat": "", "trackedToSentry": "", "openIssueMessage": "", "sceneContent": ""}, "roomDialog": {"desc_intro": "", "desc_privacy": "", "button_startSession": "", "button_stopSession": "", "desc_inProgressIntro": "", "desc_shareLink": "", "desc_exitSession": "", "shareTitle": ""}, "errorDialog": {"title": "Qátelik"}, "exportDialog": {"disk_title": "<PERSON><PERSON><PERSON> saq<PERSON>", "disk_details": "", "disk_button": "Faylǵa saqlaw", "link_title": "", "link_details": "", "link_button": "Siltemege eksportlaw", "excalidrawplus_description": "", "excalidrawplus_button": "Eksportlaw", "excalidrawplus_exportError": ""}, "helpDialog": {"blog": "Biziń blogtı oqıń", "click": "basıw", "deepSelect": "", "deepBoxSelect": "", "curvedArrow": "", "curvedLine": "", "documentation": "Hújjetshilik", "doubleClick": "", "drag": "", "editor": "Redaktor", "editLineArrowPoints": "", "editText": "", "github": "", "howto": "", "or": "yamasa", "preventBinding": "", "tools": "Ásbaplar", "shortcuts": "", "textFinish": "", "textNewLine": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON><PERSON>", "zoomToFit": "", "zoomToSelection": "", "toggleElementLock": "", "movePageUpDown": "", "movePageLeftRight": ""}, "clearCanvasDialog": {"title": ""}, "publishDialog": {"title": "", "itemName": "", "authorName": "A<PERSON>tor atı", "githubUsername": "GitHub paydalanıwshı atı", "twitterUsername": "Twitter paydalanıwshı atı", "libraryName": "Kitap<PERSON>na atama<PERSON>ı", "libraryDesc": "", "website": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"authorName": "Atıń<PERSON>z yamasa paydal<PERSON>ıwshı atı", "libraryName": "Kitapxanańız ataması", "libraryDesc": "", "githubHandle": "", "twitterHandle": "", "website": "<PERSON><PERSON> veb-say<PERSON><PERSON><PERSON><PERSON><PERSON> yamasa basqa saytqa silteme (májbúriy emes)"}, "errors": {"required": "<PERSON><PERSON>j<PERSON><PERSON><PERSON><PERSON>", "website": "Jaramlı URL mánzil kirgiziń"}, "noteDescription": "", "noteGuidelines": "", "noteLicense": "", "noteItems": "", "atleastOneLibItem": "", "republishWarning": ""}, "publishSuccessDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": ""}, "confirmDialog": {"resetLibrary": "Kitapxananı qayta ornatıw", "removeItemsFromLib": ""}, "imageExportDialog": {"header": "Súwretti e<PERSON>portlaw", "label": {"withBackground": "Fon", "onlySelected": "", "darkMode": "Qarańǵı tema", "embedScene": "", "scale": "Kólem", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Almasıw buferine k<PERSON> alıw"}}, "encrypted": {"tooltip": "", "link": ""}, "stats": {"angle": "", "element": "Element", "elements": "<PERSON><PERSON><PERSON>", "height": "", "scene": "<PERSON>xna", "selected": "Tańlandı", "storage": "", "title": "", "total": "", "version": "Vers<PERSON>", "versionCopy": "<PERSON><PERSON><PERSON><PERSON> alıw ushın basıń", "versionNotAvailable": "", "width": "<PERSON><PERSON>"}, "toast": {"addedToLibrary": "Kitapxanaǵa qosıldı", "copyStyles": "", "copyToClipboard": "Almasıw buferine k<PERSON> al<PERSON>.", "copyToClipboardAsPng": "", "fileSaved": "<PERSON><PERSON>.", "fileSavedToFilename": "{filename} saqlandı", "canvas": "", "selection": "", "pasteAsSingleElement": "", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "", "black": "Qara", "white": "Aq", "red": "Qızıl", "pink": "Qızǵılt", "grape": "", "violet": "Qızǵılt kók", "gray": "", "blue": "Kók", "cyan": "<PERSON><PERSON><PERSON>pan", "teal": "Piruza", "green": "Jasıl", "yellow": "Sarı", "orange": "Qızǵılt sarı", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "", "center_heading_plus": "Excalidraw+ ge ótiwdi qáleysiz be?", "menuHint": "<PERSON><PERSON><PERSON><PERSON>, saz<PERSON><PERSON>, tiller, ..."}, "defaults": {"menuHint": "<PERSON><PERSON><PERSON><PERSON>, sazlawlar hám basqa...", "center_heading": "Diagrammalar. Ápiwayı.", "toolbarHint": "", "helpHint": ""}}, "colorPicker": {"mostUsedCustomColors": "Kóp qollanılatuǵın arnawlı reńler", "colors": "<PERSON><PERSON><PERSON>", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "<PERSON><PERSON><PERSON><PERSON> retinde eksportlaw", "button": "<PERSON><PERSON><PERSON><PERSON> retinde eksportlaw", "description": ""}, "saveToDisk": {"title": "<PERSON><PERSON><PERSON> saq<PERSON>", "button": "<PERSON><PERSON><PERSON> saq<PERSON>", "description": ""}, "excalidrawPlus": {"title": "Excalidraw+", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "description": ""}, "shareableLink": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}