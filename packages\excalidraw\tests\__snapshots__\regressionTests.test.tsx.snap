// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`given element A and group of elements B and given both are selected when user clicks on B, on pointer up only elements from B should be selected > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id6": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id15": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`given element A and group of elements B and given both are selected when user clicks on B, on pointer up only elements from B should be selected > [end of test] number of elements 1`] = `0`;

exports[`given element A and group of elements B and given both are selected when user clicks on B, on pointer up only elements from B should be selected > [end of test] number of renders 1`] = `18`;

exports[`given element A and group of elements B and given both are selected when user clicks on B, on pointer up only elements from B should be selected > [end of test] redo stack 1`] = `[]`;

exports[`given element A and group of elements B and given both are selected when user clicks on B, on pointer up only elements from B should be selected > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 60,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id15": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id15",
            ],
            "index": "a2",
            "version": 5,
          },
          "inserted": {
            "groupIds": [],
            "index": "a0",
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id15",
            ],
            "index": "a3",
            "version": 5,
          },
          "inserted": {
            "groupIds": [],
            "index": "a2",
            "version": 3,
          },
        },
      },
    },
    "id": "id17",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id20",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id23",
  },
]
`;

exports[`given element A and group of elements B and given both are selected when user shift-clicks on B, on pointer up only element A should be selected > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id0": true,
    "id12": false,
    "id3": true,
    "id6": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`given element A and group of elements B and given both are selected when user shift-clicks on B, on pointer up only element A should be selected > [end of test] number of elements 1`] = `0`;

exports[`given element A and group of elements B and given both are selected when user shift-clicks on B, on pointer up only element A should be selected > [end of test] number of renders 1`] = `16`;

exports[`given element A and group of elements B and given both are selected when user shift-clicks on B, on pointer up only element A should be selected > [end of test] redo stack 1`] = `[]`;

exports[`given element A and group of elements B and given both are selected when user shift-clicks on B, on pointer up only element A should be selected > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 100,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 100,
            "x": 110,
            "y": 110,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 100,
            "x": 220,
            "y": 220,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "index": "a2",
            "version": 5,
          },
          "inserted": {
            "groupIds": [],
            "index": "a0",
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "index": "a3",
            "version": 5,
          },
          "inserted": {
            "groupIds": [],
            "index": "a2",
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id17",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
          "selectedGroupIds": {
            "id0": true,
            "id12": false,
            "id3": true,
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
            "id6": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id20",
  },
]
`;

exports[`regression tests > Cmd/Ctrl-click exclusively select element under pointer > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": "id28",
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": false,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id19": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > Cmd/Ctrl-click exclusively select element under pointer > [end of test] number of elements 1`] = `0`;

exports[`regression tests > Cmd/Ctrl-click exclusively select element under pointer > [end of test] number of renders 1`] = `26`;

exports[`regression tests > Cmd/Ctrl-click exclusively select element under pointer > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > Cmd/Ctrl-click exclusively select element under pointer > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id12",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id17",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id19": true,
          },
        },
        "inserted": {
          "editingGroupId": "id12",
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id19": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 60,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id21",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id19": true,
          },
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id24",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id19": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id27",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id28": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
              "id28",
            ],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
        "id19": {
          "deleted": {
            "groupIds": [
              "id28",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
              "id28",
            ],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
      },
    },
    "id": "id30",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id12",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id19": true,
            "id3": true,
          },
          "selectedGroupIds": {
            "id28": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id33",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id28",
          "selectedElementIds": {
            "id19": true,
          },
        },
        "inserted": {
          "editingGroupId": "id12",
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id36",
  },
]
`;

exports[`regression tests > Drags selected element when hitting only bounding box and keeps element selected > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > Drags selected element when hitting only bounding box and keeps element selected > [end of test] number of elements 1`] = `0`;

exports[`regression tests > Drags selected element when hitting only bounding box and keeps element selected > [end of test] number of renders 1`] = `10`;

exports[`regression tests > Drags selected element when hitting only bounding box and keeps element selected > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > Drags selected element when hitting only bounding box and keeps element selected > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 4,
            "x": 25,
            "y": 25,
          },
          "inserted": {
            "version": 3,
            "x": 0,
            "y": 0,
          },
        },
      },
    },
    "id": "id5",
  },
]
`;

exports[`regression tests > adjusts z order when grouping > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id6": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id15": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > adjusts z order when grouping > [end of test] number of elements 1`] = `0`;

exports[`regression tests > adjusts z order when grouping > [end of test] number of renders 1`] = `15`;

exports[`regression tests > adjusts z order when grouping > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > adjusts z order when grouping > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id15": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id15",
            ],
            "index": "a2",
            "version": 5,
          },
          "inserted": {
            "groupIds": [],
            "index": "a0",
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id15",
            ],
            "index": "a3",
            "version": 5,
          },
          "inserted": {
            "groupIds": [],
            "index": "a2",
            "version": 3,
          },
        },
      },
    },
    "id": "id17",
  },
]
`;

exports[`regression tests > alt-drag duplicates an element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id4": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > alt-drag duplicates an element > [end of test] number of elements 1`] = `0`;

exports[`regression tests > alt-drag duplicates an element > [end of test] number of renders 1`] = `8`;

exports[`regression tests > alt-drag duplicates an element > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > alt-drag duplicates an element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id4": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id4": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 6,
            "width": 10,
            "x": 20,
            "y": 20,
          },
          "inserted": {
            "isDeleted": true,
            "version": 5,
          },
        },
      },
      "updated": {
        "id0": {
          "deleted": {
            "version": 5,
          },
          "inserted": {
            "version": 3,
          },
        },
      },
    },
    "id": "id6",
  },
]
`;

exports[`regression tests > arrow keys > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > arrow keys > [end of test] number of elements 1`] = `0`;

exports[`regression tests > arrow keys > [end of test] number of renders 1`] = `12`;

exports[`regression tests > arrow keys > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > arrow keys > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > can drag element that covers another element, while another elem is selected > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > can drag element that covers another element, while another elem is selected > [end of test] number of elements 1`] = `0`;

exports[`regression tests > can drag element that covers another element, while another elem is selected > [end of test] number of renders 1`] = `15`;

exports[`regression tests > can drag element that covers another element, while another elem is selected > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > can drag element that covers another element, while another elem is selected > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 200,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 200,
            "x": 100,
            "y": 100,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 200,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 200,
            "x": 100,
            "y": 100,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 350,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 350,
            "x": 300,
            "y": 300,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "version": 4,
            "x": 300,
            "y": 300,
          },
          "inserted": {
            "version": 3,
            "x": 100,
            "y": 100,
          },
        },
      },
    },
    "id": "id11",
  },
]
`;

exports[`regression tests > change the properties of a shape > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "#ffc9c9",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1971c2",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": "elementStroke",
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > change the properties of a shape > [end of test] number of elements 1`] = `0`;

exports[`regression tests > change the properties of a shape > [end of test] number of renders 1`] = `9`;

exports[`regression tests > change the properties of a shape > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > change the properties of a shape > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "backgroundColor": "#ffec99",
            "version": 4,
          },
          "inserted": {
            "backgroundColor": "transparent",
            "version": 3,
          },
        },
      },
    },
    "id": "id4",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "backgroundColor": "#ffc9c9",
            "version": 5,
          },
          "inserted": {
            "backgroundColor": "#ffec99",
            "version": 4,
          },
        },
      },
    },
    "id": "id6",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "strokeColor": "#1971c2",
            "version": 6,
          },
          "inserted": {
            "strokeColor": "#1e1e1e",
            "version": 5,
          },
        },
      },
    },
    "id": "id8",
  },
]
`;

exports[`regression tests > click on an element and drag it > [dragged] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > click on an element and drag it > [dragged] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 1014066025,
  "width": 10,
  "x": 20,
  "y": 20,
}
`;

exports[`regression tests > click on an element and drag it > [dragged] number of elements 1`] = `1`;

exports[`regression tests > click on an element and drag it > [dragged] number of renders 1`] = `8`;

exports[`regression tests > click on an element and drag it > [dragged] redo stack 1`] = `[]`;

exports[`regression tests > click on an element and drag it > [dragged] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 4,
            "x": 20,
            "y": 20,
          },
          "inserted": {
            "version": 3,
            "x": 10,
            "y": 10,
          },
        },
      },
    },
    "id": "id5",
  },
]
`;

exports[`regression tests > click on an element and drag it > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > click on an element and drag it > [end of test] number of elements 1`] = `0`;

exports[`regression tests > click on an element and drag it > [end of test] number of renders 1`] = `10`;

exports[`regression tests > click on an element and drag it > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > click on an element and drag it > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 4,
            "x": 20,
            "y": 20,
          },
          "inserted": {
            "version": 3,
            "x": 10,
            "y": 10,
          },
        },
      },
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 5,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "version": 4,
            "x": 20,
            "y": 20,
          },
        },
      },
    },
    "id": "id8",
  },
]
`;

exports[`regression tests > click to select a shape > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > click to select a shape > [end of test] number of elements 1`] = `0`;

exports[`regression tests > click to select a shape > [end of test] number of renders 1`] = `10`;

exports[`regression tests > click to select a shape > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > click to select a shape > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
]
`;

exports[`regression tests > click-drag to select a group > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > click-drag to select a group > [end of test] number of elements 1`] = `0`;

exports[`regression tests > click-drag to select a group > [end of test] number of renders 1`] = `14`;

exports[`regression tests > click-drag to select a group > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > click-drag to select a group > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
]
`;

exports[`regression tests > deleting last but one element in editing group should unselect the group > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id12": false,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > deleting last but one element in editing group should unselect the group > [end of test] number of elements 1`] = `0`;

exports[`regression tests > deleting last but one element in editing group should unselect the group > [end of test] number of renders 1`] = `17`;

exports[`regression tests > deleting last but one element in editing group should unselect the group > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > deleting last but one element in editing group should unselect the group > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id12",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id16",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": false,
          },
        },
        "inserted": {
          "editingGroupId": "id12",
          "selectedElementIds": {
            "id0": true,
          },
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {
        "id0": {
          "deleted": {
            "isDeleted": true,
            "version": 5,
          },
          "inserted": {
            "isDeleted": false,
            "version": 4,
          },
        },
      },
      "removed": {},
      "updated": {},
    },
    "id": "id18",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": false,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id21",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": false,
          },
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id24",
  },
]
`;

exports[`regression tests > deselects group of selected elements on pointer down when pointer doesn't hit any element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "down",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": {
    "angle": 0,
    "backgroundColor": "transparent",
    "boundElements": null,
    "customData": undefined,
    "fillStyle": "solid",
    "frameId": null,
    "groupIds": [],
    "height": 0,
    "id": "id9",
    "index": null,
    "isDeleted": false,
    "link": null,
    "locked": false,
    "opacity": 100,
    "roughness": 1,
    "roundness": null,
    "seed": 493213705,
    "strokeColor": "#1e1e1e",
    "strokeStyle": "solid",
    "strokeWidth": 2,
    "type": "selection",
    "updated": 1,
    "version": 1,
    "versionNonce": 0,
    "width": 0,
    "x": 500,
    "y": 500,
  },
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > deselects group of selected elements on pointer down when pointer doesn't hit any element > [end of test] number of elements 1`] = `0`;

exports[`regression tests > deselects group of selected elements on pointer down when pointer doesn't hit any element > [end of test] number of renders 1`] = `11`;

exports[`regression tests > deselects group of selected elements on pointer down when pointer doesn't hit any element > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > deselects group of selected elements on pointer down when pointer doesn't hit any element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 110,
            "y": 110,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
]
`;

exports[`regression tests > deselects group of selected elements on pointer up when pointer hits common bounding box without hitting any element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > deselects group of selected elements on pointer up when pointer hits common bounding box without hitting any element > [end of test] number of elements 1`] = `0`;

exports[`regression tests > deselects group of selected elements on pointer up when pointer hits common bounding box without hitting any element > [end of test] number of renders 1`] = `11`;

exports[`regression tests > deselects group of selected elements on pointer up when pointer hits common bounding box without hitting any element > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > deselects group of selected elements on pointer up when pointer hits common bounding box without hitting any element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 110,
            "y": 110,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
]
`;

exports[`regression tests > deselects selected element on pointer down when pointer doesn't hit any element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "down",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": {
    "angle": 0,
    "backgroundColor": "transparent",
    "boundElements": null,
    "customData": undefined,
    "fillStyle": "solid",
    "frameId": null,
    "groupIds": [],
    "height": 0,
    "id": "id3",
    "index": null,
    "isDeleted": false,
    "link": null,
    "locked": false,
    "opacity": 100,
    "roughness": 1,
    "roundness": null,
    "seed": 1116226695,
    "strokeColor": "#1e1e1e",
    "strokeStyle": "solid",
    "strokeWidth": 2,
    "type": "selection",
    "updated": 1,
    "version": 1,
    "versionNonce": 0,
    "width": 0,
    "x": 110,
    "y": 110,
  },
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > deselects selected element on pointer down when pointer doesn't hit any element > [end of test] number of elements 1`] = `0`;

exports[`regression tests > deselects selected element on pointer down when pointer doesn't hit any element > [end of test] number of renders 1`] = `7`;

exports[`regression tests > deselects selected element on pointer down when pointer doesn't hit any element > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > deselects selected element on pointer down when pointer doesn't hit any element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > deselects selected element, on pointer up, when click hits element bounding box but doesn't hit the element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > deselects selected element, on pointer up, when click hits element bounding box but doesn't hit the element > [end of test] number of elements 1`] = `0`;

exports[`regression tests > deselects selected element, on pointer up, when click hits element bounding box but doesn't hit the element > [end of test] number of renders 1`] = `7`;

exports[`regression tests > deselects selected element, on pointer up, when click hits element bounding box but doesn't hit the element > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > deselects selected element, on pointer up, when click hits element bounding box but doesn't hit the element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 100,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id5",
  },
]
`;

exports[`regression tests > double click to edit a group > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": "id11",
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id6": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > double click to edit a group > [end of test] number of elements 1`] = `0`;

exports[`regression tests > double click to edit a group > [end of test] number of renders 1`] = `15`;

exports[`regression tests > double click to edit a group > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > double click to edit a group > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id10",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id11": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id11",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id11",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id11",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id13",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id11",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
          "selectedGroupIds": {
            "id11": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id15",
  },
]
`;

exports[`regression tests > drags selected elements from point inside common bounding box that doesn't hit any element and keeps elements selected after dragging > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > drags selected elements from point inside common bounding box that doesn't hit any element and keeps elements selected after dragging > [end of test] number of elements 1`] = `0`;

exports[`regression tests > drags selected elements from point inside common bounding box that doesn't hit any element and keeps elements selected after dragging > [end of test] number of renders 1`] = `12`;

exports[`regression tests > drags selected elements from point inside common bounding box that doesn't hit any element and keeps elements selected after dragging > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > drags selected elements from point inside common bounding box that doesn't hit any element and keeps elements selected after dragging > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 110,
            "y": 110,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 4,
            "x": 25,
            "y": 25,
          },
          "inserted": {
            "version": 3,
            "x": 0,
            "y": 0,
          },
        },
        "id3": {
          "deleted": {
            "version": 4,
            "x": 135,
            "y": 135,
          },
          "inserted": {
            "version": 3,
            "x": 110,
            "y": 110,
          },
        },
      },
    },
    "id": "id11",
  },
]
`;

exports[`regression tests > draw every type of shape > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "freedraw",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > draw every type of shape > [end of test] number of elements 1`] = `0`;

exports[`regression tests > draw every type of shape > [end of test] number of renders 1`] = `31`;

exports[`regression tests > draw every type of shape > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > draw every type of shape > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 10,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "diamond",
            "version": 3,
            "width": 20,
            "x": 40,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 20,
            "x": 70,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id9": true,
          },
          "selectedLinearElementId": "id9",
        },
        "inserted": {
          "selectedElementIds": {
            "id6": true,
          },
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id9": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "elbowed": false,
            "endArrowhead": "arrow",
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a3",
            "isDeleted": false,
            "lastCommittedPoint": null,
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
            ],
            "roughness": 1,
            "roundness": {
              "type": 2,
            },
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "arrow",
            "version": 4,
            "width": 50,
            "x": 130,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id12": true,
          },
          "selectedLinearElementId": "id12",
        },
        "inserted": {
          "selectedElementIds": {
            "id9": true,
          },
          "selectedLinearElementId": "id9",
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id12": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "endArrowhead": null,
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a4",
            "isDeleted": false,
            "lastCommittedPoint": null,
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
            ],
            "polygon": false,
            "roughness": 1,
            "roundness": null,
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "line",
            "version": 4,
            "width": 50,
            "x": 220,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id15": true,
          },
          "selectedLinearElementId": null,
        },
        "inserted": {
          "selectedElementIds": {
            "id12": true,
          },
          "selectedLinearElementId": "id12",
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id15": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "elbowed": false,
            "endArrowhead": "arrow",
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a5",
            "isDeleted": false,
            "lastCommittedPoint": [
              50,
              10,
            ],
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
            ],
            "roughness": 1,
            "roundness": {
              "type": 2,
            },
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "arrow",
            "version": 6,
            "width": 50,
            "x": 310,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 5,
          },
        },
      },
      "updated": {},
    },
    "id": "id17",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id15": {
          "deleted": {
            "height": 20,
            "lastCommittedPoint": [
              80,
              20,
            ],
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
              [
                80,
                20,
              ],
            ],
            "version": 8,
            "width": 80,
          },
          "inserted": {
            "height": 10,
            "lastCommittedPoint": [
              50,
              10,
            ],
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
            ],
            "version": 6,
            "width": 50,
          },
        },
      },
    },
    "id": "id19",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedLinearElementId": "id15",
        },
        "inserted": {
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id21",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id22": true,
          },
          "selectedLinearElementId": null,
        },
        "inserted": {
          "selectedElementIds": {
            "id15": true,
          },
          "selectedLinearElementId": "id15",
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id22": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "endArrowhead": null,
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a6",
            "isDeleted": false,
            "lastCommittedPoint": [
              50,
              10,
            ],
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
            ],
            "polygon": false,
            "roughness": 1,
            "roundness": null,
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "line",
            "version": 6,
            "width": 50,
            "x": 430,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 5,
          },
        },
      },
      "updated": {},
    },
    "id": "id24",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id22": {
          "deleted": {
            "height": 20,
            "lastCommittedPoint": [
              80,
              20,
            ],
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
              [
                80,
                20,
              ],
            ],
            "version": 8,
            "width": 80,
          },
          "inserted": {
            "height": 10,
            "lastCommittedPoint": [
              50,
              10,
            ],
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
            ],
            "version": 6,
            "width": 50,
          },
        },
      },
    },
    "id": "id26",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedLinearElementId": "id22",
        },
        "inserted": {
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id28",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id22": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id30",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedLinearElementId": null,
        },
        "inserted": {
          "selectedLinearElementId": "id22",
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id31": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a7",
            "isDeleted": false,
            "lastCommittedPoint": [
              50,
              10,
            ],
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                50,
                10,
              ],
              [
                50,
                10,
              ],
            ],
            "pressures": [
              0,
              0,
              0,
            ],
            "roughness": 1,
            "roundness": null,
            "simulatePressure": false,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "freedraw",
            "version": 4,
            "width": 50,
            "x": 550,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id33",
  },
]
`;

exports[`regression tests > given a group of selected elements with an element that is not selected inside the group common bounding box when element that is not selected is clicked should switch selection to not selected element on pointer up > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > given a group of selected elements with an element that is not selected inside the group common bounding box when element that is not selected is clicked should switch selection to not selected element on pointer up > [end of test] number of elements 1`] = `0`;

exports[`regression tests > given a group of selected elements with an element that is not selected inside the group common bounding box when element that is not selected is clicked should switch selection to not selected element on pointer up > [end of test] number of renders 1`] = `14`;

exports[`regression tests > given a group of selected elements with an element that is not selected inside the group common bounding box when element that is not selected is clicked should switch selection to not selected element on pointer up > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > given a group of selected elements with an element that is not selected inside the group common bounding box when element that is not selected is clicked should switch selection to not selected element on pointer up > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 100,
            "x": 110,
            "y": 110,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "diamond",
            "version": 3,
            "width": 100,
            "x": 310,
            "y": 310,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
            "id6": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id14",
  },
]
`;

exports[`regression tests > given a selected element A and a not selected element B with higher z-index than A and given B partially overlaps A when there's a shift-click on the overlapped section B is added to the selection > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "#ffc9c9",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": "elementBackground",
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > given a selected element A and a not selected element B with higher z-index than A and given B partially overlaps A when there's a shift-click on the overlapped section B is added to the selection > [end of test] number of elements 1`] = `0`;

exports[`regression tests > given a selected element A and a not selected element B with higher z-index than A and given B partially overlaps A when there's a shift-click on the overlapped section B is added to the selection > [end of test] number of renders 1`] = `12`;

exports[`regression tests > given a selected element A and a not selected element B with higher z-index than A and given B partially overlaps A when there's a shift-click on the overlapped section B is added to the selection > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > given a selected element A and a not selected element B with higher z-index than A and given B partially overlaps A when there's a shift-click on the overlapped section B is added to the selection > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "#ffc9c9",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 1000,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 1000,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "#ffc9c9",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 1000,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 1000,
            "x": 500,
            "y": 500,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
]
`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when clicking intersection between A and B B should be selected on pointer up > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id1": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when clicking intersection between A and B B should be selected on pointer up > [end of test] number of elements 1`] = `0`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when clicking intersection between A and B B should be selected on pointer up > [end of test] number of renders 1`] = `7`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when clicking intersection between A and B B should be selected on pointer up > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when clicking intersection between A and B B should be selected on pointer up > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "red",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 1000,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 2,
            "width": 1000,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 1,
          },
        },
        "id1": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "red",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 500,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 2,
            "width": 500,
            "x": 500,
            "y": 500,
          },
          "inserted": {
            "isDeleted": true,
            "version": 1,
          },
        },
      },
      "updated": {},
    },
    "id": "id4",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id1": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id7",
  },
]
`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when dragging on intersection between A and B A should be dragged and keep being selected > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when dragging on intersection between A and B A should be dragged and keep being selected > [end of test] number of elements 1`] = `0`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when dragging on intersection between A and B A should be dragged and keep being selected > [end of test] number of renders 1`] = `8`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when dragging on intersection between A and B A should be dragged and keep being selected > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > given selected element A with lower z-index than unselected element B and given B is partially over A when dragging on intersection between A and B A should be dragged and keep being selected > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "red",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 1000,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 2,
            "width": 1000,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 1,
          },
        },
        "id1": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "red",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 500,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 2,
            "width": 500,
            "x": 500,
            "y": 500,
          },
          "inserted": {
            "isDeleted": true,
            "version": 1,
          },
        },
      },
      "updated": {},
    },
    "id": "id4",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 3,
            "x": 100,
            "y": 100,
          },
          "inserted": {
            "version": 2,
            "x": 0,
            "y": 0,
          },
        },
      },
    },
    "id": "id7",
  },
]
`;

exports[`regression tests > key 2 selects rectangle tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key 2 selects rectangle tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key 2 selects rectangle tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key 2 selects rectangle tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key 2 selects rectangle tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key 3 selects diamond tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key 3 selects diamond tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key 3 selects diamond tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key 3 selects diamond tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key 3 selects diamond tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "diamond",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key 4 selects ellipse tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key 4 selects ellipse tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key 4 selects ellipse tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key 4 selects ellipse tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key 4 selects ellipse tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key 5 selects arrow tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": LinearElementEditor {
    "customLineAngle": null,
    "elbowed": false,
    "elementId": "id0",
    "endBindingElement": "keep",
    "hoverPointIndex": -1,
    "isDragging": false,
    "lastUncommittedPoint": null,
    "pointerDownState": {
      "lastClickedIsEndPoint": false,
      "lastClickedPoint": -1,
      "origin": null,
      "prevSelectedPointsIndices": null,
      "segmentMidpoint": {
        "added": false,
        "index": null,
        "value": null,
      },
    },
    "pointerOffset": {
      "x": 0,
      "y": 0,
    },
    "segmentMidPointHoveredCoords": null,
    "selectedPointsIndices": null,
    "startBindingElement": "keep",
  },
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key 5 selects arrow tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key 5 selects arrow tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key 5 selects arrow tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key 5 selects arrow tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
          "selectedLinearElementId": "id0",
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "elbowed": false,
            "endArrowhead": "arrow",
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "lastCommittedPoint": null,
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                10,
                10,
              ],
            ],
            "roughness": 1,
            "roundness": {
              "type": 2,
            },
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "arrow",
            "version": 4,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key 6 selects line tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": LinearElementEditor {
    "customLineAngle": null,
    "elbowed": false,
    "elementId": "id0",
    "endBindingElement": "keep",
    "hoverPointIndex": -1,
    "isDragging": false,
    "lastUncommittedPoint": null,
    "pointerDownState": {
      "lastClickedIsEndPoint": false,
      "lastClickedPoint": -1,
      "origin": null,
      "prevSelectedPointsIndices": null,
      "segmentMidpoint": {
        "added": false,
        "index": null,
        "value": null,
      },
    },
    "pointerOffset": {
      "x": 0,
      "y": 0,
    },
    "segmentMidPointHoveredCoords": null,
    "selectedPointsIndices": null,
    "startBindingElement": "keep",
  },
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key 6 selects line tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key 6 selects line tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key 6 selects line tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key 6 selects line tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
          "selectedLinearElementId": "id0",
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "endArrowhead": null,
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "lastCommittedPoint": null,
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                10,
                10,
              ],
            ],
            "polygon": false,
            "roughness": 1,
            "roundness": null,
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "line",
            "version": 4,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key 7 selects freedraw tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "freedraw",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key 7 selects freedraw tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key 7 selects freedraw tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key 7 selects freedraw tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key 7 selects freedraw tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "lastCommittedPoint": [
              10,
              10,
            ],
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                10,
                10,
              ],
              [
                10,
                10,
              ],
            ],
            "pressures": [
              0,
              0,
              0,
            ],
            "roughness": 1,
            "roundness": null,
            "simulatePressure": false,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "freedraw",
            "version": 4,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key a selects arrow tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": LinearElementEditor {
    "customLineAngle": null,
    "elbowed": false,
    "elementId": "id0",
    "endBindingElement": "keep",
    "hoverPointIndex": -1,
    "isDragging": false,
    "lastUncommittedPoint": null,
    "pointerDownState": {
      "lastClickedIsEndPoint": false,
      "lastClickedPoint": -1,
      "origin": null,
      "prevSelectedPointsIndices": null,
      "segmentMidpoint": {
        "added": false,
        "index": null,
        "value": null,
      },
    },
    "pointerOffset": {
      "x": 0,
      "y": 0,
    },
    "segmentMidPointHoveredCoords": null,
    "selectedPointsIndices": null,
    "startBindingElement": "keep",
  },
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key a selects arrow tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key a selects arrow tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key a selects arrow tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key a selects arrow tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
          "selectedLinearElementId": "id0",
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "elbowed": false,
            "endArrowhead": "arrow",
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "lastCommittedPoint": null,
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                10,
                10,
              ],
            ],
            "roughness": 1,
            "roundness": {
              "type": 2,
            },
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "arrow",
            "version": 4,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key d selects diamond tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key d selects diamond tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key d selects diamond tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key d selects diamond tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key d selects diamond tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "diamond",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key l selects line tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": LinearElementEditor {
    "customLineAngle": null,
    "elbowed": false,
    "elementId": "id0",
    "endBindingElement": "keep",
    "hoverPointIndex": -1,
    "isDragging": false,
    "lastUncommittedPoint": null,
    "pointerDownState": {
      "lastClickedIsEndPoint": false,
      "lastClickedPoint": -1,
      "origin": null,
      "prevSelectedPointsIndices": null,
      "segmentMidpoint": {
        "added": false,
        "index": null,
        "value": null,
      },
    },
    "pointerOffset": {
      "x": 0,
      "y": 0,
    },
    "segmentMidPointHoveredCoords": null,
    "selectedPointsIndices": null,
    "startBindingElement": "keep",
  },
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key l selects line tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key l selects line tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key l selects line tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key l selects line tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
          "selectedLinearElementId": "id0",
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedLinearElementId": null,
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "endArrowhead": null,
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "lastCommittedPoint": null,
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                10,
                10,
              ],
            ],
            "polygon": false,
            "roughness": 1,
            "roundness": null,
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "line",
            "version": 4,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key o selects ellipse tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key o selects ellipse tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key o selects ellipse tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key o selects ellipse tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key o selects ellipse tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key p selects freedraw tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "freedraw",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key p selects freedraw tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key p selects freedraw tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key p selects freedraw tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key p selects freedraw tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "lastCommittedPoint": [
              10,
              10,
            ],
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                10,
                10,
              ],
              [
                10,
                10,
              ],
            ],
            "pressures": [
              0,
              0,
              0,
            ],
            "roughness": 1,
            "roundness": null,
            "simulatePressure": false,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "freedraw",
            "version": 4,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 3,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > key r selects rectangle tool > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > key r selects rectangle tool > [end of test] number of elements 1`] = `0`;

exports[`regression tests > key r selects rectangle tool > [end of test] number of renders 1`] = `6`;

exports[`regression tests > key r selects rectangle tool > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > key r selects rectangle tool > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`regression tests > make a group and duplicate it > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id16": true,
    "id18": true,
    "id19": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id17": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > make a group and duplicate it > [end of test] number of elements 1`] = `0`;

exports[`regression tests > make a group and duplicate it > [end of test] number of renders 1`] = `17`;

exports[`regression tests > make a group and duplicate it > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > make a group and duplicate it > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id16": true,
            "id18": true,
            "id19": true,
          },
          "selectedGroupIds": {
            "id17": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
            "id6": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id16": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [
              "id17",
            ],
            "height": 10,
            "index": "a3",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 7,
            "width": 10,
            "x": 20,
            "y": 20,
          },
          "inserted": {
            "isDeleted": true,
            "version": 6,
          },
        },
        "id18": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [
              "id17",
            ],
            "height": 10,
            "index": "a4",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 7,
            "width": 10,
            "x": 40,
            "y": 20,
          },
          "inserted": {
            "isDeleted": true,
            "version": 6,
          },
        },
        "id19": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [
              "id17",
            ],
            "height": 10,
            "index": "a5",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 7,
            "width": 10,
            "x": 60,
            "y": 20,
          },
          "inserted": {
            "isDeleted": true,
            "version": 6,
          },
        },
      },
      "updated": {
        "id0": {
          "deleted": {
            "version": 6,
          },
          "inserted": {
            "version": 4,
          },
        },
        "id3": {
          "deleted": {
            "version": 6,
          },
          "inserted": {
            "version": 4,
          },
        },
        "id6": {
          "deleted": {
            "version": 6,
          },
          "inserted": {
            "version": 4,
          },
        },
      },
    },
    "id": "id21",
  },
]
`;

exports[`regression tests > noop interaction after undo shouldn't create history entry > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > noop interaction after undo shouldn't create history entry > [end of test] number of elements 1`] = `0`;

exports[`regression tests > noop interaction after undo shouldn't create history entry > [end of test] number of renders 1`] = `14`;

exports[`regression tests > noop interaction after undo shouldn't create history entry > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > noop interaction after undo shouldn't create history entry > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 5,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 4,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id14",
  },
]
`;

exports[`regression tests > pinch-to-zoom works > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "down",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "touch",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": "-6.25000",
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": true,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > pinch-to-zoom works > [end of test] number of elements 1`] = `0`;

exports[`regression tests > pinch-to-zoom works > [end of test] number of renders 1`] = `7`;

exports[`regression tests > pinch-to-zoom works > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > pinch-to-zoom works > [end of test] undo stack 1`] = `[]`;

exports[`regression tests > shift click on selected element should deselect it on pointer up > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > shift click on selected element should deselect it on pointer up > [end of test] number of elements 1`] = `0`;

exports[`regression tests > shift click on selected element should deselect it on pointer up > [end of test] number of renders 1`] = `7`;

exports[`regression tests > shift click on selected element should deselect it on pointer up > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > shift click on selected element should deselect it on pointer up > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id5",
  },
]
`;

exports[`regression tests > shift-click to multiselect, then drag > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > shift-click to multiselect, then drag > [end of test] number of elements 1`] = `0`;

exports[`regression tests > shift-click to multiselect, then drag > [end of test] number of renders 1`] = `13`;

exports[`regression tests > shift-click to multiselect, then drag > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > shift-click to multiselect, then drag > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "version": 4,
            "x": 20,
            "y": 20,
          },
          "inserted": {
            "version": 3,
            "x": 10,
            "y": 10,
          },
        },
        "id3": {
          "deleted": {
            "version": 4,
            "x": 40,
            "y": 20,
          },
          "inserted": {
            "version": 3,
            "x": 30,
            "y": 10,
          },
        },
      },
    },
    "id": "id14",
  },
]
`;

exports[`regression tests > should group elements and ungroup them > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id3": true,
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
    "id6": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > should group elements and ungroup them > [end of test] number of elements 1`] = `0`;

exports[`regression tests > should group elements and ungroup them > [end of test] number of renders 1`] = `18`;

exports[`regression tests > should group elements and ungroup them > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > should group elements and ungroup them > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 30,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {},
        },
        "inserted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
      },
    },
    "id": "id17",
  },
]
`;

exports[`regression tests > single-clicking on a subgroup of a selected group should not alter selection > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
    "id15": true,
    "id18": true,
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id15": true,
    "id18": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id32": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > single-clicking on a subgroup of a selected group should not alter selection > [end of test] number of elements 1`] = `0`;

exports[`regression tests > single-clicking on a subgroup of a selected group should not alter selection > [end of test] number of renders 1`] = `25`;

exports[`regression tests > single-clicking on a subgroup of a selected group should not alter selection > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > single-clicking on a subgroup of a selected group should not alter selection > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id15": true,
          },
          "selectedGroupIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id15": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 10,
            "y": 50,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id17",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id18": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id15": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id18": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a3",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 50,
            "y": 50,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id20",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id15": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id18": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id23",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id18": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id26",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id27": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id15": {
          "deleted": {
            "groupIds": [
              "id27",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id18": {
          "deleted": {
            "groupIds": [
              "id27",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id29",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id31",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id32": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {
            "id12": true,
            "id27": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
              "id32",
            ],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
        "id15": {
          "deleted": {
            "groupIds": [
              "id27",
              "id32",
            ],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id27",
            ],
            "version": 4,
          },
        },
        "id18": {
          "deleted": {
            "groupIds": [
              "id27",
              "id32",
            ],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id27",
            ],
            "version": 4,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
              "id32",
            ],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
        },
      },
    },
    "id": "id34",
  },
]
`;

exports[`regression tests > spacebar + drag scrolls the canvas > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 60,
  "scrollY": 60,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > spacebar + drag scrolls the canvas > [end of test] number of elements 1`] = `0`;

exports[`regression tests > spacebar + drag scrolls the canvas > [end of test] number of renders 1`] = `5`;

exports[`regression tests > spacebar + drag scrolls the canvas > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > spacebar + drag scrolls the canvas > [end of test] undo stack 1`] = `[]`;

exports[`regression tests > supports nested groups > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": "id11",
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > supports nested groups > [end of test] number of elements 1`] = `0`;

exports[`regression tests > supports nested groups > [end of test] number of renders 1`] = `23`;

exports[`regression tests > supports nested groups > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > supports nested groups > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 50,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 50,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 50,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 50,
            "x": 100,
            "y": 100,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 50,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 50,
            "x": 200,
            "y": 200,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id10",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id11": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id11",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id11",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id11",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id13",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id11",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id0": true,
            "id3": true,
          },
          "selectedGroupIds": {
            "id11": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id15",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id18",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id19": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id19",
              "id11",
            ],
            "index": "a2",
            "version": 6,
          },
          "inserted": {
            "groupIds": [
              "id11",
            ],
            "index": "a0",
            "version": 4,
          },
        },
        "id6": {
          "deleted": {
            "groupIds": [
              "id19",
              "id11",
            ],
            "index": "a3",
            "version": 6,
          },
          "inserted": {
            "groupIds": [
              "id11",
            ],
            "index": "a2",
            "version": 4,
          },
        },
      },
    },
    "id": "id21",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": null,
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": "id11",
          "selectedElementIds": {
            "id0": true,
            "id6": true,
          },
          "selectedGroupIds": {
            "id19": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id24",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
            "id3": true,
            "id6": true,
          },
          "selectedGroupIds": {
            "id11": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id27",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id11",
          "selectedElementIds": {},
          "selectedGroupIds": {
            "id19": true,
          },
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id3": true,
          },
          "selectedGroupIds": {
            "id11": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id29",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id19",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": "id11",
          "selectedElementIds": {
            "id6": true,
          },
          "selectedGroupIds": {
            "id19": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id31",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id3": true,
            "id6": true,
          },
          "selectedGroupIds": {
            "id11": true,
          },
        },
        "inserted": {
          "editingGroupId": "id19",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id34",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "editingGroupId": "id11",
          "selectedElementIds": {},
          "selectedGroupIds": {},
        },
        "inserted": {
          "editingGroupId": null,
          "selectedElementIds": {
            "id0": true,
            "id6": true,
          },
          "selectedGroupIds": {
            "id11": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id36",
  },
]
`;

exports[`regression tests > switches from group of selected elements to another element on pointer down > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "down",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id3": true,
    "id6": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": {
    "angle": 0,
    "backgroundColor": "transparent",
    "boundElements": null,
    "customData": undefined,
    "fillStyle": "solid",
    "frameId": null,
    "groupIds": [],
    "height": 0,
    "id": "id12",
    "index": null,
    "isDeleted": false,
    "link": null,
    "locked": false,
    "opacity": 100,
    "roughness": 1,
    "roundness": null,
    "seed": 289600103,
    "strokeColor": "#1e1e1e",
    "strokeStyle": "solid",
    "strokeWidth": 2,
    "type": "selection",
    "updated": 1,
    "version": 1,
    "versionNonce": 0,
    "width": 0,
    "x": 0,
    "y": 0,
  },
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > switches from group of selected elements to another element on pointer down > [end of test] number of elements 1`] = `0`;

exports[`regression tests > switches from group of selected elements to another element on pointer down > [end of test] number of renders 1`] = `14`;

exports[`regression tests > switches from group of selected elements to another element on pointer down > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > switches from group of selected elements to another element on pointer down > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 100,
            "x": 110,
            "y": 110,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id6": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 100,
            "index": "a2",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "diamond",
            "version": 3,
            "width": 100,
            "x": 310,
            "y": 310,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
]
`;

exports[`regression tests > switches selected element on pointer down > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "down",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": {
    "angle": 0,
    "backgroundColor": "transparent",
    "boundElements": null,
    "customData": undefined,
    "fillStyle": "solid",
    "frameId": null,
    "groupIds": [],
    "height": 0,
    "id": "id6",
    "index": null,
    "isDeleted": false,
    "link": null,
    "locked": false,
    "opacity": 100,
    "roughness": 1,
    "roundness": null,
    "seed": 23633383,
    "strokeColor": "#1e1e1e",
    "strokeStyle": "solid",
    "strokeWidth": 2,
    "type": "selection",
    "updated": 1,
    "version": 1,
    "versionNonce": 0,
    "width": 0,
    "x": 0,
    "y": 0,
  },
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > switches selected element on pointer down > [end of test] number of elements 1`] = `0`;

exports[`regression tests > switches selected element on pointer down > [end of test] number of renders 1`] = `10`;

exports[`regression tests > switches selected element on pointer down > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > switches selected element on pointer down > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 0,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "ellipse",
            "version": 3,
            "width": 10,
            "x": 20,
            "y": 20,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
]
`;

exports[`regression tests > two-finger scroll works > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "down",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "touch",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 20,
  "scrollY": "-18.53553",
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": true,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > two-finger scroll works > [end of test] number of elements 1`] = `0`;

exports[`regression tests > two-finger scroll works > [end of test] number of renders 1`] = `8`;

exports[`regression tests > two-finger scroll works > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > two-finger scroll works > [end of test] undo stack 1`] = `[]`;

exports[`regression tests > undo/redo drawing an element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > undo/redo drawing an element > [end of test] number of elements 1`] = `0`;

exports[`regression tests > undo/redo drawing an element > [end of test] number of renders 1`] = `19`;

exports[`regression tests > undo/redo drawing an element > [end of test] redo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedLinearElementId": null,
        },
        "inserted": {
          "selectedLinearElementId": "id6",
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id13",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id6": {
          "deleted": {
            "height": 10,
            "lastCommittedPoint": [
              60,
              10,
            ],
            "points": [
              [
                0,
                0,
              ],
              [
                60,
                10,
              ],
            ],
            "version": 9,
            "width": 60,
          },
          "inserted": {
            "height": 20,
            "lastCommittedPoint": [
              100,
              20,
            ],
            "points": [
              [
                0,
                0,
              ],
              [
                60,
                10,
              ],
              [
                100,
                20,
              ],
            ],
            "version": 8,
            "width": 100,
          },
        },
      },
    },
    "id": "id14",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id6": true,
          },
        },
      },
    },
    "elements": {
      "added": {
        "id6": {
          "deleted": {
            "isDeleted": true,
            "version": 10,
          },
          "inserted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "elbowed": false,
            "endArrowhead": "arrow",
            "endBinding": null,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a2",
            "isDeleted": false,
            "lastCommittedPoint": [
              60,
              10,
            ],
            "link": null,
            "locked": false,
            "opacity": 100,
            "points": [
              [
                0,
                0,
              ],
              [
                60,
                10,
              ],
            ],
            "roughness": 1,
            "roundness": {
              "type": 2,
            },
            "startArrowhead": null,
            "startBinding": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "arrow",
            "version": 9,
            "width": 60,
            "x": 130,
            "y": 10,
          },
        },
      },
      "removed": {},
      "updated": {},
    },
    "id": "id15",
  },
]
`;

exports[`regression tests > undo/redo drawing an element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 10,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 5,
            "width": 30,
            "x": 40,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 4,
          },
        },
      },
      "updated": {},
    },
    "id": "id17",
  },
]
`;

exports[`regression tests > updates fontSize & fontFamily appState > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "text",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 8,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > updates fontSize & fontFamily appState > [end of test] number of elements 1`] = `0`;

exports[`regression tests > updates fontSize & fontFamily appState > [end of test] number of renders 1`] = `5`;

exports[`regression tests > updates fontSize & fontFamily appState > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > updates fontSize & fontFamily appState > [end of test] undo stack 1`] = `[]`;

exports[`regression tests > zoom hotkeys > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 768,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 0,
  "offsetTop": 0,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 1024,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`regression tests > zoom hotkeys > [end of test] number of elements 1`] = `0`;

exports[`regression tests > zoom hotkeys > [end of test] number of renders 1`] = `6`;

exports[`regression tests > zoom hotkeys > [end of test] redo stack 1`] = `[]`;

exports[`regression tests > zoom hotkeys > [end of test] undo stack 1`] = `[]`;
