#!/usr/bin/env node

import { config } from '../config/index.js';
import axios from 'axios';

async function debugApolloAPI() {
  console.log('🔍 Debugging Apollo.io API Access...\n');
  console.log('API Key:', config.apollo.apiKey);
  console.log('Base URL:', config.apollo.baseUrl);
  
  // Test different base URLs and authentication methods
  const baseUrls = [
    'https://api.apollo.io/v1',
    'https://api.apollo.io/api/v1',
    'https://api.apollo.io'
  ];

  const authMethods = [
    { name: 'x-api-key header', headers: { 'x-api-key': config.apollo.apiKey } },
    { name: 'Api-Key header', headers: { 'Api-Key': config.apollo.apiKey } },
    { name: 'X-Api-Key header', headers: { 'X-Api-Key': config.apollo.apiKey } },
    { name: 'Authorization Bearer', headers: { 'Authorization': `Bearer ${config.apollo.apiKey}` } },
    { name: 'Authorization Basic', headers: { 'Authorization': `Basic ${Buffer.from(config.apollo.apiKey + ':').toString('base64')}` } }
  ];

  // Test basic endpoints that should be available
  const testEndpoints = [
    '/auth/health',
    '/health',
    '/mixed_people/search',
    '/people/search',
    '/organizations/search',
    '/email_finder',
    '/email_verifier'
  ];

  for (const baseUrl of baseUrls) {
    console.log(`\n🌐 Testing Base URL: ${baseUrl}`);
    
    for (const authMethod of authMethods) {
      console.log(`\n  🔐 Testing Auth Method: ${authMethod.name}`);
      
      const client = axios.create({
        baseURL: baseUrl,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          ...authMethod.headers
        }
      });

      for (const endpoint of testEndpoints) {
        try {
          console.log(`    📡 Testing: ${endpoint}`);
          
          let response;
          if (endpoint.includes('search')) {
            // POST request for search endpoints
            response = await client.post(endpoint, {
              q_keywords: 'test',
              per_page: 1
            });
          } else {
            // GET request for other endpoints
            response = await client.get(endpoint);
          }
          
          console.log(`    ✅ SUCCESS: ${endpoint} - Status: ${response.status}`);
          console.log(`       Credits: ${response.headers['x-daily-requests-left'] || 'Unknown'}`);
          console.log(`       Rate Limit: ${response.headers['x-hourly-requests-left'] || 'Unknown'}`);
          
          if (response.data) {
            console.log(`       Response Keys: ${Object.keys(response.data).join(', ')}`);
          }
          
          // If we found a working combination, save it
          if (response.status === 200) {
            console.log(`\n🎉 WORKING COMBINATION FOUND!`);
            console.log(`   Base URL: ${baseUrl}`);
            console.log(`   Auth Method: ${authMethod.name}`);
            console.log(`   Endpoint: ${endpoint}`);
            
            // Test a real search if this is a search endpoint
            if (endpoint.includes('search')) {
              console.log(`\n   Testing real search...`);
              try {
                const realSearch = await client.post(endpoint, {
                  q_keywords: 'headteacher',
                  person_locations: ['United Kingdom'],
                  per_page: 5
                });
                console.log(`   ✅ Real search successful! Found ${realSearch.data.pagination?.total_entries || realSearch.data.people?.length || 0} results`);
                
                if (realSearch.data.people && realSearch.data.people.length > 0) {
                  const sample = realSearch.data.people[0];
                  console.log(`   Sample: ${sample.name || 'N/A'} - ${sample.title || 'N/A'} at ${sample.organization?.name || 'N/A'}`);
                }
              } catch (searchError) {
                console.log(`   ❌ Real search failed: ${searchError.response?.status} - ${searchError.response?.data?.error || searchError.message}`);
              }
            }
            
            return {
              baseUrl,
              authMethod: authMethod.name,
              headers: authMethod.headers,
              workingEndpoint: endpoint
            };
          }
          
        } catch (error) {
          const status = error.response?.status || 'Network Error';
          const message = error.response?.data?.error || error.response?.data?.message || error.message;
          console.log(`    ❌ FAILED: ${endpoint} - ${status}: ${message}`);
        }
        
        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
  }

  console.log('\n❌ No working combination found.');
  console.log('\nPossible issues:');
  console.log('1. API key might be invalid or expired');
  console.log('2. API key might not have the required permissions');
  console.log('3. Account might not be on the right plan');
  console.log('4. API endpoints might have changed');
  
  return null;
}

// Run the debug
debugApolloAPI().then(result => {
  if (result) {
    console.log('\n✅ Debug completed successfully!');
  } else {
    console.log('\n❌ Debug completed - no working configuration found');
  }
}).catch(error => {
  console.error('❌ Debug failed:', error);
  process.exit(1);
});
