#!/usr/bin/env node

import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WebServer {
  constructor() {
    this.app = express();
    this.port = config.port || 3000;
    this.webDir = path.join(__dirname, '../web');
    this.dataDir = path.join(__dirname, '../data');
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // Serve static files from web directory
    this.app.use(express.static(this.webDir));
    
    // Parse JSON bodies
    this.app.use(express.json());
    
    // CORS for development
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      next();
    });

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.url}`);
      next();
    });
  }

  setupRoutes() {
    // Main dashboard route
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(this.webDir, 'index.html'));
    });

    // API route to get school data
    this.app.get('/api/school-data', async (req, res) => {
      try {
        const dataPath = path.join(this.webDir, 'data', 'school_data.json');
        
        try {
          const data = await fs.readFile(dataPath, 'utf8');
          res.json(JSON.parse(data));
        } catch (error) {
          // If no data file exists, return empty structure
          res.json({
            lastUpdated: null,
            stats: {
              total_schools_processed: 0,
              total_contacts_found: 0,
              total_accounts_found: 0,
              total_email_suggestions: 0,
              api_calls_used: 0,
              success_rate: 0
            },
            schools: [],
            contacts: [],
            accounts: [],
            emailSuggestions: []
          });
        }
      } catch (error) {
        logger.error('Error serving school data:', error);
        res.status(500).json({ error: 'Failed to load school data' });
      }
    });

    // API route to trigger data collection
    this.app.post('/api/collect-data', async (req, res) => {
      try {
        logger.info('🚀 Starting data collection via API...');
        
        // Import and run the data collection script
        const { default: collectData } = await import('./scripts/collectRealSchoolData.js');
        
        // This would run the actual collection
        // For now, we'll return a success response
        res.json({
          success: true,
          message: 'Data collection started',
          status: 'processing'
        });
        
        // Run collection in background
        // collectData().catch(error => {
        //   logger.error('Background data collection failed:', error);
        // });
        
      } catch (error) {
        logger.error('Error starting data collection:', error);
        res.status(500).json({ 
          success: false, 
          error: 'Failed to start data collection' 
        });
      }
    });

    // API route to get collection status
    this.app.get('/api/collection-status', (req, res) => {
      res.json({
        status: 'idle',
        last_collection: null,
        api_calls_used: 0,
        rate_limit_remaining: 'unknown'
      });
    });

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'UK Schools Lead Generation Web Server'
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Endpoint not found' });
    });

    // Error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error:', error);
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  async start() {
    try {
      // Ensure web directories exist
      await this.ensureDirectories();
      
      // Start the server
      this.app.listen(this.port, () => {
        logger.info(`🌐 Web server started on port ${this.port}`);
        logger.info(`📊 Dashboard: http://localhost:${this.port}`);
        logger.info(`🔍 Health Check: http://localhost:${this.port}/health`);
        logger.info(`📡 API: http://localhost:${this.port}/api/school-data`);
        
        console.log('\n🎉 UK Schools Lead Generation Dashboard is ready!');
        console.log(`\n🌐 Open your browser and visit: http://localhost:${this.port}`);
        console.log('\n📋 Features available:');
        console.log('   • Real-time school data from Apollo.io');
        console.log('   • Interactive dashboard with filtering');
        console.log('   • Contact and email suggestions');
        console.log('   • Data export functionality');
        console.log('   • Analytics and performance metrics');
      });
      
    } catch (error) {
      logger.error('❌ Failed to start web server:', error);
      process.exit(1);
    }
  }

  async ensureDirectories() {
    const dirs = [
      this.webDir,
      path.join(this.webDir, 'data'),
      this.dataDir
    ];
    
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        logger.error(`Error creating directory ${dir}:`, error);
      }
    }
  }
}

// Main execution
async function main() {
  logger.info('🚀 Starting UK Schools Lead Generation Web Server...');
  
  const server = new WebServer();
  await server.start();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('👋 Shutting down web server gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('👋 Shutting down web server gracefully...');
  process.exit(0);
});

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logger.error('❌ Failed to start web server:', error);
    process.exit(1);
  });
}
