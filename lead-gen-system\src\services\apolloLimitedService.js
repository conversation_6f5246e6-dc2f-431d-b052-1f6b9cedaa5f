import axios from 'axios';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import { rateLimiter } from '../utils/rateLimiter.js';

class ApolloLimitedService {
  constructor() {
    this.baseUrl = config.apollo.baseUrl;
    this.apiKey = config.apollo.apiKey;
    
    // Create axios instance with authentication
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'X-Api-Key': this.apiKey
      }
    });
  }

  /**
   * Test API connection and determine available features
   * @returns {Promise<Object>} Connection status and available features
   */
  async testConnection() {
    try {
      await rateLimiter.consume('apollo');
      
      const response = await this.client.get('/auth/health');
      
      const features = await this.checkAvailableFeatures();
      
      return {
        success: true,
        message: 'Apollo API connection successful',
        health: response.data,
        available_features: features,
        plan_type: this.determinePlanType(features)
      };
    } catch (error) {
      return {
        success: false,
        message: 'Apollo API connection failed',
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Check which API endpoints are available with current plan
   * @returns {Promise<Object>} Available features
   */
  async checkAvailableFeatures() {
    const features = {
      email_finder: false,
      people_search: false,
      mixed_people_search: false,
      organizations_search: false,
      email_verifier: false
    };

    const endpoints = [
      { feature: 'email_finder', path: '/email_finder', params: { domain: 'example.com' } },
      { feature: 'people_search', path: '/people/search', params: { q_keywords: 'test', per_page: 1 } },
      { feature: 'mixed_people_search', path: '/mixed_people/search', params: { q_keywords: 'test', per_page: 1 } },
      { feature: 'organizations_search', path: '/organizations/search', params: { q_organization_name: 'test', per_page: 1 } },
      { feature: 'email_verifier', path: '/email_verifier', params: { email: '<EMAIL>' } }
    ];

    for (const endpoint of endpoints) {
      try {
        await rateLimiter.consume('apollo');
        await this.client.get(endpoint.path, { params: endpoint.params });
        features[endpoint.feature] = true;
        logger.info(`✅ Apollo feature available: ${endpoint.feature}`);
      } catch (error) {
        if (error.response?.status === 403) {
          features[endpoint.feature] = false;
          logger.info(`❌ Apollo feature not available: ${endpoint.feature}`);
        } else {
          // Other errors might be due to invalid params, so feature might still be available
          features[endpoint.feature] = 'unknown';
          logger.info(`⚠️ Apollo feature status unknown: ${endpoint.feature}`);
        }
      }
      
      // Add delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return features;
  }

  /**
   * Determine plan type based on available features
   * @param {Object} features - Available features object
   * @returns {string} Plan type
   */
  determinePlanType(features) {
    const availableCount = Object.values(features).filter(f => f === true).length;
    
    if (availableCount === 0) {
      return 'Free/Limited';
    } else if (availableCount <= 2) {
      return 'Basic';
    } else if (availableCount <= 4) {
      return 'Professional';
    } else {
      return 'Organization';
    }
  }

  /**
   * Find email for a domain (if email_finder is available)
   * @param {string} domain - Domain to search
   * @param {string} firstName - First name
   * @param {string} lastName - Last name
   * @returns {Promise<Object>} Email finder result
   */
  async findEmail(domain, firstName = null, lastName = null) {
    try {
      await rateLimiter.consume('apollo');
      
      const params = { domain };
      if (firstName) params.first_name = firstName;
      if (lastName) params.last_name = lastName;
      
      const response = await this.client.get('/email_finder', { params });
      
      logger.info(`Email finder result for ${domain}: ${response.data.email || 'Not found'}`);
      
      return {
        success: true,
        email: response.data.email,
        confidence: response.data.confidence,
        domain: domain,
        first_name: firstName,
        last_name: lastName
      };
    } catch (error) {
      logger.error(`Email finder failed for ${domain}:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message,
        domain: domain
      };
    }
  }

  /**
   * Verify email address (if email_verifier is available)
   * @param {string} email - Email to verify
   * @returns {Promise<Object>} Verification result
   */
  async verifyEmail(email) {
    try {
      await rateLimiter.consume('apollo');
      
      const response = await this.client.get('/email_verifier', {
        params: { email }
      });
      
      logger.info(`Email verification for ${email}: ${response.data.is_valid ? 'Valid' : 'Invalid'}`);
      
      return {
        success: true,
        email: email,
        is_valid: response.data.is_valid,
        is_deliverable: response.data.is_deliverable,
        is_risky: response.data.is_risky,
        confidence: response.data.confidence
      };
    } catch (error) {
      logger.error(`Email verification failed for ${email}:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message,
        email: email
      };
    }
  }

  /**
   * Generate email suggestions for school contacts
   * @param {Object} school - School information
   * @param {Array} contactTypes - Types of contacts to find
   * @returns {Promise<Array>} Email suggestions
   */
  async generateSchoolEmailSuggestions(school, contactTypes = ['headteacher', 'admin', 'it']) {
    const suggestions = [];
    const domain = this.extractDomainFromSchool(school);
    
    if (!domain) {
      logger.warn(`No domain found for school: ${school.name}`);
      return suggestions;
    }

    const emailPatterns = {
      headteacher: ['head', 'headteacher', 'principal', 'head.teacher'],
      admin: ['admin', 'office', 'reception', 'secretary'],
      it: ['it', 'ict', 'computing', 'technology']
    };

    for (const contactType of contactTypes) {
      const patterns = emailPatterns[contactType] || [contactType];
      
      for (const pattern of patterns) {
        const emailSuggestion = `${pattern}@${domain}`;
        
        // If email verifier is available, verify the suggestion
        const features = await this.checkAvailableFeatures();
        if (features.email_verifier) {
          const verification = await this.verifyEmail(emailSuggestion);
          if (verification.success && verification.is_valid) {
            suggestions.push({
              email: emailSuggestion,
              type: contactType,
              pattern: pattern,
              verified: true,
              confidence: verification.confidence
            });
          }
        } else {
          // Add unverified suggestion
          suggestions.push({
            email: emailSuggestion,
            type: contactType,
            pattern: pattern,
            verified: false,
            confidence: 'unknown'
          });
        }
        
        // Add delay between verifications
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    return suggestions;
  }

  /**
   * Extract domain from school information
   * @param {Object} school - School object
   * @returns {string|null} Domain or null if not found
   */
  extractDomainFromSchool(school) {
    // Try to extract domain from various fields
    const possibleSources = [
      school.website,
      school.email,
      school.domain,
      school.url
    ];

    for (const source of possibleSources) {
      if (source && typeof source === 'string') {
        // Extract domain from URL
        const urlMatch = source.match(/https?:\/\/(?:www\.)?([^\/]+)/);
        if (urlMatch) {
          return urlMatch[1];
        }
        
        // Extract domain from email
        const emailMatch = source.match(/@([^@]+)$/);
        if (emailMatch) {
          return emailMatch[1];
        }
        
        // If it looks like a domain already
        if (source.includes('.') && !source.includes('/') && !source.includes('@')) {
          return source;
        }
      }
    }

    // Generate likely domain from school name
    if (school.name) {
      const cleanName = school.name
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '')
        .replace(/(school|academy|college|trust|primary|secondary|high)$/g, '');
      
      return `${cleanName}.sch.uk`; // Common UK school domain pattern
    }

    return null;
  }

  /**
   * Get usage statistics and plan information
   * @returns {Promise<Object>} Usage stats
   */
  async getUsageStats() {
    try {
      const health = await this.testConnection();
      const features = await this.checkAvailableFeatures();
      
      return {
        plan_type: this.determinePlanType(features),
        available_features: features,
        connection_status: health.success,
        recommendations: this.getPlanRecommendations(features)
      };
    } catch (error) {
      logger.error('Error getting usage stats:', error);
      return {
        error: error.message
      };
    }
  }

  /**
   * Get recommendations for plan upgrade
   * @param {Object} features - Available features
   * @returns {Array} Recommendations
   */
  getPlanRecommendations(features) {
    const recommendations = [];
    
    if (!features.people_search && !features.mixed_people_search) {
      recommendations.push('Upgrade to access people search for finding school decision makers');
    }
    
    if (!features.organizations_search) {
      recommendations.push('Upgrade to access organization search for finding schools and MATs');
    }
    
    if (!features.email_finder) {
      recommendations.push('Upgrade to access email finder for discovering contact emails');
    }
    
    if (!features.email_verifier) {
      recommendations.push('Upgrade to access email verifier for validating email addresses');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Your current plan has access to all tested features');
    }

    return recommendations;
  }
}

export const apolloLimitedService = new ApolloLimitedService();
