// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`select single element on the scene > arrow 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      30,
      50,
    ],
  ],
  "roughness": 1,
  "roundness": {
    "type": 2,
  },
  "seed": 1278240551,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": 2019559783,
  "width": 30,
  "x": 10,
  "y": 10,
}
`;

exports[`select single element on the scene > arrow escape 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "endArrowhead": null,
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      30,
      50,
    ],
  ],
  "polygon": false,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "line",
  "updated": 1,
  "version": 4,
  "versionNonce": 2019559783,
  "width": 30,
  "x": 10,
  "y": 10,
}
`;

exports[`select single element on the scene > diamond 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 3,
  "versionNonce": 401146281,
  "width": 30,
  "x": 10,
  "y": 10,
}
`;

exports[`select single element on the scene > ellipse 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 3,
  "versionNonce": 401146281,
  "width": 30,
  "x": 10,
  "y": 10,
}
`;

exports[`select single element on the scene > rectangle 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 401146281,
  "width": 30,
  "x": 10,
  "y": 10,
}
`;
