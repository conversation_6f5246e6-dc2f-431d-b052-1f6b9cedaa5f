// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`duplicate element on move when ALT is clicked > rectangle 5`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 5,
  "versionNonce": 23633383,
  "width": 30,
  "x": 30,
  "y": 20,
}
`;

exports[`duplicate element on move when ALT is clicked > rectangle 6`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id4",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1505387817,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 7,
  "versionNonce": 81784553,
  "width": 30,
  "x": -10,
  "y": 60,
}
`;

exports[`move element > rectangle 5`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 1014066025,
  "width": 30,
  "x": 0,
  "y": 40,
}
`;

exports[`move element > rectangles with binding arrow 5`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id6",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 1006504105,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`move element > rectangles with binding arrow 6`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id6",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 300,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1116226695,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 7,
  "versionNonce": 1984422985,
  "width": 300,
  "x": 201,
  "y": 2,
}
`;

exports[`move element > rectangles with binding arrow 7`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "id3",
    "focus": "-0.46667",
    "gap": 10,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": "81.40630",
  "id": "id6",
  "index": "a2",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      "81.00000",
      "81.40630",
    ],
  ],
  "roughness": 1,
  "roundness": {
    "type": 2,
  },
  "seed": 23633383,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "id0",
    "focus": "-0.60000",
    "gap": 10,
  },
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 11,
  "versionNonce": 1573789895,
  "width": "81.00000",
  "x": "110.00000",
  "y": 50,
}
`;
