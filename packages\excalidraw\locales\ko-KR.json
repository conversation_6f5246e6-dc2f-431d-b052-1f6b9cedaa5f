{"labels": {"paste": "붙여넣기", "pasteAsPlaintext": "일반 텍스트로 붙여넣기", "pasteCharts": "차트 붙여넣기", "selectAll": "전체 선택", "multiSelect": "선택 영역에 추가하기", "moveCanvas": "캔버스 이동", "cut": "잘라내기", "copy": "복사", "copyAsPng": "클립보드로 PNG 이미지 복사", "copyAsSvg": "클립보드로 SVG 이미지 복사", "copyText": "클립보드로 텍스트 복사", "copySource": "소스코드를 클립보드로 복사", "convertToCode": "코드로 변환", "bringForward": "앞으로 가져오기", "sendToBack": "맨 뒤로 보내기", "bringToFront": "맨 앞으로 가져오기", "sendBackward": "뒤로 보내기", "delete": "삭제", "copyStyles": "스타일 복사하기", "pasteStyles": "스타일 붙여넣기", "stroke": "선 색상", "background": "배경색", "fill": "채우기", "strokeWidth": "선 굵기", "strokeStyle": "선", "strokeStyle_solid": "실선", "strokeStyle_dashed": "파선", "strokeStyle_dotted": "점선", "sloppiness": "대충 긋기", "opacity": "불투명도", "textAlign": "텍스트 정렬", "edges": "가장자리", "sharp": "뾰족하게", "round": "둥글게", "arrowheads": "화살촉", "arrowhead_none": "없음", "arrowhead_arrow": "화살표", "arrowhead_bar": "막대", "arrowhead_circle": "원", "arrowhead_circle_outline": "원 (외곽선)", "arrowhead_triangle": "삼각형", "arrowhead_triangle_outline": "삼각형 (외곽선)", "arrowhead_diamond": "마름모", "arrowhead_diamond_outline": "마름모 (외곽선)", "fontSize": "글자 크기", "fontFamily": "글꼴", "addWatermark": "\"Made with Excalidraw\" 추가", "handDrawn": "손글씨", "normal": "일반", "code": "코드", "small": "작게", "medium": "보통", "large": "크게", "veryLarge": "매우 크게", "solid": "단색", "hachure": "평행선", "zigzag": "지그재그", "crossHatch": "교차선", "thin": "얇게", "bold": "굵게", "left": "왼쪽", "center": "가운데", "right": "오른쪽", "extraBold": "매우 굵게", "architect": "건축가", "artist": "예술가", "cartoonist": "만화가", "fileTitle": "파일 이름", "colorPicker": "색상 선택기", "canvasColors": "캔버스에서 사용되었음", "canvasBackground": "캔버스 배경", "drawingCanvas": "캔버스 그리기", "layers": "레이어", "actions": "동작", "language": "언어", "liveCollaboration": "실시간 협업...", "duplicateSelection": "복제", "untitled": "제목 없음", "name": "이름", "yourName": "이름 입력", "madeWithExcalidraw": "Made with Excalidraw", "group": "그룹 생성", "ungroup": "그룹 해제", "collaborators": "공동 작업자", "showGrid": "그리드 보기", "addToLibrary": "라이브러리에 추가", "removeFromLibrary": "라이브러리에서 제거", "libraryLoadingMessage": "라이브러리 불러오는 중…", "libraries": "라이브러리 찾기", "loadingScene": "화면 불러오는 중…", "align": "정렬", "alignTop": "상단 정렬", "alignBottom": "하단 정렬", "alignLeft": "왼쪽 정렬", "alignRight": "오른쪽 정렬", "centerVertically": "수직으로 중앙 정렬", "centerHorizontally": "수평으로 중앙 정렬", "distributeHorizontally": "수평으로 분배", "distributeVertically": "수직으로 분배", "flipHorizontal": "좌우반전", "flipVertical": "상하반전", "viewMode": "보기 모드", "share": "공유", "showStroke": "윤곽선 색상 선택기 열기", "showBackground": "배경 색상 선택기 열기", "toggleTheme": "테마 전환", "personalLib": "개인 라이브러리", "excalidrawLib": "Excalidraw 라이브러리", "decreaseFontSize": "폰트 사이즈 줄이기", "increaseFontSize": "폰트 사이즈 키우기", "unbindText": "텍스트 분리", "bindText": "텍스트를 컨테이너에 결합", "createContainerFromText": "텍스트를 컨테이너에 담기", "link": {"edit": "링크 수정하기", "editEmbed": "링크 & 임베드 수정하기", "create": "링크 만들기", "createEmbed": "링크 & 임베드 만들기", "label": "링크", "labelEmbed": "링크 & 임베드", "empty": "링크를 지정하지 않았습니다"}, "lineEditor": {"edit": "선 수정하기", "exit": "선 편집기 종료"}, "elementLock": {"lock": "잠금", "unlock": "잠금 해제", "lockAll": "모두 잠금", "unlockAll": "모두 잠금 해제"}, "statusPublished": "게시됨", "sidebarLock": "사이드바 유지", "selectAllElementsInFrame": "프레임의 모든 요소 선택", "removeAllElementsFromFrame": "프레임의 모든 요소 삭제", "eyeDropper": "캔버스에서 색상 고르기", "textToDiagram": "텍스트를 다이어그램으로", "prompt": "프롬프트"}, "library": {"noItems": "추가된 아이템 없음", "hint_emptyLibrary": "캔버스 위에서 아이템을 선택하여 여기에 추가를 하거나, 아래의 공용 저장소에서 라이브러리를 설치하세요.", "hint_emptyPrivateLibrary": "캔버스 위에서 아이템을 선택하여 여기 추가하세요."}, "buttons": {"clearReset": "캔버스 초기화", "exportJSON": "파일로 내보내기", "exportImage": "이미지 내보내기", "export": "다른 이름으로 저장...", "copyToClipboard": "클립보드로 복사", "save": "현재 파일에 저장", "saveAs": "다른 이름으로 저장", "load": "열기", "getShareableLink": "공유 가능한 링크 생성", "close": "닫기", "selectLanguage": "언어 선택", "scrollBackToContent": "콘텐츠 영역으로 스크롤하기", "zoomIn": "확대", "zoomOut": "축소", "resetZoom": "확대/축소 초기화", "menu": "메뉴", "done": "완료", "edit": "수정", "undo": "실행 취소", "redo": "다시 실행", "resetLibrary": "라이브러리 리셋", "createNewRoom": "방 만들기", "fullScreen": "전체화면", "darkMode": "다크 모드", "lightMode": "밝은 모드", "zenMode": "젠 모드", "objectsSnapMode": "다른 요소들에 정렬시키기", "exitZenMode": "젠 모드 종료하기", "cancel": "취소", "clear": "지우기", "remove": "삭제", "embed": "임베딩 토글", "publishLibrary": "게시하기", "submit": "제출", "confirm": "확인", "embeddableInteractionButton": "클릭하여 상호작용"}, "alerts": {"clearReset": "모든 작업 내용이 초기화됩니다. 계속하시겠습니까?", "couldNotCreateShareableLink": "공유 가능한 링크를 생성할 수 없습니다.", "couldNotCreateShareableLinkTooBig": "공유 가능한 링크를 생성할 수 없습니다: 화면이 너무 큽니다.", "couldNotLoadInvalidFile": "유효하지 않은 파일입니다.", "importBackendFailed": "서버로부터 불러 오지 못했습니다.", "cannotExportEmptyCanvas": "빈 캔버스를 내보낼 수 없습니다.", "couldNotCopyToClipboard": "클립보드로 복사하지 못했습니다.", "decryptFailed": "데이터를 복호화하지 못했습니다.", "uploadedSecurly": "업로드는 종단 간 암호화로 보호되므로 Excalidraw 서버 및 타사가 콘텐츠를 읽을 수 없습니다.", "loadSceneOverridePrompt": "외부 파일을 불러 오면 기존 콘텐츠가 대체됩니다. 계속 진행할까요?", "collabStopOverridePrompt": "협업 세션을 종료하면 로컬 저장소에 있는 그림이 협업 세션의 그림으로 대체됩니다. 진행하겠습니까?\n\n(로컬 저장소에 있는 그림을 유지하려면 현재 브라우저 탭을 닫아주세요.)", "errorAddingToLibrary": "아이템을 라이브러리에 추가 할수 없습니다", "errorRemovingFromLibrary": "라이브러리에서 아이템을 삭제할수 없습니다", "confirmAddLibrary": "{{numShapes}}개의 모양이 라이브러리에 추가됩니다. 계속하시겠어요?", "imageDoesNotContainScene": "이 이미지는 화면 데이터를 포함하고 있지 않은 것 같습니다. 내보낼 때 화면을 첨부하도록 설정하셨나요?", "cannotRestoreFromImage": "이미지 파일에서 화면을 복구할 수 없었습니다", "invalidSceneUrl": "제공된 URL에서 화면을 가져오는데 실패했습니다. 주소가 잘못되거나, 유효한 Excalidraw JSON 데이터를 포함하고 있지 않은 것일 수 있습니다.", "resetLibrary": "당신의 라이브러리를 초기화 합니다. 계속하시겠습니까?", "removeItemsFromsLibrary": "{{count}}개의 아이템을 라이브러리에서 삭제하시겠습니까?", "invalidEncryptionKey": "암호화 키는 반드시 22글자여야 합니다. 실시간 협업이 비활성화됩니다.", "collabOfflineWarning": "인터넷에 연결되어 있지 않습니다.\n변경 사항들이 저장되지 않습니다!"}, "errors": {"unsupportedFileType": "지원하지 않는 파일 형식 입니다.", "imageInsertError": "이미지를 삽입할 수 없습니다. 나중에 다시 시도 하십시오", "fileTooBig": "파일이 너무 큽니다. 최대 크기는 {{maxSize}} 입니다.", "svgImageInsertError": "SVG 이미지를 삽입하지 못했습니다. SVG 문법이 유효하지 않은 것 같습니다.", "failedToFetchImage": "이미지를 가져오는데 실패했습니다.", "invalidSVGString": "유효하지 않은 SVG입니다.", "cannotResolveCollabServer": "협업 서버에 접속하는데 실패했습니다. 페이지를 새로고침하고 다시 시도해보세요.", "importLibraryError": "라이브러리를 불러오지 못했습니다.", "collabSaveFailed": "데이터베이스에 저장하지 못했습니다. 문제가 계속 된다면, 작업 내용을 잃지 않도록 로컬 저장소에 저장해 주세요.", "collabSaveFailed_sizeExceeded": "데이터베이스에 저장하지 못했습니다. 캔버스가 너무 큰 거 같습니다. 문제가 계속 된다면, 작업 내용을 잃지 않도록 로컬 저장소에 저장해 주세요.", "imageToolNotSupported": "이미지가 비활성화 되었습니다.", "brave_measure_text_error": {"line1": "귀하께서는 <bold>강력한 지문 차단 설정</bold>이 활성화된 Brave browser를 사용하고 계신 것 같습니다.", "line2": "이 기능으로 인해 화이트보드의 <bold>텍스트 요소들</bold>이 손상될 수 있습니다.", "line3": "저희는 해당 기능을 비활성화하는 것을 강력히 권장 드립니다. 비활성화 방법에 대해서는 <link>이 게시글</link>을 참고해주세요.", "line4": "만약 이 설정을 껐음에도 텍스트 요소들이 올바르게 표시되지 않는다면, 저희 Github에 <issueLink>이슈</issueLink>를 올려주시거나 <discordLink>Discord</discordLink>로 알려주세요."}, "libraryElementTypeError": {"embeddable": "임베드 요소들은 라이브러리에 추가할 수 없습니다.", "iframe": "IFrame 요소들은 라이브러리에 추가할 수 없습니다.", "image": "라이브러리에 이미지 삽입 기능은 곧 지원될 예정입니다!"}, "asyncPasteFailedOnRead": "붙여넣는데 실패했습니다. (시스템 클립보드를 읽는데 실패했습니다)", "asyncPasteFailedOnParse": "붙여넣는데 실패했습니다.", "copyToSystemClipboardFailed": "클립보드로 복사하는데 실패했습니다."}, "toolBar": {"selection": "선택", "image": "이미지 삽입", "rectangle": "사각형", "diamond": "다이아몬드", "ellipse": "타원", "arrow": "화살표", "line": "선", "freedraw": "그리기", "text": "텍스트", "library": "라이브러리", "lock": "선택된 도구 유지하기", "penMode": "펜 모드 - 터치 방지", "link": "선택한 도형에 대해서 링크를 추가/업데이트", "eraser": "지우개", "frame": "프레임 도구", "magicframe": "와이어프레임을 코드로", "embeddable": "웹 임베드", "laser": "레이저 포인터", "hand": "손 (패닝 도구)", "extraTools": "다른 도구", "mermaidToExcalidraw": "Mermaid에서 불러오기", "magicSettings": "AI 설정"}, "headings": {"canvasActions": "캔버스 동작", "selectedShapeActions": "선택된 모양 동작", "shapes": "모양"}, "hints": {"canvasPanning": "캔버스를 옮기려면 마우스 휠이나 스페이스바를 누르고 드래그하거나, 손 도구를 사용하기", "linearElement": "여러 점을 연결하려면 클릭하고, 직선을 그리려면 바로 드래그하세요.", "freeDraw": "클릭 후 드래그하세요. 완료되면 놓으세요.", "text": "팁: 선택 툴로 아무 곳이나 더블 클릭해 텍스트를 추가할 수도 있습니다.", "embeddable": "클릭 및 드래그하여 웹사이트 임베드 만들기", "text_selected": "더블 클릭 또는 ENTER를 눌러서 텍스트 수정", "text_editing": "ESC나 CtrlOrCmd+ENTER를 눌러서 수정을 종료하기", "linearElementMulti": "마지막 지점을 클릭하거나 Esc 또는 Enter 키를 눌러 완료하세요.", "lockAngle": "SHIFT 키를 누르면서 회전하면 각도를 제한할 수 있습니다.", "resize": "SHIFT 키를 누르면서 조정하면 크기의 비율이 제한됩니다.\nALT를 누르면서 조정하면 중앙을 기준으로 크기를 조정합니다.", "resizeImage": "SHIFT를 눌러서 자유롭게 크기를 변경하거나,\nALT를 눌러서 중앙을 고정하고 크기를 변경하기", "rotate": "SHIFT 키를 누르면서 회전하면 각도를 제한할 수 있습니다.", "lineEditor_info": "꼭짓점을 수정하려면 CtrlOrCmd 키를 누르고 더블 클릭을 하거나 CtrlOrCmd + Enter를 누르세요.", "lineEditor_pointSelected": "Delete 키로 꼭짓점을 제거하거나,\nCtrlOrCmd+D 로 복제하거나, 드래그 해서 이동시키기", "lineEditor_nothingSelected": "꼭짓점을 선택해서 수정하거나 (SHIFT를 눌러서 여러개 선택),\nAlt를 누르고 클릭해서 새로운 꼭짓점 추가하기", "placeImage": "클릭해서 이미지를 배치하거나, 클릭하고 드래그해서 사이즈를 조정하기", "publishLibrary": "당신만의 라이브러리를 게시하기", "bindTextToElement": "Enter 키를 눌러서 텍스트 추가하기", "deepBoxSelect": "CtrlOrCmd 키를 눌러서 깊게 선택하고, 드래그하지 않도록 하기", "eraserRevert": "Alt를 눌러서 삭제하도록 지정된 요소를 되돌리기", "firefox_clipboard_write": "이 기능은 설정에서 \"dom.events.asyncClipboard.clipboardItem\" 플래그를 \"true\"로 설정하여 활성화할 수 있습니다. Firefox에서 브라우저 플래그를 수정하려면, \"about:config\" 페이지에 접속하세요.", "disableSnapping": "CtrlOrCmd 키를 눌러서 다른 요소와의 정렬 무시하기"}, "canvasError": {"cannotShowPreview": "미리보기를 볼 수 없습니다", "canvasTooBig": "캔버스가 너무 큽니다.", "canvasTooBigTip": "팁: 멀리 있는 요소들을 좀 더 가까이로 붙여 보세요."}, "errorSplash": {"headingMain": "오류가 발생했습니다. <button>페이지 새로고침</button>", "clearCanvasMessage": "새로고침으로 해결되지 않을 경우, <button>캔버스 비우기</button>", "clearCanvasCaveat": " 작업 내용을 잃게 됩니다 ", "trackedToSentry": "오류 {{eventId}} 가 시스템에서 발견되었습니다.", "openIssueMessage": "저희는 화면 정보를 오류에 포함하지 않도록 매우 주의하고 있습니다. 혹시 화면에 민감한 내용이 없다면 이곳에 업로드를 고려해주세요.<button>버그 트래커</button> 아래 정보를 GitHub 이슈에 복사 및 붙여넣기해 주세요.", "sceneContent": "화면 내용:"}, "roomDialog": {"desc_intro": "현재 화면에 공동 작업자를 초대해 협업할 수 있습니다.", "desc_privacy": "안심하세요, 세션은 종단 간 암호화를 사용하므로 당신의 작업은 비공개로 유지되며 서버조차도 작업 내용을 알 수 없습니다.", "button_startSession": "세션 시작", "button_stopSession": "세션 중단", "desc_inProgressIntro": "실시간 협업 세션이 진행 중입니다.", "desc_shareLink": "공동 작업자에게 이 링크를 공유하세요.", "desc_exitSession": "세션을 중단하면 연결은 끊어지나 작업을 이어갈 수 있습니다. 이 작업은 다른 작업자에게 영향을 미치지 않으며 각자의 공동 작업은 계속 유지됩니다.", "shareTitle": "Excalidraw의 실시간 협업 세션에 참가하기"}, "errorDialog": {"title": "오류"}, "exportDialog": {"disk_title": "디스크에 저장", "disk_details": "나중에 다시 불러올 수 있도록 화면 데이터를 내보냅니다.", "disk_button": "파일로 저장", "link_title": "공유 가능한 링크 생성", "link_details": "읽기 전용 링크로 내보냅니다.", "link_button": "링크로 내보내기", "excalidrawplus_description": "화면을 당신의 Excalidraw+ 작업 공간으로 저장합니다.", "excalidrawplus_button": "내보내기", "excalidrawplus_exportError": "지금은 Excalidraw+로 내보낼 수 없습니다..."}, "helpDialog": {"blog": "블로그 읽어보기", "click": "클릭", "deepSelect": "깊게 선택", "deepBoxSelect": "영역을 깊게 선택하고, 드래그하지 않도록 하기", "curvedArrow": "곡선 화살표", "curvedLine": "곡선", "documentation": "설명서", "doubleClick": "더블 클릭", "drag": "드래그", "editor": "에디터", "editLineArrowPoints": "직선 / 화살표 꼭짓점 수정", "editText": "텍스트 수정 / 라벨 추가", "github": "문제 제보하기", "howto": "가이드 참고하기", "or": "또는", "preventBinding": "화살표가 붙지 않게 하기", "tools": "도구", "shortcuts": "키보드 단축키", "textFinish": "편집 완료 (텍스트 에디터)", "textNewLine": "줄바꿈(텍스트 에디터)", "title": "도움말", "view": "보기", "zoomToFit": "모든 요소가 보이도록 확대/축소", "zoomToSelection": "선택 영역으로 확대/축소", "toggleElementLock": "선택한 항목을 잠금/잠금 해제", "movePageUpDown": "페이지 움직이기 위/아래", "movePageLeftRight": "페이지 움직이기 좌/우"}, "clearCanvasDialog": {"title": "캔버스 지우기"}, "publishDialog": {"title": "라이브러리 게시하기", "itemName": "아이템 이름", "authorName": "저자명", "githubUsername": "깃허브 사용자이름", "twitterUsername": "트위터 사용자이름", "libraryName": "라이브러리 이름", "libraryDesc": "라이브러리 설명", "website": "웹사이트", "placeholder": {"authorName": "이름 또는 사용자명", "libraryName": "당신의 라이브러리 이름", "libraryDesc": "사람들에게 라이브러리의 용도를 알기 쉽게 설명해주세요", "githubHandle": "GitHub 사용자명 (선택), 제출한 뒤에도 심사를 위해서 라이브러리를 수정할 때 사용됩니다", "twitterHandle": "Twitter 사용자명 (선택), Twitter를 통해서 홍보할 때 제작자를 밝히기 위해 사용됩니다", "website": "개인 웹사이트나 다른 어딘가의 링크 (선택)"}, "errors": {"required": "필수사항", "website": "유효한 URL을 입력하세요"}, "noteDescription": "당신의 라이브러리를 제출하여 <link>공개 라이브러리 저장소</link>에서 다른 사람들의 그림에 사용할 수 있도록 하세요.", "noteGuidelines": "라이브러리는 먼저 수동으로 승인되어야 합니다. 제출하기 전에 <link>가이드라인</link>을 먼저 읽어보세요. 의견을 공유하거나 변경사항을 만들기 위해선 GitHub 계정이 필요하지만, 반드시 필요하진 않습니다.", "noteLicense": "제출함으로써, 당신은 라이브러리가 <link>MIT 라이선스 </link>하에 배포됨을, 즉 아무나 제약 없이 사용할 수 있음에 동의합니다.", "noteItems": "각각의 라이브러리는 분류할 수 있도록 고유한 이름을 가져야 합니다. 다음의 라이브러리 항목이 포함됩니다:", "atleastOneLibItem": "최소한 하나의 라이브러리를 선택해주세요", "republishWarning": "참고: 선택된 항목의 일부는 이미 제출/게시되었습니다. 기존의 라이브러리나 제출물을 업데이트하는 경우에만 제출하세요."}, "publishSuccessDialog": {"title": "라이브러리 제출됨", "content": "{{authorName}}님 감사합니다. 당신의 라이브러리가 심사를 위해 제출되었습니다. 진행 상황을<link>여기에서 확인하실 수 있습니다.</link>"}, "confirmDialog": {"resetLibrary": "라이브러리 리셋", "removeItemsFromLib": "선택한 항목을 라이브러리에서 제거"}, "imageExportDialog": {"header": "이미지 내보내기", "label": {"withBackground": "배경", "onlySelected": "선택한 항목만", "darkMode": "다크 모드", "embedScene": "화면을 담기", "scale": "크기", "padding": "여백"}, "tooltip": {"embedScene": "화면 정보가 내보내는 PNG/SVG 파일에 저장되어 이후에 파일에서 화면을 복구할 수 있습니다. 파일 크기가 증가합니다."}, "title": {"exportToPng": "PNG로 내보내기", "exportToSvg": "SVG로 내보내기", "copyPngToClipboard": "클립보드로 PNG 복사"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "클립보드로 복사"}}, "encrypted": {"tooltip": "그림은 종단 간 암호화되므로 Excalidraw의 서버는 절대로 내용을 알 수 없습니다.", "link": "Excalidraw의 종단 간 암호화에 대한 블로그 포스트"}, "stats": {"angle": "각도", "element": "요소", "elements": "요소", "height": "높이", "scene": "화면", "selected": "선택됨", "storage": "저장공간", "title": "덕후들을 위한 통계", "total": "합계", "version": "버전", "versionCopy": "복사하려면 클릭", "versionNotAvailable": "해당 버전 사용 불가능", "width": "너비"}, "toast": {"addedToLibrary": "라이브러리에 추가되었습니다", "copyStyles": "스타일 복사.", "copyToClipboard": "클립보드로 복사.", "copyToClipboardAsPng": "{{exportSelection}}를 클립보드에 PNG로 복사했습니다\n({{exportColorScheme}})", "fileSaved": "파일이 저장되었습니다.", "fileSavedToFilename": "{filename} 로 저장되었습니다", "canvas": "캔버스", "selection": "선택한 요소", "pasteAsSingleElement": "단일 요소로 붙여넣거나, 기존 텍스트 에디터에 붙여넣으려면 {{shortcut}} 을 사용하세요.", "unableToEmbed": "이 URL의 임베딩이 허용되지 않았습니다. GitHub에 이슈를 남겨서 이 URL이 화이트리스트에 등재될 수 있도록 요청하세요", "unrecognizedLinkFormat": "임베딩하려는 링크의 형식이 잘못된 것 같습니다. 원본 사이트에서 제공하는 \"임베딩\" 텍스트를 그대로 붙여 넣어 주세요"}, "colors": {"transparent": "투명", "black": "블랙", "white": "화이트", "red": "레드", "pink": "핑크", "grape": "그레이프", "violet": "바이올렛", "gray": "그레이", "blue": "블루", "cyan": "시안", "teal": "틸", "green": "그린", "yellow": "옐로우", "orange": "오렌지", "bronze": "브론즈"}, "welcomeScreen": {"app": {"center_heading": "모든 데이터는 브라우저에 안전하게 저장됩니다.", "center_heading_plus": "대신 Excalidraw+로 이동하시겠습니까?", "menuHint": "내보내기, 설정, 언어, ..."}, "defaults": {"menuHint": "내보내기, 설정, 등등...", "center_heading": "간단하게 만드는 다이어그램.", "toolbarHint": "도구를 선택하고, 그리세요!", "helpHint": "단축키 & 도움말"}}, "colorPicker": {"mostUsedCustomColors": "가장 많이 사용된 색상들", "colors": "색상", "shades": "색조", "hexCode": "Hex 코드", "noShades": "사용할 수 있는 색조가 없음"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "이미지로 내보내기", "button": "이미지로 내보내기", "description": "나중에 다시 불러올 수 있도록 화면 데이터를 이미지로 내보냅니다."}, "saveToDisk": {"title": "디스크에 저장", "button": "디스크에 저장", "description": "나중에 다시 불러올 수 있도록 화면 데이터를 내보냅니다."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Excalidraw+로 내보내기", "description": "화면을 당신의 Excalidraw+ 작업 공간으로 저장합니다."}}, "modal": {"loadFromFile": {"title": "파일에서 불러오기", "button": "파일에서 불러오기", "description": "파일을 불러오면 <bold>현재 작성된 데이터를 덮어쓰게 됩니다</bold>.<br></br>다음 옵션 중 하나를 선택하여 작업물을 백업해 둘 수 있습니다."}, "shareableLink": {"title": "주소에서 불러오기", "button": "컨텐츠를 덮어쓰기", "description": "외부 작업물을 불러오면 <bold>현재 작성된 데이터를 덮어쓰게 됩니다</bold>.<br></br>다음 옵션 중 하나를 선택하여 작업물을 백업해 둘 수 있습니다."}}}, "mermaid": {"title": "Mermaid에서 불러오기", "button": "삽입하기", "description": "지금은 <flowchartLink>순서도</flowchartLink>,<sequenceLink> 시퀀스</sequenceLink>, <classLink>클래스 </classLink>다이어그램만 지원합니다. 다른 형식들은 Excalidraw에서는 이미지로 표시됩니다.", "syntax": "Mermaid 구문", "preview": "미리보기"}}