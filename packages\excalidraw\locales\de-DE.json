{"labels": {"paste": "Einfügen", "pasteAsPlaintext": "Als reinen Text einfügen", "pasteCharts": "Diagramme einfügen", "selectAll": "Alle auswählen", "multiSelect": "Element zur Auswahl hinzufügen", "moveCanvas": "<PERSON><PERSON><PERSON><PERSON> versch<PERSON>", "cut": "Ausschneiden", "copy": "<PERSON><PERSON><PERSON>", "copyAsPng": "In Zwischenablage kopieren (PNG)", "copyAsSvg": "In Zwischenablage kopieren (SVG)", "copyText": "In die Zwischenablage als Text kopieren", "copySource": "Quelle in Zwischenablage kopieren", "convertToCode": "In Code konvertieren", "bringForward": "<PERSON><PERSON> vorne", "sendToBack": "In den Hintergrund", "bringToFront": "In den Vordergrund", "sendBackward": "Nach hinten", "delete": "Löschen", "copyStyles": "Formatierung kopieren", "pasteStyles": "Formatierung übernehmen", "stroke": "<PERSON><PERSON>", "background": "Hi<PERSON>grund", "fill": "Füllung", "strokeWidth": "Strichstärke", "strokeStyle": "<PERSON><PERSON><PERSON><PERSON>", "strokeStyle_solid": "Durchgezogen", "strokeStyle_dashed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dotted": "Gepunktet", "sloppiness": "Sauberkeit", "opacity": "Deckkraft", "textAlign": "Textausrichtung", "edges": "Kanten", "sharp": "Scharf", "round": "Rund", "arrowheads": "Pfeilspitzen", "arrowhead_none": "<PERSON><PERSON>", "arrowhead_arrow": "Pfeil", "arrowhead_bar": "<PERSON><PERSON><PERSON>", "arrowhead_circle": "Kreis", "arrowhead_circle_outline": "Kreis (Umrandung)", "arrowhead_triangle": "<PERSON><PERSON><PERSON>", "arrowhead_triangle_outline": "<PERSON><PERSON><PERSON> (Umrandung)", "arrowhead_diamond": "<PERSON><PERSON>", "arrowhead_diamond_outline": "<PERSON><PERSON> (Umrandung)", "fontSize": "Schriftgröße", "fontFamily": "Schriftfamilie", "addWatermark": "\"Made with Excalidraw\" hinzufügen", "handDrawn": "Handgezeichnet", "normal": "Normal", "code": "Code", "small": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON><PERSON>", "veryLarge": "<PERSON><PERSON> groß", "solid": "Deckend", "hachure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zigzag": "Zickzack", "crossHatch": "K<PERSON>uzsch<PERSON><PERSON><PERSON><PERSON>", "thin": "<PERSON><PERSON><PERSON>", "bold": "<PERSON><PERSON>", "left": "Links", "center": "<PERSON><PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>", "extraBold": "Extra Fett", "architect": "Architekt", "artist": "<PERSON><PERSON><PERSON><PERSON>", "cartoonist": "Karikaturist", "fileTitle": "Dateiname", "colorPicker": "Farbauswähler", "canvasColors": "<PERSON><PERSON> ver<PERSON>", "canvasBackground": "Zeichenflächenhintergrund", "drawingCanvas": "<PERSON><PERSON><PERSON><PERSON>", "layers": "<PERSON><PERSON><PERSON>", "actions": "Aktionen", "language": "<PERSON><PERSON><PERSON>", "liveCollaboration": "Live-Zusammenarbeit...", "duplicateSelection": "Duplizieren", "untitled": "Unbenannt", "name": "Name", "yourName": "<PERSON><PERSON>", "madeWithExcalidraw": "Made with Excalidraw", "group": "Auswahl gruppieren", "ungroup": "Gruppierung aufheben", "collaborators": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showGrid": "<PERSON><PERSON> anzeigen", "addToLibrary": "Zur Bibliothek hinzufügen", "removeFromLibrary": "Aus Bibliothek entfernen", "libraryLoadingMessage": "Lade Bibliothek…", "libraries": "Bibliotheken durchsuchen", "loadingScene": "<PERSON><PERSON>…", "align": "Ausrichten", "alignTop": "<PERSON>ber<PERSON>n", "alignBottom": "<PERSON><PERSON>e Kanten", "alignLeft": "<PERSON><PERSON>", "alignRight": "<PERSON><PERSON><PERSON>", "centerVertically": "Vertikal zentrieren", "centerHorizontally": "Horizontal zentrieren", "distributeHorizontally": "Horizontal verteilen", "distributeVertically": "Vertikal verteilen", "flipHorizontal": "Horizontal spiegeln", "flipVertical": "Vertikal spiegeln", "viewMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "share": "Teilen", "showStroke": "Auswahl für Strichfarbe anzeigen", "showBackground": "Hintergrundfarbe auswählen", "toggleTheme": "Design umschalten", "personalLib": "Persönliche Bibliothek", "excalidrawLib": "Excalidraw Bibliothek", "decreaseFontSize": "Schriftgröße verkleinern", "increaseFontSize": "Sc<PERSON><PERSON> vergrößern", "unbindText": "Text lösen", "bindText": "Text an Container binden", "createContainerFromText": "Text in Container e<PERSON><PERSON>ten", "link": {"edit": "<PERSON> bear<PERSON>", "editEmbed": "Link <PERSON> & <PERSON><PERSON><PERSON>ten", "create": "<PERSON>", "createEmbed": "Link erstellen & einbetten", "label": "Link", "labelEmbed": "Verlinken & einbetten", "empty": "<PERSON><PERSON> f<PERSON>"}, "lineEditor": {"edit": "<PERSON><PERSON>", "exit": "<PERSON><PERSON>ed<PERSON> verl<PERSON>en"}, "elementLock": {"lock": "<PERSON><PERSON><PERSON>", "unlock": "Entsperren", "lockAll": "<PERSON>e sperren", "unlockAll": "Alle entsperren"}, "statusPublished": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebarLock": "Seitenleiste offen lassen", "selectAllElementsInFrame": "Alle Elemente im Rahmen auswählen", "removeAllElementsFromFrame": "Alle Elemente aus dem Rahmen entfernen", "eyeDropper": "Far<PERSON> von der Zeichenfläche auswählen", "textToDiagram": "Text zu Diagramm", "prompt": "Eingabe"}, "library": {"noItems": "Noch keine Elemente hinzugefügt...", "hint_emptyLibrary": "Wähle ein Element auf der Zeichenfläche, um es hier hinzuzufügen. Oder installiere eine Bibliothek aus dem öffentlichen Verzeichnis.", "hint_emptyPrivateLibrary": "<PERSON><PERSON>hle ein Element von der Zeichenfläche, um es hier hinzuzufügen."}, "buttons": {"clearReset": "Zeichenfläche löschen & Hintergrundfarbe zurücksetzen", "exportJSON": "In Datei exportieren", "exportImage": "Exportiere Bild...", "export": "Speichern als...", "copyToClipboard": "In Zwischenablage kopieren", "save": "In aktueller Datei speichern", "saveAs": "Speichern unter", "load": "<PERSON><PERSON><PERSON>", "getShareableLink": "Teilbaren Link erhalten", "close": "Schließen", "selectLanguage": "Sprache auswählen", "scrollBackToContent": "Zurück zum Inhalt", "zoomIn": "Vergrößern", "zoomOut": "Verkleinern", "resetZoom": "Zoom zurücksetzen", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetLibrary": "Bibliothek zurücksetzen", "createNewRoom": "Neuen Raum erstellen", "fullScreen": "Vollbildanzeige", "darkMode": "<PERSON><PERSON><PERSON> Design", "lightMode": "<PERSON><PERSON> Design", "zenMode": "Zen-Modus", "objectsSnapMode": "Einrasten an Objekten", "exitZenMode": "Zen-Modus verlassen", "cancel": "Abbrechen", "clear": "Löschen", "remove": "Entfernen", "embed": "Einbettung umschalten", "publishLibrary": "Veröffentlichen", "submit": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Bestätigen", "embeddableInteractionButton": "<PERSON><PERSON><PERSON>, um zu interagieren"}, "alerts": {"clearReset": "Dies wird die ganze Zeichenfläche löschen. Bist du dir sicher?", "couldNotCreateShareableLink": "Konnte keinen teilbaren Link erstellen.", "couldNotCreateShareableLinkTooBig": "Konnte keinen teilbaren Link erstellen: Die Zeichnung ist zu groß", "couldNotLoadInvalidFile": "Ungültige Datei konnte nicht geladen werden", "importBackendFailed": "Import vom Server ist fehlgeschlagen.", "cannotExportEmptyCanvas": "<PERSON><PERSON> Zeichenfläche kann nicht exportiert werden.", "couldNotCopyToClipboard": "Ko<PERSON>ren in die Zwischenablage fehlgeschlagen.", "decryptFailed": "Daten konnten nicht entschlüsselt werden.", "uploadedSecurly": "Der Upload wurde mit Ende-zu-Ende-Verschlüsselung gespeichert. Weder Excalidraw noch Dritte können den Inhalt einsehen.", "loadSceneOverridePrompt": "Das Laden einer externen Zeichnung ersetzt den vorhandenen Inhalt. Möchtest du fortfahren?", "collabStopOverridePrompt": "Das Stoppen der Sitzung wird deine vorherige, lokal gespeicherte Zeichnung überschreiben. Bist du dir sicher?\n\n(Wenn du deine lokale Zeichnung behalten möchtest, schließe stattdessen den Browser-Tab.)", "errorAddingToLibrary": "Das Element konnte nicht zur Bibliothek hinzugefügt werden", "errorRemovingFromLibrary": "Das Element konnte nicht aus der Bibliothek entfernt werden", "confirmAddLibrary": "Dies fügt {{numShapes}} Form(en) zu deiner Bibliothek hinzu. Bist du dir sicher?", "imageDoesNotContainScene": "Dieses Bild scheint keine Szenendaten zu enthalten. Hast Du das Einbetten der Szene während des Exports aktiviert?", "cannotRestoreFromImage": "Die Zeichnung konnte aus dieser Bilddatei nicht wiederhergestellt werden", "invalidSceneUrl": "Die Szene konnte nicht von der angegebenen URL importiert werden. Sie ist entweder fehlerhaft oder enthält keine gültigen Excalidraw JSON-Daten.", "resetLibrary": "Dieses löscht deine Bibliothek. Bist du sicher?", "removeItemsFromsLibrary": "{{count}} Element(e) aus der Bibliothek löschen?", "invalidEncryptionKey": "Verschlüsselungsschlüssel muss 22 Zeichen lang sein. Die Live-Zusammenarbeit ist deaktiviert.", "collabOfflineWarning": "<PERSON>ine Internetverbindung verfügbar.\nDeine Änderungen werden nicht gespeichert!"}, "errors": {"unsupportedFileType": "Nicht unterstützter Dateityp.", "imageInsertError": "Das Bild konnte nicht eingefügt werden. Versuche es später erneut...", "fileTooBig": "Die Datei ist zu groß. Die maximal zulässige Größe ist {{maxSize}}.", "svgImageInsertError": "SVG-Bild konnte nicht eingefügt werden. Das SVG-Markup sieht ungültig aus.", "failedToFetchImage": "Bild konnte nicht abgerufen werden.", "invalidSVGString": "Ungültige SVG.", "cannotResolveCollabServer": "<PERSON>nnte keine Verbindung zum Collab-Server herstellen. Bitte lade die Seite neu und versuche es erneut.", "importLibraryError": "Bibliothek konnte nicht geladen werden", "collabSaveFailed": "Keine Speicherung in der Backend-Datenbank möglich. Wenn die Probleme weiterhin bestehen, solltest Du Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.", "collabSaveFailed_sizeExceeded": "<PERSON>ine Speicherung in der Backend-Datenbank möglich, die Zeichenfläche scheint zu groß zu sein. Du solltest Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.", "imageToolNotSupported": "Bilder sind deaktiviert.", "brave_measure_text_error": {"line1": "<PERSON><PERSON><PERSON> so aus, als ob Du den Brave-Browser verwendest und die <bold>aggressive Blockierung von Fingerabdrücken</bold> aktiviert hast.", "line2": "Dies könnte dazu führen, dass die <bold>Textelemente</bold> in Ihren Zeichnungen zerstört werden.", "line3": "Wir empfehlen dringend, diese Einstellung zu deaktivieren. Da<PERSON> kannst Du <link>diesen Schritten</link> folgen.", "line4": "Wenn die Deaktivierung dieser Einstellung die fehlerhafte Anzeige von Textelementen nicht behebt, öffne bitte ein <issueLink>Ticket</issueLink> auf unserem GitHub oder schreibe uns auf <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "Einbettbare Elemente können der Bibliothek nicht hinzugefügt werden.", "iframe": "IFrame-Elemente können nicht zur Bibliothek hinzugefügt werden.", "image": "Unterstützung für das Hinzufügen von Bildern in die Bibliothek kommt bald!"}, "asyncPasteFailedOnRead": "Einfügen fehlgeschlagen (konnte aus der Zwischenablage des Systems nicht gelesen werden).", "asyncPasteFailedOnParse": "Einfügen fehlgeschlagen.", "copyToSystemClipboardFailed": "Ko<PERSON>ren in die Zwischenablage fehlgeschlagen."}, "toolBar": {"selection": "Auswahl", "image": "Bild einfügen", "rectangle": "<PERSON><PERSON><PERSON>", "diamond": "<PERSON><PERSON>", "ellipse": "Ellipse", "arrow": "Pfeil", "line": "<PERSON><PERSON>", "freedraw": "<PERSON><PERSON><PERSON><PERSON>", "text": "Text", "library": "Bibliothek", "lock": "Ausgewähltes Werkzeug nach Zeichnen aktiv lassen", "penMode": "Stift-Modus - Berührung verhindern", "link": "Link für ausgewählte Form hinzufügen / aktualisieren", "eraser": "<PERSON><PERSON><PERSON>", "frame": "Rahmenwerkzeug", "magicframe": "Wireframe zu Code", "embeddable": "Web-Einbettung", "laser": "<PERSON><PERSON><PERSON><PERSON>", "hand": "Hand (Schwenkwerkzeug)", "extraTools": "Weitere Werkzeuge", "mermaidToExcalidraw": "Mermaid zu <PERSON><PERSON><PERSON><PERSON>", "magicSettings": "KI-Einstellungen"}, "headings": {"canvasActions": "Aktionen für Zeichenfläche", "selectedShapeActions": "Aktionen für Auswahl", "shapes": "Formen"}, "hints": {"canvasPanning": "Um die Zeichenfläche zu verschieben, halte das Mausrad oder die Leertaste während des Ziehens, oder verwende das Hand-Werkzeug", "linearElement": "<PERSON>licken für Linie mit mehreren Punkten, <PERSON><PERSON><PERSON> für einzelne Linie", "freeDraw": "<PERSON><PERSON><PERSON> und ziehe. <PERSON><PERSON>, wenn du fertig bist", "text": "Tipp: Du kannst auch Text hinzufügen, indem du mit dem Auswahlwerkzeug auf eine beliebige Stelle doppelklickst", "embeddable": "<PERSON><PERSON><PERSON> und ziehen, um eine Webseiten-Einbettung zu erstellen", "text_selected": "Doppelklicken oder Eingabetaste drücken, um Text zu bearbeiten", "text_editing": "Drücke Escape oder CtrlOrCmd+Eingabetaste, um die Bearbeitung abzuschließen", "linearElementMulti": "Zum Beenden auf den letzten Punkt klicken oder Escape oder Eingabe drücken", "lockAngle": "Du kannst Winkel e<PERSON>chränken, indem du SHIFT gedrückt hältst", "resize": "Du kannst die Proportionen einschränken, indem du SHIFT während der Größenänderung gedrückt hältst. Halte ALT gedrückt, um die Größe vom Zentrum aus zu ändern", "resizeImage": "Du kannst die Größe frei ändern, indem du SHIFT gedr<PERSON>t hältst; halte ALT, um die Größe vom Zentrum aus zu ändern", "rotate": "Du kannst Winkel e<PERSON>chränken, indem du SHIFT während der Drehung gedrückt hältst", "lineEditor_info": "CtrlOrCmd halten und Doppelklick oder CtrlOrCmd + Eingabe drücken, um Punkte zu bearbeiten", "lineEditor_pointSelected": "<PERSON><PERSON><PERSON>, um Punkt(e) zu entfernen, CtrlOrCmd+D zum Duplizieren oder ziehe zum Verschieben", "lineEditor_nothingSelected": "Wähle einen zu bearbeitenden Punkt (halte SHIFT gedrückt um mehrere Punkte auszuwählen),\noder halte Alt gedrückt und klicke um neue Punkte hinzuzufügen", "placeImage": "<PERSON><PERSON><PERSON>, um das Bild zu platzieren oder klicken und ziehen um seine Größe manuell zu setzen", "publishLibrary": "Veröffentliche deine eigene Bibliothek", "bindTextToElement": "Zum Hinzufügen Eingabetaste drücken", "deepBoxSelect": "Halte CtrlOrCmd gedrückt, um innerhalb der Gruppe auszuwählen, und um Ziehen zu vermeiden", "eraserRevert": "Halte Alt gedrückt, um die zum Löschen markierten Elemente zurückzusetzen", "firefox_clipboard_write": "Diese Funktion kann wahrscheinlich aktiviert werden, indem die Einstellung \"dom.events.asyncClipboard.clipboardItem\" auf \"true\" gesetzt wird. Um die Browsereinstellungen in Firefox zu ändern, besuche die Seite \"about:config\".", "disableSnapping": "Halte CtrlOrCmd gedr<PERSON>t, um das Einrasten zu deaktivieren"}, "canvasError": {"cannotShowPreview": "Vors<PERSON>u kann nicht angezeigt werden", "canvasTooBig": "Die Leinwand ist möglicherweise zu groß.", "canvasTooBigTip": "Tipp: <PERSON><PERSON><PERSON><PERSON> die am weitesten entfernten Elemente ein wenig näher zusammen."}, "errorSplash": {"headingMain": "Es ist ein Fehler aufgetreten. Versuche <button>die Seite neu zu laden.</button>", "clearCanvasMessage": "<PERSON>n das Neuladen nicht funktioniert, versuche <button>die Zeichenfläche zu löschen.</button>", "clearCanvasCaveat": " Dies wird zum Verlust von Daten führen ", "trackedToSentry": "<PERSON> Fehler mit der Kennung {{eventId}} wurde in unserem System registriert.", "openIssueMessage": "Wir waren sehr vorsichtig und haben deine Zeichnungsinformationen nicht in die Fehlerinformationen aufgenommen. Wenn deine Zeichnung nicht privat ist, unterstütze uns bitte über unseren <button>Bug-Tracker</button>. Bitte teile die unten stehenden Informationen mit uns im GitHub Issue (Kopieren und Einfügen).", "sceneContent": "Zeichnungsinhalt:"}, "roomDialog": {"desc_intro": "Du kannst Leute zu deiner aktuellen Zeichnung einladen um mit ihnen zusammenzuarbeiten.", "desc_privacy": "<PERSON><PERSON>, die Sitzung nutzt eine Ende-zu-Ende-Verschlüsselung. <PERSON><PERSON> was du z<PERSON><PERSON><PERSON><PERSON>, bleibt privat. Auch unser Server sieht nicht, was du dir einfallen lässt.", "button_startSession": "Sitzung starten", "button_stopSession": "<PERSON><PERSON><PERSON> beenden", "desc_inProgressIntro": "Die Live-Sitzung wird nun ausgeführt.", "desc_shareLink": "<PERSON><PERSON> diesen Link mit allen, mit denen du zusammenarbeiten möchtest:", "desc_exitSession": "Wenn du die Sitzung beendest, wird deine Verbindung zum Raum getrennt. Du kannst jedoch lokal weiter an der Zeichnung arbeiten. <PERSON><PERSON>, dass dies keine Auswirkungen auf andere hat und diese weiterhin gemeinsam an ihrer Version arbeiten können.", "shareTitle": "An einer Live-Kollaborationssitzung auf Excalidraw teilnehmen"}, "errorDialog": {"title": "<PERSON><PERSON>"}, "exportDialog": {"disk_title": "Auf Festplatte speichern", "disk_details": "Exportiere die Zeichnungsdaten in eine Datei, die Du später importieren kannst.", "disk_button": "Als Datei speichern", "link_title": "<PERSON><PERSON><PERSON><PERSON>", "link_details": "Als schreibgeschützten Link exportieren.", "link_button": "Als Link exportieren", "excalidrawplus_description": "Speichere die Szene in deinem Excalidraw+ Arbeitsbereich.", "excalidrawplus_button": "Exportieren", "excalidrawplus_exportError": "Konnte nicht nach Excalidraw+ exportieren..."}, "helpDialog": {"blog": "Lies unseren Blog", "click": "klicken", "deepSelect": "Auswahl innerhalb der Gruppe", "deepBoxSelect": "Auswahl innerhalb der Gruppe, und Ziehen vermeiden", "curvedArrow": "<PERSON><PERSON><PERSON><PERSON>", "curvedLine": "<PERSON>eb<PERSON><PERSON>", "documentation": "Dokumentation", "doubleClick": "doppelklicken", "drag": "ziehen", "editor": "Editor", "editLineArrowPoints": "<PERSON><PERSON>-/<PERSON><PERSON>il-<PERSON>te bearbeiten", "editText": "Text bearbeiten / Label hinzufügen", "github": "Ein Problem gefunden? Informiere uns", "howto": "Folge unseren Anleitungen", "or": "oder", "preventBinding": "Pfeil-Bindung verhindern", "tools": "Werkzeuge", "shortcuts": "Tastaturkürzel", "textFinish": "<PERSON><PERSON><PERSON><PERSON> (Texteditor)", "textNewLine": "Neue Zeile <PERSON> (Texteditor)", "title": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "zoomToFit": "Zoomen um alle Elemente einzupassen", "zoomToSelection": "<PERSON><PERSON> zoomen", "toggleElementLock": "Auswahl sperren/entsperren", "movePageUpDown": "Seite nach oben/unten verschieben", "movePageLeftRight": "Seite nach links/rechts verschieben"}, "clearCanvasDialog": {"title": "Zeichenfläche löschen"}, "publishDialog": {"title": "Bibliothek veröffentlichen", "itemName": "Elementname", "authorName": "Name des Autors", "githubUsername": "GitHub-Benutzername", "twitterUsername": "Twitter-Benutzername", "libraryName": "Name der Bibliothek", "libraryDesc": "Beschreibung der Bibliothek", "website": "Webseite", "placeholder": {"authorName": "<PERSON><PERSON> oder Benutzername", "libraryName": "Name deiner Bibliothek", "libraryDesc": "Beschreibung deiner Bibliothek, um anderen Nutzern bei der Verwendung zu helfen", "githubHandle": "GitHub<PERSON><PERSON><PERSON> (optional), damit du die Bibliothek bearbeiten kannst, wenn sie zur Überprüfung eingereicht wurde", "twitterHandle": "Twitter-Ben<PERSON><PERSON><PERSON> (optional), damit wir wissen, wen wir bei Werbung über Twitter nennen können", "website": "<PERSON> zu deiner persönlichen Webseite oder zu anderer Seite (optional)"}, "errors": {"required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "website": "Gültige URL eingeben"}, "noteDescription": "Sende deine Bibliothek ein, um in die <link>öffentliche Bibliotheks-Repository aufgenommen zu werden</link>damit andere <PERSON>utzer sie in ihren Zeichnungen verwenden können.", "noteGuidelines": "Die Bibliothek muss zuerst manuell freigegeben werden. Bitte lies die <link>Richtlinien</link> vor dem Absenden. Du benötigst ein GitHub-Konto, um zu kommunizieren und Änderungen vorzunehmen, falls erforderlich, aber es ist nicht unbedingt erforderlich.", "noteLicense": "Mit dem Absenden stimmst du zu, dass die Bibliothek unter der <link>MIT-Lizenz, </link>die zusammengefasst beinhaltet, dass jeder sie ohne Einschränkungen nutzen kann.", "noteItems": "Jedes Bibliothekselement muss einen eigenen Namen haben, damit es gefiltert werden kann. Die folgenden Bibliothekselemente werden hinzugefügt:", "atleastOneLibItem": "Bitte wähle mindestens ein Bibliothekselement aus, um zu beginnen", "republishWarning": "Hinweis: Einige der ausgewählten Elemente sind bereits als veröffentlicht/eingereicht markiert. Du solltest Elemente nur erneut einreichen, wenn Du eine existierende Bibliothek oder Einreichung aktualisierst."}, "publishSuccessDialog": {"title": "Bibliothek übermittelt", "content": "Vielen Dank {{authorName}}. Deine Bibliothek wurde zur Überprüfung eingereicht. Du kannst den Status verfolgen<link>hier</link>"}, "confirmDialog": {"resetLibrary": "Bibliothek zurücksetzen", "removeItemsFromLib": "Ausgewählte Elemente aus der Bibliothek entfernen"}, "imageExportDialog": {"header": "Bild exportieren", "label": {"withBackground": "Hi<PERSON>grund", "onlySelected": "Nur ausgewählte", "darkMode": "Dunkler Modus", "embedScene": "<PERSON><PERSON><PERSON> ein<PERSON>ten", "scale": "Skalierung", "padding": "Abstand"}, "tooltip": {"embedScene": "Die Zeichnungsdaten werden in der exportierten PNG/SVG-Date<PERSON> g<PERSON>ichert, sodass das Dokument später weiter bearbeitet werden kann. \nDieses wird die exportierte Datei vergrößern."}, "title": {"exportToPng": "Als PNG exportieren", "exportToSvg": "Als SVG exportieren", "copyPngToClipboard": "PNG in die Zwischenablage kopieren"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "In Zwischenablage kopieren"}}, "encrypted": {"tooltip": "Da deine Zeichnungen Ende-zu-<PERSON><PERSON> verschlü<PERSON>t werden, sehen auch unsere Excalidraw-<PERSON> sie ni<PERSON>.", "link": "Blogbeitrag über Ende-zu-Ende-Verschlüsselung in Excalidraw"}, "stats": {"angle": "<PERSON><PERSON>", "element": "Element", "elements": "Elemente", "height": "<PERSON><PERSON><PERSON>", "scene": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected": "Ausgewählt", "storage": "<PERSON><PERSON><PERSON><PERSON>", "title": "Statistiken für Nerds", "total": "Gesamt", "version": "Version", "versionCopy": "Zum Kopieren klicken", "versionNotAvailable": "Version nicht verfügbar", "width": "Breite"}, "toast": {"addedToLibrary": "Zur Bibliothek hinzugefügt", "copyStyles": "Formatierungen kopiert.", "copyToClipboard": "In die Zwischenablage kopiert.", "copyToClipboardAsPng": "{{exportSelection}} als PNG in die Zwischenablage kopiert\n({{exportColorScheme}})", "fileSaved": "<PERSON>i g<PERSON>.", "fileSavedToFilename": "Als {filename} gespeichert", "canvas": "Zeichenfläche", "selection": "Auswahl", "pasteAsSingleElement": "Verwende {{shortcut}} , um als einzelnes Element\neinzufügen oder in einen existierenden Texteditor einzufügen", "unableToEmbed": "<PERSON><PERSON><PERSON> dieser URL ist derzeit nicht zulässig. <PERSON><PERSON><PERSON> einen Issue auf GitHub, um die URL freigeben zu lassen", "unrecognizedLinkFormat": "<PERSON>, den Du e<PERSON>bettet hast, stimmt nicht mit dem erwarteten Format überein. Bitte versuche den 'embed' String ein<PERSON><PERSON><PERSON><PERSON>, der von der Quellseite zur Verfügung gestellt wird"}, "colors": {"transparent": "Transparent", "black": "<PERSON><PERSON><PERSON>", "white": "<PERSON><PERSON>", "red": "Rot", "pink": "Pink", "grape": "<PERSON><PERSON><PERSON>", "violet": "<PERSON><PERSON>", "gray": "G<PERSON><PERSON>", "blue": "Blau", "cyan": "<PERSON><PERSON>", "teal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "green": "<PERSON><PERSON><PERSON><PERSON>", "yellow": "<PERSON><PERSON><PERSON>", "orange": "Orange", "bronze": "Bronze"}, "welcomeScreen": {"app": {"center_heading": "Alle Daten werden lokal in Deinem Browser gespeichert.", "center_heading_plus": "Möchtest du stattdessen zu Excalidraw+ gehen?", "menuHint": "Exportieren, Einstellungen, Sprachen, ..."}, "defaults": {"menuHint": "Exportieren, Einstellungen und mehr...", "center_heading": "Diagramme. Einfach. Gemacht.", "toolbarHint": "Wähle ein Werkzeug & beginne zu zeichnen!", "helpHint": "Kurzbefehle & Hilfe"}}, "colorPicker": {"mostUsedCustomColors": "Beliebteste benutzerdefinierte Farben", "colors": "<PERSON><PERSON>", "shades": "Sc<PERSON>tierungen", "hexCode": "Hex-Code", "noShades": "<PERSON><PERSON> Schattierungen für diese Farbe verfügbar"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Als Bild exportieren", "button": "Als Bild exportieren", "description": "Exportiere die Zeichnungsdaten als ein Bild, von dem <PERSON> später importieren kannst."}, "saveToDisk": {"title": "Auf Festplatte speichern", "button": "Auf Festplatte speichern", "description": "Exportiere die Zeichnungsdaten in eine Datei, von der Du später importieren kannst."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Export nach Excalidraw+", "description": "Speichere die Szene in deinem Excalidraw+-Arbeitsbereich."}}, "modal": {"loadFromFile": {"title": "<PERSON>s Datei laden", "button": "<PERSON>s Datei laden", "description": "Das Laden aus einer Datei wird <bold><PERSON><PERSON> vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst <PERSON><PERSON> Zeichnung zu<PERSON>t mit einer der folgenden Optionen sichern."}, "shareableLink": {"title": "Aus Link laden", "button": "Meinen Inhalt ersetzen", "description": "Das Laden einer externen Zeichnung wird <bold><PERSON><PERSON> vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst <PERSON><PERSON> Zeichnung zu<PERSON>t mit einer der folgenden Optionen sichern."}}}, "mermaid": {"title": "Mermaid zu <PERSON><PERSON><PERSON><PERSON>", "button": "Einfügen", "description": "Derzeit werden nur <flowchartLink>Flussdiagramme</flowchartLink>, <sequenceLink>Sequenzdiagramme</sequenceLink> und <classLink>Klassendiagramme</classLink> unterstützt. Die anderen Typen werden als Bild in Excalidraw dargestellt.", "syntax": "Mermaid-Syntax", "preview": "Vorschau"}}