﻿import React, { useState } from "react";
import { GoogleSlidesViewer } from "./GoogleSlidesViewer";

interface GoogleSlidesButtonProps {
  className?: string;
  style?: React.CSSProperties;
}

export const GoogleSlidesButton: React.FC<GoogleSlidesButtonProps> = ({
  className = "",
  style = {}
}) => {
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const defaultStyle: React.CSSProperties = {
    background: "#4285f4",
    color: "white",
    border: "none",
    borderRadius: "6px",
    padding: "8px 12px",
    cursor: "pointer",
    fontSize: "14px",
    fontWeight: "500",
    display: "flex",
    alignItems: "center",
    gap: "6px",
    transition: "all 0.2s ease",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    ...style
  };

  const handleClick = () => {
    setIsViewerOpen(true);
  };

  const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.background = "#3367d6";
    e.currentTarget.style.transform = "translateY(-1px)";
    e.currentTarget.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.15)";
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.background = "#4285f4";
    e.currentTarget.style.transform = "translateY(0)";
    e.currentTarget.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.1)";
  };

  return (
    <>
      <button
        className={`google-slides-button ${className}`}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={defaultStyle}
        title="Open Google Slides Integration"
      >
         Google Slides
      </button>
      
      <GoogleSlidesViewer 
        isVisible={isViewerOpen}
        onClose={() => setIsViewerOpen(false)}
      />
    </>
  );
};
