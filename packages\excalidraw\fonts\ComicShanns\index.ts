// The following file content was generated with https://chinese-font.netlify.app/online-split,
// but has been manully rewritten from `@font-face` rules into TS while leveraging FontFace API.

import { type ExcalidrawFontFaceDescriptor } from "../Fonts";

import _0 from "./ComicShanns-Regular-279a7b317d12eb88de06167bd672b4b4.woff2";
import _3 from "./ComicShanns-Regular-6e066e8de2ac57ea9283adb9c24d7f0c.woff2";
import _2 from "./ComicShanns-Regular-dc6a8806fa96795d7b3be5026f989a17.woff2";
import _1 from "./ComicShanns-Regular-fcb0fc02dcbee4c9846b3e2508668039.woff2";

/* Generated By cn-font-split@5.2.2 https://www.npmjs.com/package/cn-font-split
CreateTime: Thu, 17 Oct 2024 09:57:51 GMT;
Origin File Name Table:
copyright: MIT License

Copyright (c) 2018 Shannon Miwa
Copyright (c) 2023 Jesus <PERSON>
Copyright (c) 2023 Rodrigo Bat<PERSON> Moraes
Copyright (c) 2024 Fini J<PERSON>
Copyright (c) 2024 <PERSON>mission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
fontFamily: Comic Shanns Mono-Regular
fontSubfamily: Regular
uniqueID: FontForge 2.0 : Comic Shanns Mono Regular : 17-10-2024
fullName: Comic Shanns Mono Regular
version: 1.3.0
postScriptName: ComicShannsMono-Regular
license: MIT License

Copyright (c) 2018 Shannon Miwa
Copyright (c) 2023 Jesus Gonzalez

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
preferredFamily: Comic Shanns Mono
 */

export const ComicShannsFontFaces: ExcalidrawFontFaceDescriptor[] = [
  {
    uri: _0,
    descriptors: {
      unicodeRange:
        "U+20-7e,U+a1-a6,U+a8,U+ab-ac,U+af-b1,U+b4,U+b8,U+bb-bc,U+bf-cf,U+d1-d7,U+d9-de,U+e0-ef,U+f1-f7,U+f9-ff,U+131,U+152-153,U+2c6,U+2da,U+2dc,U+2013-2014,U+2018-201a,U+201c-201d,U+2020-2022,U+2026,U+2039-203a,U+2044,U+20ac,U+2191,U+2193,U+2212",
    },
  },
  {
    uri: _1,
    descriptors: {
      unicodeRange:
        "U+100-10f,U+112-125,U+128-130,U+134-137,U+139-13c,U+141-148,U+14c-151,U+154-161,U+164-165,U+168-17f,U+1bf,U+1f7,U+218-21b,U+237,U+1e80-1e85,U+1ef2-1ef3,U+a75b",
    },
  },
  {
    uri: _2,
    descriptors: {
      unicodeRange:
        "U+2c7,U+2d8-2d9,U+2db,U+2dd,U+315,U+2190,U+2192,U+2200,U+2203-2204,U+2264-2265,U+f6c3",
    },
  },
  {
    uri: _3,
    descriptors: { unicodeRange: "U+3bb" },
  },
];
