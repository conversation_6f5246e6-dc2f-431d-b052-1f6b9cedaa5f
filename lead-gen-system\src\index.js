#!/usr/bin/env node

import express from 'express';
import { config, validateConfig } from './config/index.js';
import { logger } from './utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

class LeadGenerationSystem {
  constructor() {
    this.app = express();
    this.port = config.port;
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // Basic middleware
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Request logging
    this.app.use((req, res, next) => {
      logger.http(`${req.method} ${req.url}`);
      next();
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    });

    // System status endpoint
    this.app.get('/status', async (req, res) => {
      try {
        const status = await this.getSystemStatus();
        res.json(status);
      } catch (error) {
        logger.error('Error getting system status:', error);
        res.status(500).json({ error: 'Failed to get system status' });
      }
    });

    // Data collection endpoints
    this.app.post('/api/collect/mats', async (req, res) => {
      try {
        logger.info('Starting MAT data collection via API');
        // This would trigger the data collection process
        res.json({ message: 'MAT data collection started', status: 'processing' });
      } catch (error) {
        logger.error('Error starting MAT collection:', error);
        res.status(500).json({ error: 'Failed to start MAT collection' });
      }
    });

    // Configuration check endpoint
    this.app.get('/api/config/check', (req, res) => {
      const configStatus = this.checkConfiguration();
      res.json(configStatus);
    });

    // Default route
    this.app.get('/', (req, res) => {
      res.json({
        message: 'UK Schools Lead Generation System',
        version: '1.0.0',
        endpoints: [
          'GET /health - Health check',
          'GET /status - System status',
          'GET /api/config/check - Configuration check',
          'POST /api/collect/mats - Start MAT data collection'
        ]
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Endpoint not found' });
    });

    // Error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error:', error);
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  async getSystemStatus() {
    const status = {
      system: 'UK Schools Lead Generation',
      status: 'running',
      timestamp: new Date().toISOString(),
      configuration: this.checkConfiguration(),
      data_files: await this.checkDataFiles(),
      services: await this.checkServices()
    };

    return status;
  }

  checkConfiguration() {
    const requiredVars = [
      'COMPANIES_HOUSE_API_KEY',
      'APOLLO_API_KEY',
      'HUNTER_API_KEY',
      'HUBSPOT_API_KEY',
      'SENDGRID_API_KEY',
      'OPENAI_API_KEY'
    ];

    const configStatus = {
      valid: true,
      missing: [],
      present: []
    };

    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        configStatus.present.push(varName);
      } else {
        configStatus.missing.push(varName);
        configStatus.valid = false;
      }
    });

    return configStatus;
  }

  async checkDataFiles() {
    const dataDir = './data';
    const expectedFiles = [
      'mats_basic.json',
      'mats_detailed.json',
      'collection_summary.json'
    ];

    const fileStatus = {
      data_directory_exists: false,
      files: {}
    };

    try {
      await fs.access(dataDir);
      fileStatus.data_directory_exists = true;

      for (const file of expectedFiles) {
        try {
          const stats = await fs.stat(path.join(dataDir, file));
          fileStatus.files[file] = {
            exists: true,
            size: stats.size,
            modified: stats.mtime
          };
        } catch {
          fileStatus.files[file] = { exists: false };
        }
      }
    } catch {
      fileStatus.data_directory_exists = false;
    }

    return fileStatus;
  }

  async checkServices() {
    const services = {
      companies_house: { status: 'unknown', message: 'Not tested' },
      apollo: { status: 'unknown', message: 'Not tested' },
      hunter: { status: 'unknown', message: 'Not tested' },
      hubspot: { status: 'unknown', message: 'Not tested' },
      sendgrid: { status: 'unknown', message: 'Not tested' },
      openai: { status: 'unknown', message: 'Not tested' }
    };

    // In a production system, you would test each service here
    // For now, we'll just check if API keys are present
    if (config.companiesHouse.apiKey) {
      services.companies_house = { status: 'configured', message: 'API key present' };
    }
    if (config.apollo.apiKey) {
      services.apollo = { status: 'configured', message: 'API key present' };
    }
    if (config.hunter.apiKey) {
      services.hunter = { status: 'configured', message: 'API key present' };
    }
    if (config.hubspot.apiKey) {
      services.hubspot = { status: 'configured', message: 'API key present' };
    }
    if (config.sendgrid.apiKey) {
      services.sendgrid = { status: 'configured', message: 'API key present' };
    }
    if (config.openai.apiKey) {
      services.openai = { status: 'configured', message: 'API key present' };
    }

    return services;
  }

  async start() {
    // Validate configuration before starting
    if (!validateConfig()) {
      logger.error('❌ Configuration validation failed. Please check your .env file.');
      process.exit(1);
    }

    // Create necessary directories
    await this.createDirectories();

    // Start the server
    this.app.listen(this.port, () => {
      logger.info(`🚀 Lead Generation System started on port ${this.port}`);
      logger.info(`📊 Dashboard: http://localhost:${this.port}`);
      logger.info(`🔍 Status: http://localhost:${this.port}/status`);
      logger.info(`⚙️  Config Check: http://localhost:${this.port}/api/config/check`);
    });
  }

  async createDirectories() {
    const directories = ['./data', './logs'];
    
    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        logger.error(`Error creating directory ${dir}:`, error);
      }
    }
  }
}

// Main execution
async function main() {
  logger.info('🎯 Initializing UK Schools Lead Generation System...');
  
  const system = new LeadGenerationSystem();
  await system.start();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('👋 Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('👋 Shutting down gracefully...');
  process.exit(0);
});

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logger.error('❌ Failed to start system:', error);
    process.exit(1);
  });
}
