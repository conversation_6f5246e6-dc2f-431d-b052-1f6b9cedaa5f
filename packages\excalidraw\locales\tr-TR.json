{"labels": {"paste": "Yapıştır", "pasteAsPlaintext": "<PERSON><PERSON><PERSON> metin olarak ya<PERSON>ı<PERSON>ır", "pasteCharts": "Grafik<PERSON>i ya<PERSON>ıştır", "selectAll": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "multiSelect": "Seçime öge ekle", "moveCanvas": "Tuvali taşı", "cut": "<PERSON><PERSON>", "copy": "Kopyala", "copyAsPng": "Panoya PNG olarak kopyala", "copyAsSvg": "Panoya SVG olarak kopyala", "copyText": "<PERSON><PERSON> metin o<PERSON>ak k<PERSON>", "copySource": "", "convertToCode": "", "bringForward": "Bir öne getir", "sendToBack": "Ark<PERSON> g<PERSON>", "bringToFront": "En öne getir", "sendBackward": "<PERSON>ir geriye gönder", "delete": "Sil", "copyStyles": "<PERSON><PERSON> k<PERSON>", "pasteStyles": "<PERSON><PERSON>ı<PERSON>", "stroke": "Vurgu", "background": "Arka plan", "fill": "<PERSON><PERSON><PERSON>", "strokeWidth": "<PERSON><PERSON><PERSON> genişliği", "strokeStyle": "<PERSON><PERSON><PERSON> s<PERSON>", "strokeStyle_solid": "Do<PERSON>", "strokeStyle_dashed": "Kesik çizgili", "strokeStyle_dotted": "Noktalı", "sloppiness": "Üstün körülük", "opacity": "Opaklık", "textAlign": "<PERSON><PERSON>", "edges": "<PERSON><PERSON><PERSON>", "sharp": "<PERSON><PERSON>", "round": "<PERSON><PERSON><PERSON>", "arrowheads": "Ok uçları", "arrowhead_none": "Yok", "arrowhead_arrow": "Ok", "arrowhead_bar": "<PERSON><PERSON><PERSON>", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Üçgen", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Ya<PERSON><PERSON> tipi boyutu", "fontFamily": "Yazı tipi ailesi", "addWatermark": "\"Excalidraw ile yapıldı\" yazısını ekle", "handDrawn": "El-yazısı", "normal": "Normal", "code": "Kod", "small": "Küçük", "medium": "Orta", "large": "Büyük", "veryLarge": "Çok geniş", "solid": "Do<PERSON>", "hachure": "Taralı", "zigzag": "<PERSON><PERSON><PERSON>", "crossHatch": "Çapraz-taralı", "thin": "İnce", "bold": "Kalı<PERSON>", "left": "Sol", "center": "<PERSON><PERSON><PERSON>", "right": "Sağ", "extraBold": "Ekstra kalın", "architect": "<PERSON><PERSON>", "artist": "Sanatçı", "cartoonist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileTitle": "<PERSON><PERSON><PERSON> adı", "colorPicker": "Renk seçici", "canvasColors": "<PERSON><PERSON>in üzerinde kullanıldı", "canvasBackground": "Tuval arka planı", "drawingCanvas": "<PERSON><PERSON><PERSON>", "layers": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "language": "Dil", "liveCollaboration": "Canlı ortak çalışma alanı...", "duplicateSelection": "Çoğalt", "untitled": "Adsız", "name": "İsim", "yourName": "<PERSON><PERSON><PERSON><PERSON>", "madeWithExcalidraw": "Excalidraw ile yapıldı", "group": "Seçimi grup yap", "ungroup": "Seçilen gru<PERSON>", "collaborators": "Ortaklar", "showGrid": "Izgarayı göster", "addToLibrary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeFromLibrary": "Kütüphaneden kaldır", "libraryLoadingMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "libraries": "Kütüphanelere gözat", "loadingScene": "<PERSON><PERSON><PERSON>…", "align": "<PERSON><PERSON><PERSON>", "alignTop": "Yu<PERSON>ı hizala", "alignBottom": "Aşağı hizala", "alignLeft": "<PERSON>a hizala", "alignRight": "<PERSON><PERSON><PERSON>", "centerVertically": "<PERSON><PERSON><PERSON> ortala", "centerHorizontally": "<PERSON><PERSON><PERSON><PERSON> ortala", "distributeHorizontally": "<PERSON><PERSON><PERSON>", "distributeVertically": "<PERSON><PERSON>", "flipHorizontal": "<PERSON><PERSON><PERSON>", "flipVertical": "<PERSON><PERSON>", "viewMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modu", "share": "Paylaş", "showStroke": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> renk seçiciyi göster", "showBackground": "Arkaplan için renk seçiciyi göster", "toggleTheme": "Temayı etkinleştir/devre dışı bırak", "personalLib": "<PERSON><PERSON><PERSON>l Kit<PERSON>lık", "excalidrawLib": "Excalidraw Kitaplığı", "decreaseFontSize": "Yazı Tipi Boyutunu Küçült", "increaseFontSize": "Yazı Tipi Boyutunu Büyült", "unbindText": "<PERSON><PERSON>", "bindText": "<PERSON>ni taşıyıcıya bağla", "createContainerFromText": "<PERSON>ni bi<PERSON>en içinde sar", "link": {"edit": "Bağlantıyı düzenle", "editEmbed": "Bağlantıyı düzenle & yerleştir", "create": "Bağlantı oluştur", "createEmbed": "Bağlantı oluştur & yerleştir", "label": "Bağlantı", "labelEmbed": "Bağlantı & yerleştirme", "empty": "Herhangi bir bağlantı oluşturulmadı"}, "lineEditor": {"edit": "Çizgiyi düzenle", "exit": "Çizgi düzenlemeden çık"}, "elementLock": {"lock": "<PERSON><PERSON><PERSON>", "unlock": "<PERSON><PERSON><PERSON>", "lockAll": "<PERSON><PERSON><PERSON> kilitle", "unlockAll": "<PERSON><PERSON><PERSON><PERSON> kilidini kaldır"}, "statusPublished": "Yayınlandı", "sidebarLock": "<PERSON>ar <PERSON>ğu açık kalsın", "selectAllElementsInFrame": "Çerçevedeki tüm bileşenleri seç", "removeAllElementsFromFrame": "Çerçevedeki tüm bileşenleri sil", "eyeDropper": "Tu<PERSON><PERSON> renk seç", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "<PERSON><PERSON><PERSON>...", "hint_emptyLibrary": "Öğelerden birini eklemek için öğeyi seçiniz veya aşağıdaki genel kütüphaneden öğeleri ekleyin.", "hint_emptyPrivateLibrary": "Tuvalden bir eleman seçerek sayfaya e<PERSON>."}, "buttons": {"clearReset": "<PERSON><PERSON><PERSON> sı<PERSON>ı<PERSON>a", "exportJSON": "Dosyaya aktar", "exportImage": "Resimleri dışa aktar...", "export": "Şuraya kaydet...", "copyToClipboard": "<PERSON><PERSON> k<PERSON>", "save": "Geçerli dos<PERSON>ya ka<PERSON>et", "saveAs": "Farklı kaydet", "load": "Aç", "getShareableLink": "Paylaşılabilir bağlantı al", "close": "Ka<PERSON><PERSON>", "selectLanguage": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "scrollBackToContent": "İçeriğe geri dön", "zoomIn": "Yakınlaştır", "zoomOut": "Uzaklaştır", "resetZoom": "Yakınlaştırmayı sıfırla", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON> yap", "resetLibrary": "Kü<PERSON>ü<PERSON><PERSON>i sıfırla", "createNewRoom": "<PERSON><PERSON> oda <PERSON>", "fullScreen": "<PERSON>", "darkMode": "<PERSON><PERSON> tema", "lightMode": "<PERSON><PERSON><PERSON>k tema", "zenMode": "Zen modu", "objectsSnapMode": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>a", "exitZenMode": "Zen modundan çık", "cancel": "İptal", "clear": "<PERSON><PERSON><PERSON>", "remove": "Kaldır", "embed": "", "publishLibrary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "embeddableInteractionButton": "Etkileşime girmek için tıkla"}, "alerts": {"clearReset": "<PERSON><PERSON><PERSON> tamamı temizlenecek. <PERSON><PERSON> misiniz?", "couldNotCreateShareableLink": "Paylaşılabilir bağlantı oluşturulamadı.", "couldNotCreateShareableLinkTooBig": "Paylaşılabilir bağlantı oluşturulamadı: sahne çok büyük", "couldNotLoadInvalidFile": "Bilinmeyen dosya yüklenemiyor", "importBackendFailed": "Sunucudan içe aktarma başarısız.", "cannotExportEmptyCanvas": "<PERSON><PERSON> tuval dışarıya aktarılamaz.", "couldNotCopyToClipboard": "Panoya kopyalanamıyor.", "decryptFailed": "Şifrelenmiş veri <PERSON>.", "uploadedSecurly": "Yükleme uçtan uca şifreleme ile korunmaktadır. Excalidraw sunucusu ve üçüncül şahıslar içeriği okuyamayacaktır.", "loadSceneOverridePrompt": "Harici çizimler yüklemek mevcut olan içeriği değiştirecektir. Devam etmek istiyor musunuz?", "collabStopOverridePrompt": "Oturumu sonlandırma<PERSON>, yerel olarak kaydedilmiş çizimin üzerine kaydedilmesine sebep ola<PERSON>k. Emin misiniz?\n\n(<PERSON><PERSON> çiz<PERSON> kaybetmemek için tarayıcı sekmesini kapatabilirsiniz.)", "errorAddingToLibrary": "<PERSON><PERSON><PERSON> eklenemedi", "errorRemovingFromLibrary": "<PERSON><PERSON><PERSON> k<PERSON>üpha<PERSON>en silinemedi", "confirmAddLibrary": "<PERSON><PERSON>, kitaplığınıza {{numShapes}} tane şekil ekley<PERSON>ek. Emin misiniz?", "imageDoesNotContainScene": "<PERSON>u görü<PERSON><PERSON> herhangi bir sahne verisi içermiyor gibi görünüyor. Dışa aktarma sırasında sahne yerleştirmeyi etkinleştirdiniz mi?", "cannotRestoreFromImage": "<PERSON><PERSON><PERSON> bu resim dos<PERSON>ından geri yü<PERSON>medi", "invalidSceneUrl": "Verilen bağlantıdan ç<PERSON>ışma alanı yüklenemedi. Dosya bozuk olabilir veya geçerli bir Excalidraw JSON verisi bulundurmuyor olabilir.", "resetLibrary": "Bu işlem kütüphanenizi sıfırlayacak. <PERSON><PERSON> misiniz?", "removeItemsFromsLibrary": "{{count}} öğ<PERSON>(ler) kitaplıktan kaldırılsın mı?", "invalidEncryptionKey": "Şifreleme anahtarı 22 karakter olmalı. Canlı işbirliği devre dışı bırakıldı.", "collabOfflineWarning": "İnternet bağlantısı bulunamadı. Değişiklikleriniz kaydedilmeyecek!"}, "errors": {"unsupportedFileType": "Desteklenmeyen dosya türü.", "imageInsertError": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>nemedi. <PERSON>ha sonra tekrar deneyin...", "fileTooBig": "Dosya çok büyük. İzin verilen maksimum boyut {{maxSize}}.", "svgImageInsertError": "SVG resmi eklenemedi. SVG işaretlemesi geçersiz görünüyor.", "failedToFetchImage": "", "invalidSVGString": "Geçersiz SVG.", "cannotResolveCollabServer": "İş birliği sunuc<PERSON>una bağlanılamıyor. Lütfen sayfayı yenileyip tekrar deneyin.", "importLibraryError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collabSaveFailed": "Backend veritabanına kaydedilemedi. Eğer problem devam ederse, çalışmanızı korumak için dosyayı yerel olarak kaydetmelisiniz.", "collabSaveFailed_sizeExceeded": "Backend veritabanına kaydedilemedi; tuval ç<PERSON> büyük. Çalışmanızı korumak için dosyayı yerel olarak kaydetmelisiniz.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": "Resimleri kütüphaneye ekleme desteği yakında geliyor!"}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "Seçme", "image": "<PERSON><PERSON><PERSON><PERSON>", "rectangle": "Dikdörtgen", "diamond": "<PERSON><PERSON>", "ellipse": "Elips", "arrow": "Ok", "line": "<PERSON><PERSON><PERSON>", "freedraw": "Çiz", "text": "Yazı", "library": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lock": "Seçilen aracı çizimden sonra aktif tut", "penMode": "Kalem modu - dokunmayı engelle", "link": "Seçilen şekil için b<PERSON>lantı Ekle/Güncelle", "eraser": "<PERSON><PERSON><PERSON>", "frame": "Çerçeve aracı", "magicframe": "", "embeddable": "Web Ye<PERSON>ştirme", "laser": "Lazer işaretçisi", "hand": "", "extraTools": "<PERSON><PERSON> fazla a<PERSON>", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "<PERSON><PERSON>", "selectedShapeActions": "Seçilen şekil aksiyonları", "shapes": "Şekiller"}, "hints": {"canvasPanning": "", "linearElement": "Birden fazla nokta için tı<PERSON>, tek çizgi için s<PERSON>", "freeDraw": "Tıkla ve sü<PERSON>ükle, bitirdiğinde serbest bırak", "text": "İpucu: seçme aracıyla herhangi bir yere çift tıklayarak da yazı ekleyebilirsin", "embeddable": "Web sitesi yerleştirmek için sürükle bırak", "text_selected": "Metni düzenlemek için çift tıklayın veya ENTER'a basın", "text_editing": "Düzenlemeyi bitirmek için ESC veya Ctrl/Cmd+ENTER tuşlarına basın", "linearElementMulti": "Bitirmek i<PERSON>in son noktaya tıklayın ya da Escape veya Enter tuşuna basın", "lockAngle": "SHIFT tuşuna basılı tutarak açıyı koruyabilirsiniz", "resize": "Yeniden boyutlandırırken SHIFT tuşunu basılı tutarak oranları sınırlayabilirsiniz,\nmerkezden yeniden boyutlandırmak için ALT tuşunu basılı tutun", "resizeImage": "SHIFT'e basılı tutarak serbestçe yeniden boyutlandırabilirsiniz, merkezden yeniden boyutlandırmak için ALT tuşunu basılı tutun", "rotate": "Döndürürken SHIFT tuşuna basılı tutarak açıları koruyabilirsiniz", "lineEditor_info": "Puanları düzenlemek için ctrl veya cmd tuşuna basılı tutup çift tıklayın veya enter tuşuna basın", "lineEditor_pointSelected": "Sil tuşuna basarak no<PERSON>alar<PERSON>,\nCtrl/Cmd + D ile <PERSON>, ya da sürükleyerek taşıyın", "lineEditor_nothingSelected": "Düzenlemek için bir nokta seçin (birden fazla seçmek için SHIFT tuşunu basılı tutun),\nveya Alt tuşunu basılı tutun ve yeni noktalar eklemek için tıklayın", "placeImage": "<PERSON><PERSON><PERSON>rmek için tıklayın ya da boyutunu manuel olarak ayarlamak için tıklayıp sürükleyin", "publishLibrary": "Kendi kitaplığınızı yayınlayın", "bindTextToElement": "Enter tu<PERSON><PERSON> basarak metin e<PERSON>in", "deepBoxSelect": "Ctrl/Cmd tuşuna basılı tutarak derin seçim yapın ya da sürüklemeyi engelleyin", "eraserRevert": "Alt tuşuna basılı tutarak silinme için işaretlenmiş ögeleri tersine çevirin", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Ö<PERSON>zleme gösterilemiyor", "canvasTooBig": "Kanvas çok büyük olabilir.", "canvasTooBigTip": "İpucu: En uzaktaki elemanları birbirine yakınlaştırmayı deneyin."}, "errorSplash": {"headingMain": "Hata oluştu. Lütfen <button>say<PERSON><PERSON>ı ye<PERSON><PERSON><PERSON> deney<PERSON>.</button>", "clearCanvasMessage": "<PERSON><PERSON><PERSON><PERSON> son<PERSON>ı sorun devam edi<PERSON>, lütfen <button>çizim alanını temizlemeyi deneyin.</button>", "clearCanvasCaveat": " B<PERSON>, yaptığınız değişiklikleri sıfırlayacak ", "trackedToSentry": "Tanı<PERSON>layıcı ile ilgili hata {{eventId}} sistemimize yakalandı.", "openIssueMessage": "Sahne bilginizi hata mesajına yansıtmamak için oldukça dikkatli davrandık. Eğer sahneniz gizli değilse hatayı lütfen şuradan takip edin <button>hata takibi.</button> Lütfen aşağıya GitHub sorununa kopyalayarak ve yapıştırarak bilgi ekleyin.", "sceneContent": "<PERSON><PERSON><PERSON> içeriği:"}, "roomDialog": {"desc_intro": "Çalışma <PERSON>, sizinle birlikte çalışabilmeleri için başkalarını da ekleyebilirsiniz.", "desc_privacy": "Çalışma ortamında yaptıklarınız ve çizimleriniz uçtan uca şifrelemeyle saklanmaktadır. Sunucularımız dahi bu verileri şifrelenmemiş haliyle göremez.", "button_startSession": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "button_stopSession": "<PERSON><PERSON><PERSON><PERSON>", "desc_inProgressIntro": "Ortak çalışma ortamı oluşturuldu.", "desc_shareLink": "Bu bağlantıyı birlikte çalışacağınız kişilerle paylaşabilirsiniz:", "desc_exitSession": "Çalışma ortamını kapattığınızda ortak çalışmadan ayrılmış olursunuz ancak kendi versiyonunuzda çalışmaya devam edebilirsiniz. Bu durumda ortak çalıştığınız diğer kişiler etkilenmeyecek, çalışma ortamındaki versiyon üzerinden çalışmaya devam edebilecekler.", "shareTitle": "Excalidraw'da canlı ortak calışma oturumuna katıl"}, "errorDialog": {"title": "<PERSON><PERSON>"}, "exportDialog": {"disk_title": "<PERSON><PERSON><PERSON> ka<PERSON>", "disk_details": "Sahne verilerini daha sonra içe aktarabileceğiniz bir dosyaya aktarın.", "disk_button": "Dosyaya kaydet", "link_title": "Paylaşılabilir bağlantı", "link_details": "Salt okunur bir bağlantı olarak dışa aktarın.", "link_button": "Bağlantı olarak dışa aktar", "excalidrawplus_description": "Sahneyi Excalidraw+ çalışma alanınıza kaydedin.", "excalidrawplus_button": "Dışa aktar", "excalidrawplus_exportError": "<PERSON><PERSON> anda <PERSON>+'a aktarılamadı..."}, "helpDialog": {"blog": "B<PERSON>'<PERSON><PERSON><PERSON>un", "click": "tıkla", "deepSelect": "<PERSON><PERSON>", "deepBoxSelect": "<PERSON><PERSON> i<PERSON> derin seç<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> engel<PERSON>", "curvedArrow": "Eğri ok", "curvedLine": "<PERSON><PERSON><PERSON>", "documentation": "Dokümantasyon", "doubleClick": "çift-tıklama", "drag": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor": "D<PERSON><PERSON>leyici", "editLineArrowPoints": "Çizgi/ok noktalarını düzenle", "editText": "Etiket / metin düzenle", "github": "Bir hata mı buldun? Bildir", "howto": "Rehberlerimizi takip edin", "or": "veya", "preventBinding": "Ok bağlamayı önleyin", "tools": "Araçlar", "shortcuts": "Klavye kısayolları", "textFinish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bitir (metin dü<PERSON>)", "textNewLine": "<PERSON><PERSON> (metin dü<PERSON>)", "title": "Yardım", "view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "zoomToFit": "<PERSON><PERSON>m öğeleri sığdırmak için yakınlaştır", "zoomToSelection": "Seçime yakınlaş", "toggleElementLock": "<PERSON><PERSON><PERSON><PERSON>/ç<PERSON>z", "movePageUpDown": "Sayfayı yukarı/aşağı kaydır", "movePageLeftRight": "Sayfayı sola/sağa kaydır"}, "clearCanvasDialog": {"title": "<PERSON><PERSON><PERSON> temi<PERSON>"}, "publishDialog": {"title": "Kitaplığı yayınla", "itemName": "<PERSON><PERSON><PERSON> adı", "authorName": "<PERSON><PERSON> adı", "githubUsername": "GıtHub kullanıcı adı", "twitterUsername": "Twitter kullanıcı adı", "libraryName": "Kitaplık adı", "libraryDesc": "Kitaplık açıklaması", "website": "Web sitesi", "placeholder": {"authorName": "Adınız ya da kullanıcı adınız", "libraryName": "Kitaplığınızın adı", "libraryDesc": "İnsanların kullanımını anlamasına yardımcı olmak için kitaplığınızın açıklaması", "githubHandle": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> ( tercihe bağlı), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gözden geçirme için onaylandığında düzenleyebiliesiniz diye", "twitterHandle": "Twitter kullanıcı adı ( tercihe bağlı), bu sayede Twitter üzerinde paylaşıren çalışmanızı size atfedebiliriz", "website": "Kişisel web sayfanızı ya da başka bir yeri bağlayın (tercihe bağlı)"}, "errors": {"required": "<PERSON><PERSON><PERSON><PERSON>", "website": "Geçerli bir URL girin"}, "noteDescription": "Submit your library to be included in the <link>genel kü<PERSON><PERSON><PERSON><PERSON> reposu</link>diğer insanlar çizimlerinde kullanabilsin diye.", "noteGuidelines": "Önce kütüphane elle on<PERSON>ı. <PERSON><PERSON><PERSON> okuyun <link><PERSON><PERSON><PERSON><PERSON><PERSON></link> onaylamadan önce. gerekli olması halinde iletişim kurmak için ve değişiklik için Github he<PERSON> gerek<PERSON>, ama çok da illaki olmalı değil.", "noteLicense": "<PERSON><PERSON><PERSON> on<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nin şu lisansla yayınlanmasını onaylıyorsunuz <link>MIT Lisans, </link>ki bu kısaca herkesin onu kısıtlama olmaksızın kullanabileceği anlamına gelmektedir.", "noteItems": "Her kütüphane kendi ismine sahip olmalı ki tarama yapa<PERSON>elim. <PERSON><PERSON> kütüphane ögeleri da<PERSON> edile<PERSON>k:", "atleastOneLibItem": "Lütfen başlamak için en az bir tane kütüphane öges<PERSON> seçin", "republishWarning": "Not: seçilen ögelerden bir kısmı zaten yayınlanmış/gönderilmiş. Yalnızca mevcut kütüphane ve gönderileri güncellerken yeniden gönderme işlemi yapmalısınız."}, "publishSuccessDialog": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "Teşekkürler {{authorName}}. Kütüphaneniz gözden geçirme için alındı. Durumu takip edebilirsiniz<link>burada</link>"}, "confirmDialog": {"resetLibrary": "Kü<PERSON>ü<PERSON><PERSON>i sıfırla", "removeItemsFromLib": "Seçilen ögeleri kütüphaneden kaldır"}, "imageExportDialog": {"header": "<PERSON><PERSON><PERSON> dı<PERSON> aktar", "label": {"withBackground": "Arka plan", "onlySelected": "<PERSON><PERSON><PERSON>", "darkMode": "Karanlık mod", "embedScene": "<PERSON><PERSON><PERSON>", "scale": "Ölçeklendir", "padding": "Dış boşluk"}, "tooltip": {"embedScene": "<PERSON><PERSON><PERSON>, sahn<PERSON>n geri yüklenebilmesi için dışarı aktarılan PNG/SVG dosyasına kaydedilecektir. Bu, dışa aktarılan dosya boyutunu arttıracaktır."}, "title": {"exportToPng": "PNG olarak dışa aktar", "exportToSvg": "SVG olarak dışa aktar", "copyPngToClipboard": ""}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "<PERSON><PERSON> k<PERSON>"}}, "encrypted": {"tooltip": "Çizimleriniz uçtan-uca şifrelenmiştir, Excalidraw'ın sunucuları bile onları göremez.", "link": "Excalidraw'da uçtan uca şifreleme hakkında blog yazısı"}, "stats": {"angle": "Açı", "element": "Bileşen", "elements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "height": "Yükseklik", "scene": "<PERSON><PERSON><PERSON>", "selected": "Seçili", "storage": "<PERSON><PERSON><PERSON>", "title": "<PERSON>nekle<PERSON> i<PERSON><PERSON> istat<PERSON>", "total": "Toplam", "version": "S<PERSON>r<PERSON><PERSON>", "versionCopy": "Kopyalamak için tıkla", "versionNotAvailable": "<PERSON><PERSON><PERSON><PERSON><PERSON> mevcut de<PERSON>", "width": "Genişlik"}, "toast": {"addedToLibrary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "copyStyles": "<PERSON><PERSON>.", "copyToClipboard": "Panoya kopyalandı.", "copyToClipboardAsPng": "{{exportSelection}} panoya PNG olarak\n({{exportColorScheme}}) kopyalandı", "fileSaved": "<PERSON><PERSON><PERSON>.", "fileSavedToFilename": "{filename} kayd<PERSON><PERSON><PERSON>", "canvas": "tuval", "selection": "<PERSON><PERSON><PERSON>", "pasteAsSingleElement": "Tekil obje olarak yapıştırmak için veya var olan bir metin editörüne yapıştırmak için {{shortcut}} kullanın", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "<PERSON><PERSON><PERSON><PERSON>", "black": "Siyah", "white": "<PERSON><PERSON>", "red": "Kırmızı", "pink": "Pembe", "grape": "<PERSON><PERSON>", "violet": "<PERSON><PERSON><PERSON><PERSON>", "gray": "<PERSON><PERSON>", "blue": "<PERSON><PERSON>", "cyan": "Camgöbeği", "teal": "<PERSON><PERSON>i", "green": "<PERSON><PERSON><PERSON>", "yellow": "Sarı", "orange": "<PERSON><PERSON><PERSON>", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "", "center_heading_plus": "Ecalidraw+'a mı gitmek istediniz?", "menuHint": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, diller, ..."}, "defaults": {"menuHint": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ve daha fazlası...", "center_heading": "", "toolbarHint": "Bir araç seçin ve çizime başlayın!", "helpHint": "Kısayollar & yardım"}}, "colorPicker": {"mostUsedCustomColors": "En çok kullanılan özel renkler", "colors": "Ren<PERSON>r", "shades": "", "hexCode": "Hex kodu", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "Diske Kaydet", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}