# Customizing Styles

Excalidraw uses CSS variables to style certain components. To override them, you should set your own on the `.excalidraw` and `.excalidraw.theme--dark` (for dark mode variables) selectors.

Make sure the selector has higher specificity, e.g. by prefixing it with your app's selector:

```css
.your-app .excalidraw {
  --color-primary: red;
}
.your-app .excalidraw.theme--dark {
  --color-primary: pink;
}
```

Most notably, you can customize the primary colors, by overriding these variables:

- `--color-primary`
- `--color-primary-darker`
- `--color-primary-darkest`
- `--color-primary-light`
- `--color-primary-contrast-offset` — a slightly darker (in light mode), or lighter (in dark mode) `--color-primary` color to fix contrast issues (see [<PERSON><PERSON> illusion](https://en.wikipedia.org/wiki/Chubb_illusion)). It will fall back to `--color-primary` if not present.

For a complete list of variables, check [theme.scss](https://github.com/excalidraw/excalidraw/blob/master/packages/excalidraw/css/theme.scss), though most of them will not make sense to override.

```css showLineNumbers
.custom-styles .excalidraw {
  --color-primary: #fcc6d9;
  --color-primary-darker: #f783ac;
  --color-primary-darkest: #e64980;
  --color-primary-light: #f2a9c4;
}

.custom-styles .excalidraw.theme--dark {
  --color-primary: #d494aa;
  --color-primary-darker: #d64c7e;
  --color-primary-darkest: #e86e99;
  --color-primary-light: #dcbec9;
}
```
```tsx live
function App() {
  return (
    <div style={{ height: "500px" }} className="custom-styles">
      <Excalidraw />
    </div>
  );
}
```
