{"labels": {"paste": "دانانەوە", "pasteAsPlaintext": "دایبنێ وەک دەقی سادە", "pasteCharts": "دانانەوەی خشتەکان", "selectAll": "دیاریکردنی هەموو", "multiSelect": "زیادکردنی بۆ دیاریکراوەکان", "moveCanvas": "تابلۆ بجوڵێنە", "cut": "بڕین", "copy": "لەبەرگرتنەوە", "copyAsPng": "PNGلەبەرگرتنەوە بۆ تەختەنووس وەک", "copyAsSvg": "SVGلەبەرگرتنەوە بۆ تەختەنووس وەک", "copyText": "لەبەرگرتنەوە بۆ تەختەنووس وەک نوسین", "copySource": "", "convertToCode": "", "bringForward": "بهێنە پێشتر", "sendToBack": "بنێرە دواوە", "bringToFront": "بهێنە پێشەوە", "sendBackward": "بنێرە دواتر", "delete": "سڕینەوە", "copyStyles": "لەبەرگرتنەوەی ستایل", "pasteStyles": "دانانەوەی ستایل", "stroke": "هێڵکار", "background": "پاشبنەما", "fill": "پڕکردنەوە", "strokeWidth": "پانی هێڵکاری", "strokeStyle": "ستایلی هێڵکاری", "strokeStyle_solid": "سادە", "strokeStyle_dashed": "پچڕ پچڕ", "strokeStyle_dotted": "خاڵدار", "sloppiness": "لێژی", "opacity": "ڕوونی", "textAlign": "ڕێکخستنی دەق", "edges": "لێوارەکان", "sharp": "تیژ", "round": "چەماوە", "arrowheads": "سەرەتیر", "arrowhead_none": "ه<PERSON><PERSON><PERSON>ان", "arrowhead_arrow": "تیر", "arrowhead_bar": "هێڵ", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "سێگۆشە", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "قەبارەی فۆنت", "fontFamily": "خێزانی فۆنت", "addWatermark": "زیادبکە \"Made with Excalidraw\"", "handDrawn": "دەست کێشراو", "normal": "ئاسایی", "code": "کۆد", "small": "بچووک", "medium": "ناوەند", "large": "گه‌وره‌", "veryLarge": "زۆر گه‌وره‌", "solid": "سادە", "hachure": "هاچور", "zigzag": "زیگزاگ", "crossHatch": "کرۆس هاتچ", "thin": "تەنک", "bold": "تۆخ", "left": "چەپ", "center": "ناوه‌ند", "right": "ڕاست", "extraBold": "زۆر تۆخ", "architect": "تەلارساز", "artist": "هونەرمەند", "cartoonist": "کارتۆنی", "fileTitle": "ناوی فایل", "colorPicker": "ڕەنگ هەڵگر", "canvasColors": "بەکارهاتووە لەسەر تابلۆ", "canvasBackground": "پاشبنەمای تابلۆ", "drawingCanvas": "کێشانی تابلۆ", "layers": "چینەکان", "actions": "کردارەکان", "language": "زمان", "liveCollaboration": "هاوکاریکردنی زیندو...", "duplicateSelection": "لەبەرگرتنەوە", "untitled": "بێ ناونیشان", "name": "ناو", "yourName": "ناوەکەت", "madeWithExcalidraw": "دروستکراوە بە Excalidraw", "group": "دیاریکردنی گروپ", "ungroup": "گروپی دیاریکراوەکان لابەرە", "collaborators": "هاوکارەکان", "showGrid": "گرید نیشانبدە", "addToLibrary": "زیادکردن بۆ کتێبخانە", "removeFromLibrary": "لابردن لە کتێبخانە", "libraryLoadingMessage": "...بارکردنی کتێبخانە", "libraries": "گەڕانی کتێبخانە", "loadingScene": "...بارکردنی دیمەنەکە", "align": "لاچەنکردن", "alignTop": "لاچەنکردن بۆ سەرەوە", "alignBottom": "لاچەنکردن بۆ خوارەوە", "alignLeft": "لاچەنکردن بۆ چەپ", "alignRight": "لاچەنکردن بۆ ڕاست", "centerVertically": "بە ستونی ناوەند بکە", "centerHorizontally": "بە ئاسۆی ناوەند بکە", "distributeHorizontally": "بە ئاسۆی دابەشی بکە", "distributeVertically": "بە ستونی دابەشی بکە", "flipHorizontal": "هەڵگەڕانەوەی ئاسۆیی", "flipVertical": "هەڵگەڕانەوەی ستونی", "viewMode": "دۆخی بینین", "share": "هاوبەشی پێکردن", "showStroke": "ڕەنگهەڵگری هێڵکار نیشانبدە", "showBackground": "ڕەنگهەڵگری باکگراوند نیشانبدە", "toggleTheme": "دۆخی ڕوکار بگۆڕە", "personalLib": "کتێبخانەی کەسی", "excalidrawLib": "کتێبخانەی Excalidraw", "decreaseFontSize": "کەمکردنەوەی قەبارەی فۆنت", "increaseFontSize": "زایدکردنی قەبارەی فۆنت", "unbindText": "دەقەکە جیابکەرەوە", "bindText": "دەقەکە ببەستەوە بە کۆنتەینەرەکەوە", "createContainerFromText": "دەق لە چوارچێوەیەکدا بپێچە", "link": {"edit": "دەستکاریکردنی بەستەر", "editEmbed": "", "create": "دروستکردنی بەستەر", "createEmbed": "", "label": "بەستەر", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "دەستکاری کردنی دێڕ", "exit": "دەرچوون لە دەستکاریکەری دێڕ"}, "elementLock": {"lock": "قفڵکردن", "unlock": "کردنەوە", "lockAll": "قفڵکردنی هەموو", "unlockAll": "کردنەوەی قفلی هەمووی"}, "statusPublished": "بڵاوکراوەتەوە", "sidebarLock": "هێشتنەوەی شریتی لا بە کراوەیی", "selectAllElementsInFrame": "هەموو توخمەکانی ناو چوارچێوەکە دیاری بکە", "removeAllElementsFromFrame": "هەموو توخمەکانی ناو چوارچێوەکە لابەرە", "eyeDropper": "ڕەنگێک لەسەر تابلۆکە هەڵبژێرە", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "هێشتا هیچ بڕگەیەک زیاد نەکراوە...", "hint_emptyLibrary": "شتێک لەسەر تابلۆ هەڵبژێرە بۆ ئەوەی لێرە زیادی بکەیت، یان کتێبخانەیەک لە کۆگا گشتیەکەوە دابمەزرێنە، لە خوارەوە.", "hint_emptyPrivateLibrary": "شتێک لەسەر تابلۆ هەڵبژێرە بۆ ئەوەی لێرە زیادی بکەیت."}, "buttons": {"clearReset": "تابلۆکە وەک سەرەتا لێبکەوە", "exportJSON": "هەناردەکردن بۆ فایل", "exportImage": "وێنە هەناردە بکە...", "export": "پاشەکەوت بکە بۆ...", "copyToClipboard": "له‌به‌ری بگره‌وه‌ بۆ ته‌خته‌نووس", "save": "پاشەکەوت بکە بۆ فایلی بەردەست", "saveAs": "پاشەکەوتکردن وەک", "load": "بکەرەوە", "getShareableLink": "بەستەری هاوبەشیپێکردن بەدەستبهێنە", "close": "داخستن", "selectLanguage": "دیاریکردنی زمان", "scrollBackToContent": "گەڕاندنەوە بۆ ناوەڕۆک", "zoomIn": "نزیک خستنەوە", "zoomOut": "دوورخستنەوە", "resetZoom": "ڕێستکردنی زووم", "menu": "پێڕست", "done": "تەواو", "edit": "دەستکاری کردن", "undo": "گه‌ڕانه‌وه‌ بۆ پێشوو", "redo": "گه‌ڕانه‌وه‌ بۆ داهاتوو", "resetLibrary": "ڕێکخستنەوەی کتێبخانە", "createNewRoom": "ژوورێکی نوێ دروست بکە", "fullScreen": "پڕ بە شاشە", "darkMode": "دۆخی تاریک", "lightMode": "دۆخی ڕووناک", "zenMode": "دۆخی زێن", "objectsSnapMode": "", "exitZenMode": "بەجێهێشتنی دۆخی زێن", "cancel": "هەڵوەشاندنەوە", "clear": "خاوێنکردنەوە", "remove": "لابردن", "embed": "", "publishLibrary": "بڵاوکردنەوە", "submit": "پێشکەشکردن", "confirm": "دوپاتکردنەوە", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "ئەمە هەموو تابلۆکە خاوێن دەکاتەوە، دڵنیایت؟", "couldNotCreateShareableLink": "نەتوانرا بەستەری هاوبەشیپێکردن دروستبکرێت", "couldNotCreateShareableLinkTooBig": "نەتوانرا بەستەری هاوبەشیپێکردن دروستبکرێت: دیمەنەکە زۆر گەورەیە", "couldNotLoadInvalidFile": "ناتوانرا باربکرێت، فایلەکە دروستنییە", "importBackendFailed": "هاوردەکردن لە پاشکۆکە سەرکەوتوو نەبوو.", "cannotExportEmptyCanvas": "ناتوانرێت تابلۆی بەتاڵ هەناردەبکرێت.", "couldNotCopyToClipboard": "ناتوانرا لەبەربگیرێتەوە بۆ تەختەنوس", "decryptFailed": "ناتوانرا داتاکان شیبکرێتەوە", "uploadedSecurly": "بارکردنەکە بە کۆدکردنی کۆتایی بۆ کۆتایی پارێزراوە، ئەمەش واتە سێرڤەری Excalidraw و لایەنی سێیەم ناتوانن ناوەڕۆکەکە بخوێننەوە.", "loadSceneOverridePrompt": "بارکردنی وێنەکێشانی دەرەکی جێگەی ناوەڕۆکی بەردەستت دەگرێتەوە. دەتەوێت بەردەوام بیت؟", "collabStopOverridePrompt": "وەستاندنی دانیشتنەکە وێنەکێشانی پێشووت دەنووسێتەوە کە لە ناوخۆدا هەڵگیراوە. ئایا دڵنیایت?\n\n(ئەگەر دەتەوێت وێنەکێشانی ناوخۆیی خۆت بهێڵیتەوە، لەبری ئەوە تەنها تابی وێبگەڕەکە دابخە).", "errorAddingToLibrary": "نەیتوانی بڕگە زیاد بکات بۆ کتێبخانە", "errorRemovingFromLibrary": "نەیتوانی بڕگە لە کتێبخانە بسڕێتەوە", "confirmAddLibrary": "ئەمە {{numShapes}} شێوە(ەکان) زیاد دەکات بۆ کتێبخانەکەت. ئایا دڵنیایت?", "imageDoesNotContainScene": "وادیارە ئەم وێنەیە هیچ داتایەکی دیمەنی تێدا نییە. ئایا دیمەنی چەسپاندنت لە کاتی هەناردەدا چالاک کردووە؟", "cannotRestoreFromImage": "ناتوانرێت دیمەنەکە بگەڕێندرێتەوە لەم فایلە وێنەیە", "invalidSceneUrl": "ناتوانێت دیمەنەکە هاوردە بکات لە URL ی دابینکراو. یان نادروستە، یان داتای \"ئێکسکالیدراو\" JSON ی دروستی تێدا نییە.", "resetLibrary": "ئەمە کتێبخانەکەت خاوێن دەکاتەوە. ئایا دڵنیایت?", "removeItemsFromsLibrary": "سڕینەوەی {{count}} ئایتم(ەکان) لە کتێبخانە؟", "invalidEncryptionKey": "کلیلی رەمزاندن دەبێت لە 22 پیت بێت. هاوکاری ڕاستەوخۆ لە کارخراوە.", "collabOfflineWarning": "هێڵی ئینتەرنێت بەردەست نییە.\n گۆڕانکارییەکانت سەیڤ ناکرێن!"}, "errors": {"unsupportedFileType": "جۆری فایلی پشتگیری نەکراو.", "imageInsertError": "نەیتوانی وێنە داخڵ بکات. دواتر هەوڵ بدە", "fileTooBig": "فایلەکە زۆر گەورەیە. زۆرترین قەبارەی ڕێگەپێدراو {{maxSize}}}.", "svgImageInsertError": "نەیتوانی وێنەی SVG داخڵ بکات. نیشانەی ئێس ڤی جی نادروست دیارە.", "failedToFetchImage": "", "invalidSVGString": "ئێس ڤی جی نادروستە.", "cannotResolveCollabServer": "ناتوانێت پەیوەندی بکات بە سێرڤەری کۆلاب. تکایە لاپەڕەکە دووبارە باربکەوە و دووبارە هەوڵ بدەوە.", "importLibraryError": "نەیتوانی کتێبخانە بار بکات", "collabSaveFailed": "نەتوانرا لە بنکەدراوەی ڕاژەدا پاشەکەوت بکرێت. ئەگەر کێشەکان بەردەوام بوون، پێویستە فایلەکەت لە ناوخۆدا هەڵبگریت بۆ ئەوەی دڵنیا بیت کە کارەکانت لەدەست نادەیت.", "collabSaveFailed_sizeExceeded": "نەتوانرا لە بنکەدراوەی ڕاژەدا پاشەکەوت بکرێت، پێدەچێت تابلۆکە زۆر گەورە بێت. پێویستە فایلەکە لە ناوخۆدا هەڵبگریت بۆ ئەوەی دڵنیا بیت کە کارەکانت لەدەست نادەیت.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "وادیارە وێبگەڕی Brave بەکاردەهێنیت و ڕێکخستنی <bold>Aggressively Block Fingerprinting</bold> ـت چالاک کردووە.", "line2": "ئەمە ئەکرێ ببێتە هۆی تێکدانی <bold>دانە دەقییەکان</bold> لە وێنەکێشانەکانتدا.", "line3": "ئێمە بە توندی پێشنیاری لەکارخستنی ئەم ڕێکخستنە دەکەین. بۆ لە کارخستنی دەتوانیت بەم <link>هەنگاوانە</link>دا بڕۆیت.", "line4": "ئەگەر لەکارخستنی ئەم ڕێکخستنە نەبوە هۆی چاککردنەوەی پێشاندانی دانە دەقییەکان، تکایە <issueLink>کێشە</issueLink>یەک بکەرەوە لەسەر گیتهەبەکەمان، یان بۆمان بنوسە لەسەر <discordLink>دیسکۆرد</discordLink>"}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "دەستنیشانکردن", "image": "داخڵکردنی وێنە", "rectangle": "لاکێشە", "diamond": "ئەڵماس", "ellipse": "هێلکەیی", "arrow": "تیر", "line": "هێڵ", "freedraw": "کێشان", "text": "دەق", "library": "کتێبخانە", "lock": "ئامێرە دیاریکراوەکان چالاک بهێڵەوە دوای وێنەکێشان", "penMode": "شێوازی قەڵەم - دەست لێدان ڕابگرە", "link": "زیادکردن/ نوێکردنەوەی لینک بۆ شێوەی دیاریکراو", "eraser": "سڕەر", "frame": "ئامرازی چوارچێوە", "magicframe": "", "embeddable": "", "laser": "", "hand": "دەست (ئامرازی پانکردن)", "extraTools": "ئامرازی زیاتر", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "کردارەکانی تابلۆ", "selectedShapeActions": "کردارەکانی شێوەی دەستنیشانکراو", "shapes": "شێوەکان"}, "hints": {"canvasPanning": "بۆ جوڵاندنی تابلۆ، ویلی ماوسەکەت یان دوگمەی سپەیس بگرە لەکاتی ڕاکێشاندە، یانیش ئامرازی دەستەکە بەکاربهێنە", "linearElement": "کرتە بکە بۆ دەستپێکردنی چەند خاڵێک، ڕایبکێشە بۆ یەک هێڵ", "freeDraw": "کرتە بکە و ڕایبکێشە، کاتێک تەواو بوویت دەست هەڵگرە", "text": "زانیاری: هەروەها دەتوانیت دەق زیادبکەیت بە دوو کرتەکردن لە هەر شوێنێک لەگەڵ ئامڕازی دەستنیشانکردن", "embeddable": "", "text_selected": "دووجار کلیک بکە یان ENTER بکە بۆ دەستکاریکردنی دەق", "text_editing": "بۆ تەواوکردنی دەستکاریکردنەکە Escape یان Ctrl/Cmd+ENTER بکە", "linearElementMulti": "کلیک لەسەر کۆتا خاڵ بکە یان Escape یان Enter بکە بۆ تەواوکردن", "lockAngle": "دەتوانیت گۆشە سنووردار بکەیت بە ڕاگرتنی SHIFT", "resize": "دەتوانیت ڕێژەکان سنووردار بکەیت بە ڕاگرتنی SHIFT لەکاتی گۆڕینی قەبارەدا،\nALT ڕابگرە بۆ گۆڕینی قەبارە لە ناوەندەوە", "resizeImage": "دەتوانیت بە ئازادی قەبارە بگۆڕیت بە ڕاگرتنی SHIFT،\nALT ڕابگرە بۆ گۆڕینی قەبارە لە ناوەندەوە", "rotate": "دەتوانیت گۆشەکان سنووردار بکەیت بە ڕاگرتنی SHIFT لەکاتی سوڕانەوەدا", "lineEditor_info": "یان Ctrl یان Cmd بگرە و دوانە کلیک بکە یانیش پەنجە بنێ بە Ctrl یان Cmd + ئینتەر بۆ دەستکاریکردنی خاڵەکان", "lineEditor_pointSelected": "بۆ لابردنی خاڵەکان Delete دابگرە، Ctrl Cmd+D بکە بۆ لەبەرگرتنەوە، یان بۆ جووڵە ڕاکێشان بکە", "lineEditor_nothingSelected": "خاڵێک هەڵبژێرە بۆ دەستکاریکردن (SHIFT ڕابگرە بۆ هەڵبژاردنی چەندین)،\nیان Alt ڕابگرە و کلیک بکە بۆ زیادکردنی خاڵە نوێیەکان", "placeImage": "کلیک بکە بۆ دانانی وێنەکە، یان کلیک بکە و ڕایبکێشە بۆ ئەوەی قەبارەکەی بە دەستی دابنێیت", "publishLibrary": "کتێبخانەی تایبەت بە خۆت بڵاوبکەرەوە", "bindTextToElement": "بۆ زیادکردنی دەق enter بکە", "deepBoxSelect": "CtrlOrCmd ڕابگرە بۆ هەڵبژاردنی قووڵ، و بۆ ڕێگریکردن لە ڕاکێشان", "eraserRevert": "بۆ گەڕاندنەوەی ئەو توخمانەی کە بۆ سڕینەوە نیشانە کراون، Alt ڕابگرە", "firefox_clipboard_write": "ئەم تایبەتمەندییە بە ئەگەرێکی زۆرەوە دەتوانرێت چالاک بکرێت بە ڕێکخستنی ئاڵای \"dom.events.asyncClipboard.clipboardItem\" بۆ \"true\". بۆ گۆڕینی ئاڵاکانی وێبگەڕ لە فایەرفۆکسدا، سەردانی لاپەڕەی \"about:config\" بکە.", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "ناتوانرێ پێشبینین پیشان بدرێت", "canvasTooBig": "تابلۆکە لەوانەیە زۆر گەورەبێت.", "canvasTooBigTip": "زانیاری: هەوڵ بدە دوورترین توخمەکان کەمێک لە یەکتر نزیک بکەوە."}, "errorSplash": {"headingMain": "تووشی هەڵەیەک بوو. هەوڵ بدە <button>دووبارە بارکردنی لاپەڕەکە.</button>", "clearCanvasMessage": "ئەگەر دووبارە بارکردنەوە کار ناکات، هەوڵبدە <button>خاوێنکردنەوەی تابلۆکە.</button>", "clearCanvasCaveat": " ئەمە دەبێتە هۆی لەدەستدانی ئەوەی کە کردوتە ", "trackedToSentry": "هەڵەکە لەگەڵ ناسێنەری {{eventId}} لەسەر سیستەمەکەمان بەدواداچوونی بۆ کرا.", "openIssueMessage": "ئێمە زۆر وریا بووین کە زانیارییەکانی دیمەنەکەت لەسەر هەڵەکە نەخەینەڕوو. ئەگەر دیمەنەکەت تایبەت نییە، تکایە بیر لە بەدواداچوون بکەنەوە بۆ ئێمە <button>شوێنپێهەڵگری هەڵە.</button> تکایە ئەم زانیارییانەی خوارەوە کۆپی بکە و لە بەشی کێشەکانی Github دایبنێ.", "sceneContent": "پێکهاتەی ناو دیمەنەکە:"}, "roomDialog": {"desc_intro": "دەتوانیت خەڵک بانگهێشت بکەیت بۆ دیمەنی ئێستات بۆ هاوکاری کردن لەگەڵت.", "desc_privacy": "نیگەران مەبە، دانیشتنەکە کۆدکردنی کۆتایی بە کۆتایی بەکاردەهێنێت، بۆیە هەرچییەک بکێشیت بە تایبەتی دەمێنێتەوە. تەنانەت سێرڤەرەکەمان ناتوانێت بزانێت کە تۆ چیت دروستکردووە.", "button_startSession": "دەستپێکردنی دانیشتن", "button_stopSession": "وەستاندنی دانیشتن", "desc_inProgressIntro": "دانیشتنی هاوکاری ڕاستەوخۆ ئێستا لە ئارادایە.", "desc_shareLink": "هاوبەشکردنی ئەم لینکە لەگەڵ هەر کەسێک کە دەتەوێت هاوکاری بکەیت لەگەڵ:", "desc_exitSession": "وەستاندنی دانیشتنەکە پەیوەندیت لەگەڵ ژوورەکە دەپچڕێنێت، بەڵام تۆ دەتوانیت بەردەوام بیت لە کارکردن لەگەڵ دیمەنەکە، لە ناوخۆدا. تێبینی بکە کە ئەمە کاریگەری لەسەر کەسانی تر نابێت، وە ئەوان هێشتا دەتوانن هاوکاری بکەن لەسەر وەشانەکەیان.", "shareTitle": "بەشداری بکە لە دانیشتنی هاریکاری ڕاستەوخۆ لە ئێکسکالیدراو"}, "errorDialog": {"title": "هه‌ڵه‌ ڕوویدا"}, "exportDialog": {"disk_title": "پاشەکەوت بکە لە دیسک", "disk_details": "هەناردەکردنی داتای دیمەنەکە بۆ فایلێک کە دواتر دەتوانیت لێی هاوردە بکەیت.", "disk_button": "پاشەکەوت بکە بۆ فایل", "link_title": "بەستەری هاوبەشیپێکردن", "link_details": "ناردن وەک بەستەری تەنها-خوێندنەوە.", "link_button": "هەناردەکردن بۆ بەستەر", "excalidrawplus_description": "دیمەنەکە لە شوێنی کارکردنی Excalidraw+ هەڵبگرە.", "excalidrawplus_button": "هەناردەکردن", "excalidrawplus_exportError": "لەم ساتەدا نەتوانرا هەناردە بکرێت بۆ Excalidrow+..."}, "helpDialog": {"blog": "بلۆگەکەمان بخوێنەوە", "click": "گرتە", "deepSelect": "دەستنیشانکردنی قوڵ", "deepBoxSelect": "لەناو بۆکسەکەدا بە قووڵی هەڵبژێرە، و ڕێگری لە ڕاکێشان بکە", "curvedArrow": "تیری نوشتاوە", "curvedLine": "هێڵی نوشتاوە", "documentation": "دۆکیومێنتەیشن", "doubleClick": "دوو گرتە", "drag": "راکێشان", "editor": "دەستکاریکەر", "editLineArrowPoints": "دەستکاری خاڵەکانی هێڵ/تیر بکە", "editText": "دەستکاری دەق بکە / لەیبڵێک زیاد بکە", "github": "کێشەیەکت دۆزیەوە؟ پێشکەشکردن", "howto": "شوێن ڕینماییەکانمان بکەوە", "or": "یان", "preventBinding": "ڕێگریبکە لە نوشتاناوەی تیر", "tools": "ئامرازەکان", "shortcuts": "کورتکراوەکانی تەختەکلیل", "textFinish": "تەواوکردنی دەستکاریکردن (دەستکاریکەری دەق)", "textNewLine": "زیادکردنی دێڕی نوێ (دەستکاریکەری دەق)", "title": "یارماتی", "view": "دیمەن", "zoomToFit": "زووم بکە بۆ ئەوەی لەگەڵ هەموو توخمەکاندا بگونجێت", "zoomToSelection": "زووم بکە بۆ دەستنیشانکراوەکان", "toggleElementLock": "قفڵ/کردنەوەی دەستنیشانکراوەکان", "movePageUpDown": "لاپەڕەکە بجوڵێنە بۆ سەرەوە/خوارەوە", "movePageLeftRight": "لاپەڕەکە بجوڵێنە بۆ چەپ/ڕاست"}, "clearCanvasDialog": {"title": "تابلۆکە خاوێن بکەرەوە"}, "publishDialog": {"title": "پێشکەشکردنی کتێبخانە", "itemName": "ناوی بڕگە", "authorName": "ناوی نوسەر", "githubUsername": "ناوی بەکارهێنەری <PERSON>ub", "twitterUsername": "ناوی بەکارهێنەری Twitter", "libraryName": "ناوی کتێبخانە", "libraryDesc": "وەسفی کتێبخانە", "website": "ماڵپەڕ", "placeholder": {"authorName": "ناوەکات یاخود ناوی بەکارهێنەر", "libraryName": "ناوی کتێبخانەکەت", "libraryDesc": "وەسفی کتێبخانەکەت بۆ یارمەتیدانی خەڵک بۆ تێگەیشتن لە بەکارهێنانی", "githubHandle": "ناوی GitHub (ئارەزوومەندانە)، بۆیە دەتوانیت دەستکاری کتێبخانەکە بکەیت کاتێک پێشکەش دەکرێت بۆ پێداچوونەوە", "twitterHandle": "ناوی بەکارهێنەری تویتەر (ئارەزوومەندانە)، بۆیە بزانین لەکاتی بانگەشەکردن لە ڕێگەی تویتەرەوە کریدت بۆ کێ بکەین", "website": "لینکی ماڵپەڕی تایبەتی خۆت یان شوێنێکی تر (ئارەزومەندانە)"}, "errors": {"required": "داواکراوە", "website": "URLێکی دروست تێبنووسە"}, "noteDescription": "کتێبخانەکەت بنێرە بۆ ئەوەی بخرێتە ناو <link>کۆگای کتێبخانەی گشتی</link>بۆ ئەوەی کەسانی تر لە وێنەکێشانەکانیاندا بەکاری بهێنن.", "noteGuidelines": "کتێبخانەکە پێویستە سەرەتا بە دەست پەسەند بکرێت. تکایە بفەرمو بە خوێندنەوەی <link>ڕێنماییەکان</link> پێش پێشکەشکردن. پێویستت بە ئەژمێری GitHub دەبێت بۆ پەیوەندیکردن و گۆڕانکاری ئەگەر داوای لێکرا، بەڵام بە توندی پێویست نییە.", "noteLicense": "بە پێشکەشکردن، تۆ ڕەزامەندیت لەسەر بڵاوکردنەوەی کتێبخانەکە بەپێی <link>مۆڵەتی MIT، </link>کە بە کورتی مانای ئەوەیە کە هەرکەسێک دەتوانێت بە بێ سنوور بەکاری بهێنێت", "noteItems": "هەر شتێکی کتێبخانە دەبێت ناوی تایبەتی خۆی هەبێت بۆ ئەوەی بتوانرێت فلتەر بکرێت. ئەم بابەتانەی کتێبخانانەی خوارەوە لەخۆدەگرێت:", "atleastOneLibItem": "تکایە بەلایەنی کەمەوە یەک بڕگەی کتێبخانە دیاریبکە بۆ دەستپێکردن", "republishWarning": "تێبینی: هەندێک لە ئایتمە دیاریکراوەکان نیشانکراون وەک ئەوەی پێشتر بڵاوکراونەتەوە/نێردراون. تەنها پێویستە شتەکان دووبارە پێشکەش بکەیتەوە لە کاتی نوێکردنەوەی کتێبخانەیەکی هەبوو یان پێشکەشکردن."}, "publishSuccessDialog": {"title": "کتێبخانە پێشکەش کرا", "content": "سوپاس {{authorName}}. کتێبخانەکەت پێشکەش کراوە بۆ پێداچوونەوە. دەتوانیت بەدواداچوون بۆ دۆخەکە بکەیت<link>لێرە</link>"}, "confirmDialog": {"resetLibrary": "ڕێکخستنەوەی کتێبخانە", "removeItemsFromLib": "لابردنی ئایتمە دیاریکراوەکان لە کتێبخانە"}, "imageExportDialog": {"header": "وێنە هەناردە بکە", "label": {"withBackground": "پاشبنەما", "onlySelected": "تەنها دیاریکراوەکان", "darkMode": "دۆخی تاریک", "embedScene": "دیمەنەکە بەکاربهێنەرەوە", "scale": "قەبارە", "padding": "بۆشایی"}, "tooltip": {"embedScene": "داتاکانی دیمەنەکە لە فایلە هەناردەکراوەکەی PNG/SVG هەڵدەگیرێن بۆ ئەوەی دیمەنەکە لێیەوە بگەڕێتەوە.\nقەبارەی پەڕگەی هەناردەکراو زیاد دەکات."}, "title": {"exportToPng": "هەناردە بکە وەک PNG", "exportToSvg": "هەناردە بکە وەک SVG", "copyPngToClipboard": "لەبەربگرەوە بۆ سەر تەختەنوس"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "له‌به‌ری بگره‌وه‌ بۆ ته‌خته‌نووس"}}, "encrypted": {"tooltip": "وێنەکێشانەکانت لە کۆتاییەوە بۆ کۆتایی کۆد کراون بۆیە سێرڤەرەکانی ئێکسکالیدرا هەرگیز نایانبینن.", "link": "بلۆگ پۆست لەسەر کۆدکردنی کۆتای بۆ کۆتای لە ئێکسکالیدرەو"}, "stats": {"angle": "گۆشە", "element": "توخم", "elements": "توخمەکان", "height": "بەرزی", "scene": "دیمەنەکە", "selected": "دەستنیشانکراوەکان", "storage": "بیرگە", "title": "ئامار بۆ نێردەکان", "total": "گشتی", "version": "وەشان", "versionCopy": "کلیک بۆ لەبەرگرتنەوە", "versionNotAvailable": "وەشان بەردەست نییە", "width": "پ<PERSON>ی"}, "toast": {"addedToLibrary": "زیادکرا بۆ کتێبخانە", "copyStyles": "ستایلی کۆپیکراو.", "copyToClipboard": "لەبەرگیرایەوە بۆ تەختەنوس.", "copyToClipboardAsPng": "کۆپی کراوە {{exportSelection}} بۆ کلیپبۆرد وەک PNG\n({{exportColorScheme}})", "fileSaved": "فایل هەڵگیرا.", "fileSavedToFilename": "هەڵگیراوە بۆ {filename}", "canvas": "تابلۆ", "selection": "دەستنیشانکراوەکان", "pasteAsSingleElement": "بۆ دانانەوە وەکو یەک توخم یان دانانەوە بۆ نێو دەسکاریکەرێکی دەق کە بوونی هەیە {{shortcut}} بەکاربهێنە", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "ڕوون", "black": "ڕەش", "white": "سپی", "red": "سور", "pink": "پەمەیی", "grape": "مێوژی", "violet": "مۆری کاڵ", "gray": "خۆڵەمێشی", "blue": "شین", "cyan": "شینی ئاسمانی", "teal": "شەدری", "green": "سه‌وز", "yellow": "زەرد", "orange": "پرتەقاڵی", "bronze": "برۆنزی"}, "welcomeScreen": {"app": {"center_heading": "هەموو داتاکانت لە ناوخۆی وێنگەڕەکەتدا پاشەکەوت کراوە.", "center_heading_plus": "ویستت بڕۆیت بۆ Excalidraw+?", "menuHint": "هەناردەکردن، ڕێکخستنەکان، زمانەکان، ..."}, "defaults": {"menuHint": "هەناردەکردن، ڕێکخستنەکان، و زیاتر...", "center_heading": "دایاگرامەکان. ئاسان. کراون.", "toolbarHint": "ئامرازێک هەڵبگرە و دەستبکە بە کێشان!", "helpHint": "قەدبڕەکان و یارمەتی"}}, "colorPicker": {"mostUsedCustomColors": "زۆرترین ڕەنگە باوە بەکارهاتووەکان", "colors": "ڕەنگەکان", "shades": "سێبەرەکان", "hexCode": "کۆدی هێکس", "noShades": "هیچ سێبەرێک بۆ ئەم ڕەنگە بەردەست نییە"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}