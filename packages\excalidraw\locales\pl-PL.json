{"labels": {"paste": "<PERSON><PERSON><PERSON>", "pasteAsPlaintext": "Wklej jako zwykły tekst", "pasteCharts": "<PERSON><PERSON><PERSON> w<PERSON>", "selectAll": "Zaznacz wszystko", "multiSelect": "Dodaj element do zaznaczenia", "moveCanvas": "Przesuń obszar roboczy", "cut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copyAsPng": "Skopiuj do schowka jako plik PNG", "copyAsSvg": "Skopiuj do schowka jako plik SVG", "copyText": "Skopiuj do schowka jako tekst", "copySource": "Skopiuj źródło do schowka", "convertToCode": "Skonwertuj do kodu", "bringForward": "Przenieś wyżej", "sendToBack": "Przenieś na spód", "bringToFront": "Przenieś na wierzch", "sendBackward": "Przenieś niżej", "delete": "Usuń", "copyStyles": "Kopiuj style", "pasteStyles": "Wklej style", "stroke": "<PERSON><PERSON>", "background": "<PERSON><PERSON> wypełnienia", "fill": "Wypełnienie", "strokeWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>mowan<PERSON>", "strokeStyle": "<PERSON><PERSON> obrysu", "strokeStyle_solid": "Pełny", "strokeStyle_dashed": "Kreskowany", "strokeStyle_dotted": "Kropkowany", "sloppiness": "<PERSON><PERSON>", "opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textAlign": "Wyrównanie tekstu", "edges": "K<PERSON><PERSON><PERSON><PERSON>", "sharp": "Ostry", "round": "Zaokrąglij", "arrowheads": "<PERSON><PERSON><PERSON>", "arrowhead_none": "Brak", "arrowhead_arrow": "Strzałka", "arrowhead_bar": "Kreska", "arrowhead_circle": "Okrąg", "arrowhead_circle_outline": "<PERSON><PERSON><PERSON><PERSON> (obrys)", "arrowhead_triangle": "<PERSON><PERSON><PERSON>jk<PERSON><PERSON>", "arrowhead_triangle_outline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (obrys)", "arrowhead_diamond": "Romb", "arrowhead_diamond_outline": "<PERSON><PERSON> (obrys)", "fontSize": "Rozmiar <PERSON>", "fontFamily": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>a", "addWatermark": "<PERSON><PERSON><PERSON> \"Zrobione w Excalidraw\"", "handDrawn": "Odręczny", "normal": "Normalny", "code": "Kod", "small": "Ma<PERSON><PERSON>", "medium": "Średni", "large": "<PERSON><PERSON><PERSON>", "veryLarge": "<PERSON><PERSON><PERSON>", "solid": "Pełne", "hachure": "<PERSON><PERSON>", "zigzag": "<PERSON><PERSON><PERSON><PERSON>", "crossHatch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thin": "<PERSON><PERSON><PERSON>", "bold": "Pogrubione", "left": "<PERSON>", "center": "Do środka", "right": "Do prawej", "extraBold": "Ekstra pogrubione", "architect": "Dokładny", "artist": "Artystyczny", "cartoonist": "Rysunkowy", "fileTitle": "Nazwa pliku", "colorPicker": "Paleta k<PERSON>ów", "canvasColors": "Używane na płótnie", "canvasBackground": "<PERSON><PERSON> dokumentu", "drawingCanvas": "<PERSON><PERSON><PERSON> rob<PERSON>", "layers": "Warstwy", "actions": "<PERSON><PERSON><PERSON><PERSON>", "language": "Język", "liveCollaboration": "Współpraca w czasie rzeczywistym...", "duplicateSelection": "<PERSON><PERSON><PERSON>", "untitled": "Bez tytułu", "name": "Nazwa", "yourName": "<PERSON><PERSON> im<PERSON>", "madeWithExcalidraw": "Zrobione w Excal<PERSON>raw", "group": "Zgrupuj wybrane", "ungroup": "Rozgrupuj wybrane", "collaborators": "Współtwórcy", "showGrid": "Po<PERSON>ż siatkę", "addToLibrary": "Dodaj do biblioteki", "removeFromLibrary": "Usuń z biblioteki", "libraryLoadingMessage": "Wczytywanie biblioteki…", "libraries": "Przeglądaj biblioteki", "loadingScene": "Wczytywanie sceny…", "align": "Wyrównaj", "alignTop": "Wyrównaj do góry", "alignBottom": "Wyrównaj do dołu", "alignLeft": "Wyrównaj do lewej", "alignRight": "Wyrównaj do prawej", "centerVertically": "Wyśrodkuj w pionie", "centerHorizontally": "Wyśrodkuj w poziomie", "distributeHorizontally": "Rozł<PERSON><PERSON> poziomo", "distributeVertically": "Rozłóż pionowo", "flipHorizontal": "Odwróć w poziomie", "flipVertical": "Odwróć w pionie", "viewMode": "<PERSON><PERSON> widoku", "share": "Udostępnij", "showStroke": "Pokaż próbnik kolorów obrysu", "showBackground": "Pokaż próbnik koloru tła", "toggleTheme": "Przełącz motyw", "personalLib": "Biblioteka p<PERSON>watna", "excalidrawLib": "Biblioteka Excalidraw", "decreaseFontSize": "Zmniejsz rozmiar czcionki", "increaseFontSize": "Zwiększ rozmiar czcionki", "unbindText": "Odłącz tekst od kontenera", "bindText": "Połącz tekst z kontenerem", "createContainerFromText": "Zawijaj tekst w kontenerze", "link": {"edit": "<PERSON><PERSON><PERSON><PERSON>", "editEmbed": "Edytuj i osadź link", "create": "Utwórz <PERSON>", "createEmbed": "Stwórz i osadź link", "label": "Łącze", "labelEmbed": "Podlinkuj i osadź", "empty": "<PERSON><PERSON> usta<PERSON>u"}, "lineEditor": {"edit": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "exit": "Wyjdź z edytora linii"}, "elementLock": {"lock": "Zablokuj", "unlock": "Odblokuj", "lockAll": "Zablokuj wszystko", "unlockAll": "Odblokuj wszystko"}, "statusPublished": "Opublikowano", "sidebarLock": "Panel boczny zawsze otwarty", "selectAllElementsInFrame": "Zaznacz wszystkie elementy w ramce", "removeAllElementsFromFrame": "Usuń wszystkie elementy z ramki", "eyeDropper": "Wybierz kolor z płótna", "textToDiagram": "Tekst do diagramu", "prompt": ""}, "library": {"noItems": "Nie dodano jeszcze żadnych elementów...", "hint_emptyLibrary": "Wybierz element na płótnie, aby go tutaj do<PERSON>, lub zainstaluj bibliotekę z poniższego publicznego repozytorium.", "hint_emptyPrivateLibrary": "W<PERSON>bierz element, aby doda<PERSON> go tutaj."}, "buttons": {"clearReset": "Wyczyść dokument i zresetuj kolor dokumentu", "exportJSON": "Eksportuj do pliku", "exportImage": "Eksportuj obraz...", "export": "Zap<PERSON>z jako...", "copyToClipboard": "Skopiuj do schowka", "save": "Zapisz do bieżącego pliku", "saveAs": "<PERSON>ap<PERSON>z jako", "load": "Otwórz", "getShareableLink": "Udostępnij", "close": "Zamknij", "selectLanguage": "<PERSON><PERSON>bierz język", "scrollBackToContent": "Wróć do obszaru roboczego", "zoomIn": "Powię<PERSON><PERSON>", "zoomOut": "Po<PERSON><PERSON><PERSON><PERSON>", "resetZoom": "Zresetuj powiększenie", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "undo": "Cof<PERSON>j", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetLibrary": "Resetuj bibliotekę", "createNewRoom": "Utwórz nowy pokój", "fullScreen": "Pełny ekran", "darkMode": "Ciemny motyw", "lightMode": "<PERSON><PERSON><PERSON> motyw", "zenMode": "<PERSON><PERSON>", "objectsSnapMode": "Przyciąganie do obiektów", "exitZenMode": "Wyjdź z trybu Zen", "cancel": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remove": "Usuń", "embed": "Przełącz osadzenie", "publishLibrary": "Opublikuj", "submit": "Prześ<PERSON>j", "confirm": "Zatwierdź", "embeddableInteractionButton": "<PERSON><PERSON><PERSON><PERSON>, aby wejść w interakcję"}, "alerts": {"clearReset": "To spowoduje usunięcie wszystkiego z dokumentu. <PERSON><PERSON> kontynuować?", "couldNotCreateShareableLink": "Wystąpił błąd przy generowaniu linka do udostępniania.", "couldNotCreateShareableLinkTooBig": "Nie można utworzyć linku do udostępnienia: scena jest za duża", "couldNotLoadInvalidFile": "Nie udało się otworzyć pliku. Wybrany plik jest nieprawidłowy.", "importBackendFailed": "Wystą<PERSON>ł błąd podczas importowania pliku.", "cannotExportEmptyCanvas": "Najpierw musisz <PERSON> na<PERSON>, aby zap<PERSON> dokument.", "couldNotCopyToClipboard": "Nie udało się skopiować do schowka.", "decryptFailed": "<PERSON>e udało się odszyfrować danych.", "uploadedSecurly": "By z<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, udostępnianie projektu jest zabezpieczone szyfrowaniem end-to-end, co oznacza, że poza tobą i osobą z którą podzielisz się linkiem, nikt nie ma dostępu do tego co udostępniasz.", "loadSceneOverridePrompt": "Wczytanie zewnętrznego rysunku zastąpi istniejącą zawarto<PERSON>. <PERSON><PERSON><PERSON> kont<PERSON>?", "collabStopOverridePrompt": "Zatrzymanie sesji nadpisze poprzedni, zapisany lokalnie rysunek. <PERSON><PERSON> jeste<PERSON> pewien?\n\n(<PERSON><PERSON><PERSON> ch<PERSON>z zachowa<PERSON> swój lokalny rysunek, po prostu zamknij zakładkę przeglądarki.)", "errorAddingToLibrary": "Nie udało się dodać elementu do biblioteki", "errorRemovingFromLibrary": "Nie udało się usunąć elementu z biblioteki", "confirmAddLibrary": "To doda {{numShapes}} kształtów do twojej biblioteki. Jesteś pewien?", "imageDoesNotContainScene": "Ten obraz nie zawiera żadnych informacji o scenie. <PERSON><PERSON> włączyłeś osadzanie sceny podczas eksportu?", "cannotRestoreFromImage": "Scena nie mogła zostać przywrócona z pliku obrazu", "invalidSceneUrl": "Nie udało się zaimportować sceny z podanego adresu URL. Jest ona wadliwa lub nie zawiera poprawnych danych Excalidraw w formacie JSON.", "resetLibrary": "To wyczyści twoją bibliotekę. <PERSON><PERSON><PERSON> pewien?", "removeItemsFromsLibrary": "<PERSON><PERSON><PERSON><PERSON> {{count}} element(ów) z biblioteki?", "invalidEncryptionKey": "Klucz szyfrowania musi składać się z 22 znaków. Współpraca na żywo jest wyłączona.", "collabOfflineWarning": "Brak połączenia z Internetem.\nTwoje zmiany nie zostaną zapisane!"}, "errors": {"unsupportedFileType": "Nieobsługiwany typ pliku.", "imageInsertError": "Nie udało się wstawić obrazu. Spróbuj ponownie później...", "fileTooBig": "Plik jest zbyt duży. Maksymalny dozwolony rozmiar to {{maxSize}}.", "svgImageInsertError": "Nie udało się wstawić obrazu SVG. Znacznik SVG wygląda na nieprawidłowy.", "failedToFetchImage": "<PERSON>e udało się załadować obrazu.", "invalidSVGString": "Nieprawidłowy SVG.", "cannotResolveCollabServer": "Nie można połączyć się z serwerem współpracy w czasie rzeczywistym. Proszę odświeżyć stronę i spróbować ponownie.", "importLibraryError": "Wystąpił błąd w trakcie ładowania biblioteki", "collabSaveFailed": "Nie udało się zapisać w bazie danych. Jeśli problemy nie ustąpią, zapisz plik lokalnie, aby nie utracić swojej pracy.", "collabSaveFailed_sizeExceeded": "Nie udało się zapisać w bazie danych — dokument jest za duży. Zapisz plik lokalnie, aby nie utracić swojej pracy.", "imageToolNotSupported": "Dodawanie obrazów jest wyłączone.", "brave_measure_text_error": {"line1": "Wygląda na to, że używasz przeglądarki Brave z włączonym ustawieniem <bold>Agressively Block Fingerprinting</bold>.", "line2": "<PERSON><PERSON><PERSON> to doprowadzić do złamania <bold>elementów tekstu</bold> na rysunkach.", "line3": "Zdecydowanie zalecamy wyłączenie tego ustawienia. Mo<PERSON><PERSON><PERSON> w<PERSON> <link>te kroki</link>, aby to zrobić.", "line4": "<PERSON><PERSON><PERSON> wyłączenie tego ustawienia nie naprawia wyświetlania elementów tekstowych, zgł<PERSON>ś <issueLink>problem</issueLink> na naszym GitHubie lub napisz do nas na <discordLink>Discordzie</discordLink>"}, "libraryElementTypeError": {"embeddable": "Elementy osadzone nie mogą zostać dodane do biblioteki.", "iframe": "Elementy IFrame nie mogą zostać dodane do biblioteki.", "image": "Dodawania obrazów do biblioteki nadejdzie wkrótce!"}, "asyncPasteFailedOnRead": "Nie udało się w<PERSON> (nie udało się odczytać ze schowka systemowego).", "asyncPasteFailedOnParse": "<PERSON><PERSON> u<PERSON>ł<PERSON> si<PERSON> w<PERSON>.", "copyToSystemClipboardFailed": "Nie udało się skopiować do schowka."}, "toolBar": {"selection": "Zaznaczenie", "image": "Wstaw obraz", "rectangle": "Prostokąt", "diamond": "Romb", "ellipse": "Elipsa", "arrow": "Strzałka", "line": "<PERSON><PERSON>", "freedraw": "<PERSON><PERSON><PERSON><PERSON>", "text": "Tekst", "library": "Biblioteka", "lock": "Zablokuj wybrane narzędzie", "penMode": "Tryb pióra — zapobiegaj dotknięciom", "link": "Dodaj/aktualizuj link dla wybranego kształtu", "eraser": "<PERSON><PERSON><PERSON>", "frame": "<PERSON><PERSON>", "magicframe": "Wireframe do kodu", "embeddable": "Osadzenie z internetu", "laser": "Wskaźnik laserowy", "hand": "Ręka (narzędzie do przesuwania)", "extraTools": "<PERSON>ię<PERSON>j narzędzi", "mermaidToExcalidraw": "Konwertuj diagram Mermaid do Excalidraw", "magicSettings": "Ustawienia AI"}, "headings": {"canvasActions": "Narzędzia", "selectedShapeActions": "Wybrane narzędzie", "shapes": "Kształty"}, "hints": {"canvasPanning": "Aby prz<PERSON><PERSON><PERSON> płó<PERSON>no, przytrzymaj kółko myszy lub spację podczas przeciągania, albo użyj narzędzia ręki", "linearElement": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby na<PERSON><PERSON><PERSON> linię", "freeDraw": "Naciśnij i przeciągnij by <PERSON><PERSON><PERSON><PERSON>, puść kiedy skończysz", "text": "Wskazówka: możesz również dodać tekst klikając dwukrotnie gdziekolwiek za pomocą narzędzia zaznaczania", "embeddable": "Kliknij i przeciągnij, aby stworzyć osadzenie strony", "text_selected": "<PERSON><PERSON><PERSON><PERSON> dwukrotnie lub naciśnij ENTER, aby ed<PERSON><PERSON> tekst", "text_editing": "Naciśnij Escape lub Ctrl (Cmd w macOS) + ENTER, aby z<PERSON> edycję", "linearElementMulti": "<PERSON>by zak<PERSON><PERSON><PERSON><PERSON> krzywą, ponownie kliknij w ostatni punkt, bą<PERSON>ź naciśnij Esc albo Enter", "lockAngle": "Możesz ograniczyć kąt trzymając SHIFT", "resize": "<PERSON><PERSON><PERSON><PERSON> zach<PERSON>ć proporcję trzymająć wcisnięty SHIFT, przytrzymaj ALT by <PERSON><PERSON><PERSON><PERSON> rozmiar względem środka", "resizeImage": "Możesz zmienić rozmiar swobodnie trzymając SHIFT,\nprzytrzymaj ALT, aby prz<PERSON><PERSON><PERSON> względem środka obiektu", "rotate": "Możesz obracać element w równych odstępach trzymając wciśnięty SHIFT", "lineEditor_info": "Przytrzymaj CtrlOrCmd i kliknij dwukrotnie lub naciśnij CtrlOrCmd + Enter, aby edytować punkty", "lineEditor_pointSelected": "Naciśnij przycisk Delete, aby usun<PERSON><PERSON> punkt. Ctrl/Cmd+D, aby go zduplikować. Przeciągnij, aby go przenie<PERSON>ć", "lineEditor_nothingSelected": "Wybierz punkt do edycji (przytrzymaj SHIFT, aby wybrać wiele),\nlub przytrzymaj Alt i kliknij, aby dodać nowe punkty", "placeImage": "<PERSON><PERSON><PERSON><PERSON>, aby <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, lub klik<PERSON>j i przeciągnij, aby usta<PERSON><PERSON> jego rozmiar ręcznie", "publishLibrary": "Opublikuj własną bibliotekę", "bindTextToElement": "<PERSON><PERSON><PERSON><PERSON><PERSON> enter, aby doda<PERSON> tekst", "deepBoxSelect": "Przytrzymaj CtrlOrCmd, aby wy<PERSON> w obrębie grupy i uniknąć przeciągania", "eraserRevert": "Przytrzymaj Alt, aby przywrócić elementy oznaczone do usunięcia", "firefox_clipboard_write": "Ta funkcja może być włączona poprzez ustawienie flagi \"dom.events.asyncClipboard.clipboardItem\" na \"true\". A<PERSON> zmie<PERSON>ć flagi przeglądarki w Firefox, odwi<PERSON><PERSON> stronę \"about:config\".", "disableSnapping": "Przytrzymaj Ctrl lub Cmd, aby wyłączyć przyciąganie"}, "canvasError": {"cannotShowPreview": "Nie można wyświetlić podglądu", "canvasTooBig": "<PERSON><PERSON><PERSON> roboczy może być za duży.", "canvasTooBigTip": "Wskazówka: spróbuj nieco zbliżyć najdalej wysunięte elementy."}, "errorSplash": {"headingMain": "Wystą<PERSON>ł błąd. Spróbuj <button>od<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stronę.</button>", "clearCanvasMessage": "<PERSON><PERSON><PERSON> odświeżenie strony nie zadziałało, spróbuj <button>usun<PERSON>ć wszystko z dokumentu.</button>", "clearCanvasCaveat": " <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>e spowoduje to utratę całej twojej pracy ", "trackedToSentry": "Błąd o identyfikatorze {{eventId}} został zaraportowany w naszym systemie.", "openIssueMessage": "Szanujemy twoją prywat<PERSON>ć i raport nie zawierał żadnych danych dotyczących tego nad czym pracowałeś, natomiast je<PERSON><PERSON> jesteś w stanie podzielić się tym nad czym pracowałeś, prosimy o dodatkowy raport poprzez <button>nasze narzędzie do raportowania błędów.</button> Prosimy o dołączenie poniższej informacji poprzez skopiowanie jej i umieszczenie jej w zgłoszeniu na portalu GitHub.", "sceneContent": "Zawartość dokumentu:"}, "roomDialog": {"desc_intro": "Będziesz w stanie pracować wraz z osobami które zaprosisz do współpracy.", "desc_privacy": "By z<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, se<PERSON><PERSON> współpracy na żywo jest zabezpieczona szyfrowaniem end-to-end, co oznacza, że poza tobą i osobami z którymi podzielisz się linkiem, nikt nie ma dostępu do tego co będziecie tworzyć.", "button_startSession": "Rozpocznij se<PERSON>ję", "button_stopSession": "Zakończ sesję", "desc_inProgressIntro": "Sesja współpracy na żywo właśnie się rozpoczęła.", "desc_shareLink": "Udostępnij ten link osobom, z którymi chcesz współpracować:", "desc_exitSession": "Zakończenie sesji spowoduje odłączenie ciebie od pokoju, ale nadal będziesz mógł lokalnie kontynuować pracę. <PERSON><PERSON><PERSON><PERSON>, że osoby z którymi współpracowałeś nadal będą mogły współpracować.", "shareTitle": "Dołącz do sesji współpracy na żywo w Excalidraw"}, "errorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd"}, "exportDialog": {"disk_title": "Zapisz na dysku", "disk_details": "Eksportuj dane sceny do pliku, z którego możesz importować później.", "disk_button": "Zapisz do pliku", "link_title": "Link do udostępnienia", "link_details": "Eksportuj jako link tylko do odczytu.", "link_button": "Wygeneruj link", "excalidrawplus_description": "Zapisz scenę do swojego obszaru roboczego Excalidraw+.", "excalidrawplus_button": "Eksportuj", "excalidrawplus_exportError": "W tej chwili nie można wyeksportować do Excalidraw+..."}, "helpDialog": {"blog": "Przeczytaj na naszym blogu", "click": "kliknięcie", "deepSelect": "Wybór w obrębie grupy", "deepBoxSelect": "Wybór w obrębie grupy i unikanie przeciągania", "curvedArrow": "Zakrzywiona strzałka", "curvedLine": "Zakrzywiona linia", "documentation": "Dokumentacja", "doubleClick": "podwójne kliknięcie", "drag": "przec<PERSON><PERSON><PERSON><PERSON>", "editor": "<PERSON><PERSON><PERSON>", "editLineArrowPoints": "Edytuj punkty linii/strzałki", "editText": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>/dodaj et<PERSON>", "github": "Znalazłeś problem? Prześlij", "howto": "Skorzystaj z instrukcji", "or": "lub", "preventBinding": "Zapobiegaj wią<PERSON><PERSON> s<PERSON>ł<PERSON>", "tools": "Narzędzia", "shortcuts": "Skróty <PERSON>zo<PERSON>", "textFinish": "Zakończ edycję (edytor tekstu)", "textNewLine": "<PERSON><PERSON><PERSON>y w<PERSON> (edytor tekstu)", "title": "Pomoc", "view": "Widok", "zoomToFit": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby wyświetlić wszystkie elementy", "zoomToSelection": "Przybliż do zaznaczenia", "toggleElementLock": "Zablokuj/odblokuj zaznaczenie", "movePageUpDown": "Przesuń stronę w górę/w dół", "movePageLeftRight": "Przenieś stronę w lewo/prawo"}, "clearCanvasDialog": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć płótno"}, "publishDialog": {"title": "Opublikuj bibliotekę", "itemName": "<PERSON><PERSON><PERSON>", "authorName": "Nazwa autora", "githubUsername": "Nazwa użytkownika na GitHubie", "twitterUsername": "Nazwa użytkownika Twitter", "libraryName": "Nazwa biblioteki", "libraryDesc": "Opis biblioteki", "website": "Strona internetowa", "placeholder": {"authorName": "Twoje imię lub nazwa użytkownika", "libraryName": "Nazwa twojej biblioteki", "libraryDesc": "Opis twojej biblioteki, aby pomóc innym zrozumieć jej d<PERSON>", "githubHandle": "<PERSON><PERSON><PERSON><PERSON> (opcjonalny), <PERSON><PERSON><PERSON><PERSON> cze<PERSON> moż<PERSON>z edytować bibliotekę po przesłaniu do sprawdzenia", "twitterHandle": "Nazwa użytkownika w serwisie Twitter (opcjonalna), aby wied<PERSON><PERSON> kogo oznaczyć przy promowaniu na Twitterze", "website": "Link do Twojej osobistej strony internetowej lub gdzie indziej (opcjonalnie)"}, "errors": {"required": "<PERSON><PERSON><PERSON><PERSON>", "website": "Wprowadź prawidłowy adres URL"}, "noteDescription": "<link></link>dla innych osób do wykorzystania w swoich rysunkach.", "noteGuidelines": "Biblioteka musi być najpierw zatwierdzona ręcznie. Przeczytaj <link>wytyczne</link>", "noteLicense": "Wysyła<PERSON><PERSON><PERSON> zgadzasz się, że biblioteka zostanie opublikowana pod <link>Licencja MIT, </link>w skr<PERSON><PERSON>, każdy może z nich korzystać bez ograniczeń.", "noteItems": "Każdy element biblioteki musi mieć własną nazwę, aby był filtrowalny. Uwzględnione zostaną następujące elementy biblioteki:", "atleastOneLibItem": "<PERSON><PERSON><PERSON> w<PERSON> co najmniej jeden element biblioteki, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republishWarning": "Uwaga: niektóre z wybranych elementów są oznaczone jako już opublikowane/wysłane. Powinieneś ponownie przesłać elementy tylko wtedy, gdy aktualizujesz istniejącą bibliotekę lub zgłoszenie."}, "publishSuccessDialog": {"title": "Biblioteka została przesłana", "content": "D<PERSON>ę<PERSON><PERSON><PERSON> {{authorName}}. Twoja biblioteka została przesłana do sprawdzenia. Moż<PERSON>z śledzić jej stan<link>tutaj</link>"}, "confirmDialog": {"resetLibrary": "Zresetuj Bibliotekę", "removeItemsFromLib": "Usuń wybrane elementy z biblioteki"}, "imageExportDialog": {"header": "Eksportuj obraz", "label": {"withBackground": "Tło", "onlySelected": "<PERSON><PERSON><PERSON> w<PERSON>ne", "darkMode": "<PERSON><PERSON> c<PERSON>ny", "embedScene": "Osadź scenę", "scale": "<PERSON><PERSON><PERSON>", "padding": "Dopełnienie"}, "tooltip": {"embedScene": "Dane sceny zostaną zapisane w eksportowanym pliku PNG/SVG tak, aby scena mogła zostać z niego przywrócona.\n<PERSON><PERSON><PERSON><PERSON><PERSON> to rozmiar eksportowanego pliku."}, "title": {"exportToPng": "Zapisz jako PNG", "exportToSvg": "Zapisz jako SVG", "copyPngToClipboard": "Skopiuj do schowka jako PNG"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Skopiuj do schowka"}}, "encrypted": {"tooltip": "Twoje rysunki są zabezpieczone szyfrowaniem end-to-end, tak więc nawet w Excalidraw nie jesteśmy w stanie zobaczyć tego co tworzysz.", "link": "Wpis na blogu dotyczący szyfrowania end-to-end w Excalidraw"}, "stats": {"angle": "<PERSON><PERSON><PERSON>", "element": "Element", "elements": "Elementy", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scene": "<PERSON><PERSON>", "selected": "Zaznaczenie", "storage": "<PERSON><PERSON><PERSON><PERSON>", "title": "Statystyki dla nerdów", "total": "Łącznie", "version": "<PERSON><PERSON><PERSON>", "versionCopy": "<PERSON><PERSON><PERSON><PERSON>, aby sko<PERSON>ć", "versionNotAvailable": "<PERSON><PERSON>ja niedostępna", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Dodano do biblioteki", "copyStyles": "Skopiowano style.", "copyToClipboard": "Skopiowano do schowka.", "copyToClipboardAsPng": "Skopiowano {{exportSelection}} do schowka jako PNG\n({{exportColorScheme}})", "fileSaved": "Zapisano plik.", "fileSavedToFilename": "Zapisano jako {filename}", "canvas": "płótno", "selection": "zaznaczenie", "pasteAsSingleElement": "Użyj {{shortcut}}, aby w<PERSON><PERSON> jako pojedy<PERSON>zy element,\nlub wklej do istniejącego edytora tekstu", "unableToEmbed": "Osadzenie tego linku jest obecnie niedozwolone. Zgłoś propozycję na portalu GitHub, aby dodać go do listy dozwolonych wyjątków", "unrecognizedLinkFormat": "Osadzony link ma niewłaściwy format. Spróbuj wkleić całą zawartoś<PERSON> pola \"embed\" z oryginalnej strony."}, "colors": {"transparent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "black": "<PERSON><PERSON><PERSON>", "white": "Biały", "red": "Czerwony", "pink": "Różowy", "grape": "Winogronowy", "violet": "<PERSON><PERSON><PERSON><PERSON>", "gray": "Szary", "blue": "<PERSON><PERSON><PERSON><PERSON>", "cyan": "Cyjanowy", "teal": "Turkusowy", "green": "<PERSON><PERSON><PERSON>", "yellow": "Żół<PERSON>", "orange": "Pomarańczowy", "bronze": "Brązowy"}, "welcomeScreen": {"app": {"center_heading": "Wszystkie dane są zapisywane lokalnie w przeglądarce.", "center_heading_plus": "<PERSON><PERSON> z<PERSON>st tego chcesz przejść do Excalidraw+?", "menuHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>..."}, "defaults": {"menuHint": "Eksportuj, preferencje i więcej...", "center_heading": "Schematy uproszczone.", "toolbarHint": "Wybierz narzędzie i zacznij rysować!", "helpHint": "Skróty klawiaturowe i pomoc"}}, "colorPicker": {"mostUsedCustomColors": "Najczęściej używane kolory", "colors": "<PERSON><PERSON><PERSON>", "shades": "Odcienie", "hexCode": "Kod HEX", "noShades": "Brak dostępnych odcieni dla tego koloru"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Eksportuj jako obraz", "button": "Eksportuj jako obraz", "description": "Eksportuj zawartość sceny jako obraz z możliwością importowania."}, "saveToDisk": {"title": "Zapisz na dysku", "button": "Zapisz na dysku", "description": "Eksportuj zawartość sceny jako plik z możliwością importowania."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Eksportuj do Excalidraw+", "description": "Zapisz scenę do swojego obszaru roboczego Excalidraw+."}}, "modal": {"loadFromFile": {"title": "Wczytaj z pliku", "button": "Wczytaj z pliku", "description": "Wczytanie z pliku <bold>nad<PERSON><PERSON> istniejącą zawa<PERSON></bold>.<br></br>Możesz najpierw utworzyć kopię zapasową swojego rysunku, używając jednej z poniższych opcji."}, "shareableLink": {"title": "Wczytaj z linku", "button": "Nadpisz moją z<PERSON>", "description": "Wczytanie zewnętrznego pliku <bold>nad<PERSON><PERSON> istniejącą zawarto<PERSON></bold>.<br></br>Możesz najpierw utworzyć kopię zapasową swojego rysunku, używając jednej z poniższych opcji."}}}, "mermaid": {"title": "Konwertuj diagram Mermaid do Excalidraw", "button": "Wstaw", "description": "Obecnie wspierane są jedynie <flowchartLink>proste grafy</flowchartLink>, <sequenceLink>sekwencje</sequenceLink> i <classLink>diagramy klas</classLink>. Pozostałe typy będą wyświetlane jako obrazy w Excalidraw.", "syntax": "Składnia diagramów Mermaid", "preview": "Podgląd"}}