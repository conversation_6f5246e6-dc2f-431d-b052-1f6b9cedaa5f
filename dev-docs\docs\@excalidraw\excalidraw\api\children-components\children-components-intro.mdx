---
sidebar_label: Children Components
slug: /@excalidraw/excalidraw/api/children-components
---

# `<Excalidraw/>` children

We expose several components you can render as children of the `<Excalidraw/>` component to customize the UI.

:::info

We have only recently started migrating to this type of component API. Some UI components are still using render props, and some UI customization isn't supported yet (such as the toolbar or the element properties panel). Stay tuned for more updates!

:::

Below are the currently supported components:

- [MainMenu](/docs/@excalidraw/excalidraw/api/children-components/main-menu)
- [WelcomeScreen](/docs/@excalidraw/excalidraw/api/children-components/welcome-screen)
- [Sidebar](/docs/@excalidraw/excalidraw/api/children-components/sidebar)
- [Footer](/docs/@excalidraw/excalidraw/api/children-components/footer)
- [LiveCollaborationTrigger](/docs/@excalidraw/excalidraw/api/children-components/live-collaboration-trigger)
