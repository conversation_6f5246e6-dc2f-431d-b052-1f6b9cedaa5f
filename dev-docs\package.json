{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start --port 3003", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "2.2.0", "@docusaurus/preset-classic": "2.2.0", "@docusaurus/theme-live-codeblock": "2.2.0", "@excalidraw/excalidraw": "0.18.0", "@mdx-js/react": "^1.6.22", "clsx": "^1.2.1", "docusaurus-plugin-sass": "0.2.3", "prism-react-renderer": "^1.3.5", "react": "18.2.0", "react-dom": "18.2.0", "sass": "1.57.1"}, "devDependencies": {"@docusaurus/module-type-aliases": "2.0.0-rc.1", "@tsconfig/docusaurus": "^1.0.5", "docusaurus2-dotenv": "1.4.0", "typescript": "^4.7.4"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.14"}}