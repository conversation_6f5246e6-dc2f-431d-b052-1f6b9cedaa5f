{"labels": {"paste": "Įklijuoti", "pasteAsPlaintext": "Įklijuoti kaip paprastą tekstą", "pasteCharts": "Įklijuoti diagramas", "selectAll": "Pažymėti viską", "multiSelect": "Pridėkite elementą prie pasirinktų", "moveCanvas": "<PERSON><PERSON><PERSON> d<PERSON>", "cut": "Iškirpti", "copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "copyAsPng": "Kopijuoti į iškarpinę kaip PNG", "copyAsSvg": "Kopijuoti į iškarpinę kaip SVG", "copyText": "Kopijuoti į iškarpinę kaip tekstą", "copySource": "", "convertToCode": "", "bringForward": "Kelti priekio link", "sendToBack": "<PERSON>ustum<PERSON> į užnugarį", "bringToFront": "<PERSON><PERSON><PERSON><PERSON> į priekį", "sendBackward": "Nustumti link užnugario", "delete": "<PERSON><PERSON><PERSON><PERSON>", "copyStyles": "<PERSON><PERSON><PERSON><PERSON><PERSON> stilius", "pasteStyles": "Įklijuoti stilius", "stroke": "<PERSON><PERSON>", "background": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeWidth": "<PERSON><PERSON><PERSON> storis", "strokeStyle": "<PERSON><PERSON><PERSON>", "strokeStyle_solid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dashed": "Brūk<PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dotted": "Taškuota", "sloppiness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textAlign": "Teks<PERSON> ly<PERSON>", "edges": "Kraštai", "sharp": "<PERSON><PERSON><PERSON><PERSON>", "round": "Užapvalintas", "arrowheads": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arrowhead_none": "<PERSON><PERSON><PERSON>", "arrowhead_arrow": "Rodyklė", "arrowhead_bar": "Brukšnys", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Trikampis", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "<PERSON><PERSON><PERSON>", "fontFamily": "<PERSON><PERSON><PERSON>", "addWatermark": "Sukurta su Excalidraw", "handDrawn": "Ranka raš<PERSON>as", "normal": "Normalus", "code": "<PERSON><PERSON>", "small": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON><PERSON>", "veryLarge": "<PERSON><PERSON>", "solid": "", "hachure": "", "zigzag": "", "crossHatch": "", "thin": "Plonas", "bold": "<PERSON><PERSON><PERSON>", "left": "<PERSON><PERSON><PERSON><PERSON>", "center": "Centre", "right": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extraBold": "<PERSON><PERSON><PERSON>", "architect": "Architektas", "artist": "Menininkas", "cartoonist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fileTitle": "Failo pavadin<PERSON>", "colorPicker": "Spalvos parinkiklis", "canvasColors": "", "canvasBackground": "<PERSON><PERSON><PERSON><PERSON>", "drawingCanvas": "", "layers": "Sluoksniai", "actions": "Veiks<PERSON><PERSON>", "language": "Kalba", "liveCollaboration": "Bendradarbiavimas gyvai...", "duplicateSelection": "", "untitled": "", "name": "", "yourName": "<PERSON><PERSON><PERSON><PERSON> vardas", "madeWithExcalidraw": "Sukurta su Excalidraw", "group": "Grupuo<PERSON>", "ungroup": "Išgrupuoti p<PERSON>", "collaborators": "Bendradarbiautojai", "showGrid": "Rod<PERSON><PERSON> tinklelį", "addToLibrary": "Pridėti į biblioteką", "removeFromLibrary": "Pašalinti iš bibliotekos", "libraryLoadingMessage": "", "libraries": "Naršyti bibliotekas", "loadingScene": "", "align": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignTop": "Lygiuoti viršuje", "alignBottom": "Lygiuoti apačioje", "alignLeft": "<PERSON>ygi<PERSON><PERSON>", "alignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "centerVertically": "Centruoti vertika<PERSON>i", "centerHorizontally": "Centruoti horizontal<PERSON>i", "distributeHorizontally": "", "distributeVertically": "", "flipHorizontal": "Apversti horizontaliai", "flipVertical": "Ap<PERSON><PERSON> vert<PERSON>i", "viewMode": "", "share": "<PERSON><PERSON><PERSON>", "showStroke": "", "showBackground": "", "toggleTheme": "", "personalLib": "Asmeninė biblioteka", "excalidrawLib": "Exaclidraw biblioteka", "decreaseFontSize": "", "increaseFontSize": "", "unbindText": "", "bindText": "", "createContainerFromText": "", "link": {"edit": "Redeguoti nuorodą", "editEmbed": "", "create": "Sukurti nuorodą", "createEmbed": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "", "exit": ""}, "elementLock": {"lock": "Užrakinti", "unlock": "Atrakinti", "lockAll": "", "unlockAll": ""}, "statusPublished": "", "sidebarLock": "", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "", "hint_emptyLibrary": "", "hint_emptyPrivateLibrary": ""}, "buttons": {"clearReset": "", "exportJSON": "Eksportuoti į failą", "exportImage": "", "export": "", "copyToClipboard": "Kopijuoti į iškarpinę", "save": "", "saveAs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "load": "", "getShareableLink": "<PERSON>aut<PERSON> nuorod<PERSON>", "close": "Uždaryti", "selectLanguage": "Pasirinkite kalbą", "scrollBackToContent": "", "zoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "resetZoom": "", "menu": "<PERSON><PERSON>", "done": "", "edit": "Red<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON>", "redo": "", "resetLibrary": "Atstatyti biblioteką", "createNewRoom": "Sukurti naują kambarį", "fullScreen": "Visas ekranas", "darkMode": "<PERSON><PERSON>", "lightMode": "<PERSON><PERSON><PERSON>", "zenMode": "„Zen“ režimas", "objectsSnapMode": "", "exitZenMode": "Išeiti iš „Zen“ režimo", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Išvalyti", "remove": "<PERSON><PERSON><PERSON><PERSON>", "embed": "", "publishLibrary": "Paskelbti", "submit": "Pat<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "", "couldNotCreateShareableLink": "", "couldNotCreateShareableLinkTooBig": "", "couldNotLoadInvalidFile": "", "importBackendFailed": "", "cannotExportEmptyCanvas": "", "couldNotCopyToClipboard": "", "decryptFailed": "", "uploadedSecurly": "", "loadSceneOverridePrompt": "", "collabStopOverridePrompt": "Sesijos nutraukimas perrašys ankstesnį, lokaliai išsaugotą piešinį. Ar tikrai to nori?\n\n(Jei nori išlaikyti lokalų piešinį, tiesiog uždaryk naršyklės skirtuką.)", "errorAddingToLibrary": "Nepavyko įtraukti elemento į biblioteką", "errorRemovingFromLibrary": "Nepavyko p<PERSON>šalinti elemento iš bibliotekos", "confirmAddLibrary": "Tai įtrauks {{numShapes}} figūrą/-as į tavo biblioteką. Ar tikrai to nori?", "imageDoesNotContainScene": "<PERSON><PERSON><PERSON><PERSON>, jog <PERSON><PERSON> pave<PERSON>liukas neturi scenos duomenų. Ar yra įjuntas scenos įtraukimas ekportavimo metu?", "cannotRestoreFromImage": "Nepavyko atstatyti scenos iš šio nuotraukos failo", "invalidSceneUrl": "Nepavyko suimportuoti scenos iš pateiktos nuorodos (URL). Ji arba blogai suformatuota, arba savyje neturi teisingų Excalidraw JSON duomenų.", "resetLibrary": "Tai išvalys tavo biblioteką. Ar tikrai to nori?", "removeItemsFromsLibrary": "<PERSON><PERSON><PERSON><PERSON> {{count}} elementą/-us iš bibliote<PERSON>?", "invalidEncryptionKey": "Šifravimo raktas turi būti iš 22 simbolių. Redagavimas gyvai yra išjungtas.", "collabOfflineWarning": ""}, "errors": {"unsupportedFileType": "Nepalaikomas failo tipas.", "imageInsertError": "Nepyko įkelti paveiksliuko. Pabandyk vėliau...", "fileTooBig": "Per dideli<PERSON> failas. <PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON> dydis yra {{maxSize}}.", "svgImageInsertError": "Nepavyko įtraukti SVG paveiksliuko. <PERSON><PERSON><PERSON><PERSON>, jog SVG yra nevalid<PERSON>.", "failedToFetchImage": "", "invalidSVGString": "Nevalidus SVG.", "cannotResolveCollabServer": "Nepavyko prisijungti prie serverio bendradarbiavimui. Perkrauk puslapį ir pabandyk prisijungti dar kartą.", "importLibraryError": "Nepavyko įkelti bibliotekos", "collabSaveFailed": "", "collabSaveFailed_sizeExceeded": "", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "Žymėjimas", "image": "Įkelti paveiksliuką", "rectangle": "Stačiakampis", "diamond": "<PERSON><PERSON><PERSON>", "ellipse": "Elipsė", "arrow": "Rodyklė", "line": "<PERSON><PERSON>", "freedraw": "<PERSON><PERSON><PERSON>", "text": "Tekstas", "library": "Biblioteka", "lock": "<PERSON><PERSON>, i<PERSON><PERSON><PERSON>ti pasirinktą įrankį", "penMode": "<PERSON><PERSON><PERSON><PERSON> - neleisti prisilietimų", "link": "<PERSON><PERSON><PERSON><PERSON> / Atna<PERSON>jinti pasirinktos figūros nuorodą", "eraser": "<PERSON><PERSON><PERSON><PERSON>", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "Veiksmai su drobe", "selectedShapeActions": "Veiksmai su pasirinkta figūra", "shapes": "<PERSON><PERSON><PERSON><PERSON>"}, "hints": {"canvasPanning": "", "linearElement": "Paspaudimai sukurs papildomus <PERSON>, nepertraukiamas tempimas sukurs liniją", "freeDraw": "<PERSON><PERSON> ir tempk, paleisk kai nor<PERSON><PERSON> p<PERSON>ti", "text": "Užuomina: tekst<PERSON> taip pat galima pridėti bet kur su dvigubu pelė<PERSON> paspa<PERSON>, kol parinkas žymėjimo įrankis", "embeddable": "", "text_selected": "", "text_editing": "", "linearElementMulti": "", "lockAngle": "", "resize": "", "resizeImage": "", "rotate": "", "lineEditor_info": "", "lineEditor_pointSelected": "", "lineEditor_nothingSelected": "", "placeImage": "", "publishLibrary": "", "bindTextToElement": "", "deepBoxSelect": "", "eraserRevert": "", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "", "canvasTooBig": "", "canvasTooBigTip": ""}, "errorSplash": {"headingMain": "", "clearCanvasMessage": "", "clearCanvasCaveat": "", "trackedToSentry": "", "openIssueMessage": "", "sceneContent": ""}, "roomDialog": {"desc_intro": "", "desc_privacy": "", "button_startSession": "<PERSON><PERSON><PERSON><PERSON>", "button_stopSession": "Sustab<PERSON><PERSON>", "desc_inProgressIntro": "", "desc_shareLink": "", "desc_exitSession": "", "shareTitle": ""}, "errorDialog": {"title": "<PERSON><PERSON><PERSON>"}, "exportDialog": {"disk_title": "Įrašyti į diską", "disk_details": "", "disk_button": "Įrašyti į failą", "link_title": "<PERSON><PERSON><PERSON><PERSON>", "link_details": "", "link_button": "", "excalidrawplus_description": "", "excalidrawplus_button": "Eksportuoti", "excalidrawplus_exportError": ""}, "helpDialog": {"blog": "", "click": "pas<PERSON><PERSON><PERSON>", "deepSelect": "", "deepBoxSelect": "", "curvedArrow": "<PERSON><PERSON><PERSON>", "curvedLine": "Bang<PERSON>ta linija", "documentation": "Dokumentacija", "doubleClick": "<PERSON><PERSON><PERSON><PERSON> pas<PERSON><PERSON>", "drag": "vilkti", "editor": "<PERSON><PERSON><PERSON><PERSON>", "editLineArrowPoints": "", "editText": "", "github": "Radai klaidą? Pateik", "howto": "Vadovaukis mūsų gidu", "or": "arba", "preventBinding": "", "tools": "Įrankiai", "shortcuts": "Spartieji k<PERSON>šai", "textFinish": "Baigti redagavimą (teksto redaktoriuje)", "textNewLine": "Pridėti naują eilutę (tekto redaktoriuje)", "title": "Pagalba", "view": "", "zoomToFit": "", "zoomToSelection": "Priartinti iki pažymėtos vietos", "toggleElementLock": "", "movePageUpDown": "Pajudinti puslapį aukštyn/žemyn", "movePageLeftRight": "Pa<PERSON>din<PERSON> puslapį kairėn/de<PERSON><PERSON><PERSON>n"}, "clearCanvasDialog": {"title": "Išvalyti drobę"}, "publishDialog": {"title": "Paviešinti biblioteką", "itemName": "Elemento pavadinimas", "authorName": "<PERSON><PERSON><PERSON> var<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "twitterUsername": "Twitter slapyvardis", "libraryName": "Bibliotekos pavadinimas", "libraryDesc": "Bibliotekos <PERSON>", "website": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": {"authorName": "<PERSON><PERSON> vardas arba spa<PERSON>", "libraryName": "Tavo bibliotekos pavadinimas", "libraryDesc": "Tavo bibliotekos <PERSON>, <PERSON><PERSON><PERSON> geriau suprasti jos paskirtį", "githubHandle": "", "twitterHandle": "", "website": ""}, "errors": {"required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "website": "Įveskite teisingą nuorodą (URL)"}, "noteDescription": "Pateik savo biblioteką, jog ji galėtų būti įtraukta į <link></link>jog kiti ž<PERSON>s galėtų tai naudoti savo piešiniuose.", "noteGuidelines": "<PERSON><PERSON><PERSON>, biblioteka turi būti rankiniu būdu patvir<PERSON>. Prašome paskaityti <link>g<PERSON><PERSON><PERSON></link>", "noteLicense": "<link>MIT licencija, </link>", "noteItems": "", "atleastOneLibItem": "", "republishWarning": ""}, "publishSuccessDialog": {"title": "Biblioteka pateikta", "content": "<PERSON><PERSON><PERSON><PERSON> {{authorName}}. Tavo biblioteka buvo pateikta peržiūrai. Gali sekti būseną<link>čia</link>"}, "confirmDialog": {"resetLibrary": "Atstatyti biblioteką", "removeItemsFromLib": "Pašalinti pasirinktus elementus iš bibliotekos"}, "imageExportDialog": {"header": "", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "", "scale": "", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "", "link": ""}, "stats": {"angle": "", "element": "Elementas", "elements": "Elementai", "height": "<PERSON><PERSON><PERSON><PERSON>", "scene": "<PERSON><PERSON>", "selected": "Pasirinkta", "storage": "Saug<PERSON><PERSON>", "title": "Informacija moksliukams", "total": "", "version": "", "versionCopy": "", "versionNotAvailable": "", "width": "<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Pridėta į biblioteką", "copyStyles": "", "copyToClipboard": "Nukopijuota į iškarpinę.", "copyToClipboardAsPng": "", "fileSaved": "<PERSON><PERSON><PERSON>.", "fileSavedToFilename": "Išsaugota į {filename}", "canvas": "<PERSON><PERSON><PERSON><PERSON>", "selection": "", "pasteAsSingleElement": "", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Permatoma", "black": "", "white": "", "red": "", "pink": "", "grape": "", "violet": "", "gray": "", "blue": "", "cyan": "", "teal": "", "green": "", "yellow": "", "orange": "", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "", "center_heading_plus": "", "menuHint": ""}, "defaults": {"menuHint": "", "center_heading": "", "toolbarHint": "", "helpHint": ""}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}