#!/usr/bin/env node

import { config, validateConfig } from '../config/index.js';
import { apolloService } from '../services/apolloService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';

class ApolloTester {
  constructor() {
    this.outputDir = './data';
  }

  async runTests() {
    logger.info('🧪 Starting Apollo.io API Tests...\n');

    try {
      // Test 1: Connection Test
      await this.testConnection();
      
      // Test 2: Search for UK Education Contacts
      await this.testEducationSearch();
      
      // Test 3: Search for specific school contacts
      await this.testSchoolSearch();
      
      // Test 4: Search for organizations
      await this.testOrganizationSearch();
      
      logger.info('✅ All Apollo tests completed successfully!');
      
    } catch (error) {
      logger.error('❌ Apollo tests failed:', error);
      throw error;
    }
  }

  async testConnection() {
    logger.info('🔌 Testing Apollo API connection...');
    
    try {
      const result = await apolloService.testConnection();
      
      if (result.success) {
        logger.info('✅ Apollo API connection successful!');
        logger.info(`   Credits remaining: ${result.credits_remaining}`);
        logger.info(`   Rate limit: ${result.rate_limit}`);
      } else {
        logger.error('❌ Apollo API connection failed:', result.error);
        throw new Error('Apollo connection test failed');
      }
    } catch (error) {
      logger.error('❌ Connection test error:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async testEducationSearch() {
    logger.info('🎓 Testing education sector contact search...');
    
    try {
      const results = await apolloService.searchEducationContacts({
        q_keywords: 'headteacher OR principal',
        per_page: 5
      });
      
      logger.info(`✅ Found ${results.contacts.length} education contacts`);
      logger.info(`   Total available: ${results.pagination?.total_entries || 'Unknown'}`);
      
      if (results.contacts.length > 0) {
        const sample = results.contacts[0];
        logger.info('   Sample contact:');
        logger.info(`     Name: ${sample.name || 'N/A'}`);
        logger.info(`     Title: ${sample.title || 'N/A'}`);
        logger.info(`     Organization: ${sample.organization?.name || 'N/A'}`);
        logger.info(`     Email: ${sample.email ? '✓ Available' : '✗ Not available'}`);
      }
      
      // Save sample results
      await this.saveResults('apollo_education_search.json', results);
      
    } catch (error) {
      logger.error('❌ Education search test failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async testSchoolSearch() {
    logger.info('🏫 Testing specific school contact search...');
    
    // Test with some well-known UK schools/MATs
    const testSchools = [
      'Harris Federation',
      'Ark Schools',
      'United Learning',
      'Oasis Community Learning',
      'Star Academies'
    ];
    
    try {
      for (const schoolName of testSchools.slice(0, 2)) { // Test first 2 to save API credits
        logger.info(`   Searching for contacts at: ${schoolName}`);
        
        const results = await apolloService.searchSchoolContacts(schoolName);
        
        logger.info(`   ✅ Found ${results.contacts.length} contacts at ${schoolName}`);
        
        if (results.contacts.length > 0) {
          const sample = results.contacts[0];
          logger.info(`     Sample: ${sample.name} - ${sample.title}`);
        }
        
        // Add small delay between searches
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      logger.error('❌ School search test failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async testOrganizationSearch() {
    logger.info('🏢 Testing organization search...');
    
    try {
      const results = await apolloService.searchOrganizations('Academy Trust');
      
      logger.info(`✅ Found ${results.organizations.length} organizations`);
      logger.info(`   Total available: ${results.total_found}`);
      
      if (results.organizations.length > 0) {
        const sample = results.organizations[0];
        logger.info('   Sample organization:');
        logger.info(`     Name: ${sample.name}`);
        logger.info(`     Domain: ${sample.domain || 'N/A'}`);
        logger.info(`     Size: ${sample.size || 'N/A'} employees`);
        logger.info(`     Location: ${sample.location.city || 'N/A'}, ${sample.location.country || 'N/A'}`);
      }
      
      // Save results
      await this.saveResults('apollo_organization_search.json', results);
      
    } catch (error) {
      logger.error('❌ Organization search test failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async saveResults(filename, data) {
    try {
      const filepath = `${this.outputDir}/${filename}`;
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      logger.info(`💾 Results saved to ${filepath}`);
    } catch (error) {
      logger.error(`Error saving results to ${filename}:`, error);
    }
  }

  async generateTestReport() {
    const report = {
      test_date: new Date().toISOString(),
      apollo_api_key: config.apollo.apiKey ? 'Configured' : 'Missing',
      tests_run: [
        'Connection Test',
        'Education Search',
        'School Search', 
        'Organization Search'
      ],
      status: 'Completed',
      next_steps: [
        'Apollo API is working correctly',
        'Ready to integrate with school data collection',
        'Can proceed with contact enrichment for MATs and schools',
        'Consider upgrading Apollo plan for higher volume processing'
      ]
    };
    
    await this.saveResults('apollo_test_report.json', report);
    return report;
  }
}

// Main execution function
async function main() {
  logger.info('🚀 Starting Apollo.io Integration Tests');
  
  // Check if Apollo API key is configured
  if (!config.apollo.apiKey || config.apollo.apiKey === 'your_apollo_key_here') {
    logger.error('❌ Apollo API key not configured. Please check your .env file.');
    process.exit(1);
  }
  
  const tester = new ApolloTester();
  
  try {
    await tester.runTests();
    const report = await tester.generateTestReport();
    
    logger.info('📊 Test Summary:');
    logger.info(`   Status: ${report.status}`);
    logger.info(`   Tests Run: ${report.tests_run.length}`);
    logger.info('   Next Steps:');
    report.next_steps.forEach(step => logger.info(`     • ${step}`));
    
  } catch (error) {
    logger.error('❌ Apollo tests failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
