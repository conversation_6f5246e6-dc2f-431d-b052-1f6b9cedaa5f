#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SystemSetup {
  constructor() {
    this.requiredDirs = ['data', 'logs', 'data/schools', 'data/exports'];
    this.requiredEnvVars = [
      'COMPANIES_HOUSE_API_KEY',
      'APOLLO_API_KEY', 
      'HUNTER_API_KEY',
      'HUBSPOT_API_KEY',
      'SENDGRID_API_KEY',
      'OPENAI_API_KEY'
    ];
  }

  async run() {
    console.log('🚀 Setting up UK Schools Lead Generation System...\n');

    try {
      await this.createDirectories();
      await this.checkEnvironmentFile();
      await this.displaySetupInstructions();
      
      console.log('✅ Setup completed successfully!\n');
      console.log('📋 Next steps:');
      console.log('1. Configure your .env file with API keys');
      console.log('2. Run: npm install');
      console.log('3. Run: npm start');
      console.log('4. Visit: http://localhost:3000/api/config/check');
      
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    }
  }

  async createDirectories() {
    console.log('📁 Creating required directories...');
    
    for (const dir of this.requiredDirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`   ✓ Created: ${dir}`);
      } catch (error) {
        console.log(`   ⚠️  Directory ${dir} already exists or couldn't be created`);
      }
    }
    console.log('');
  }

  async checkEnvironmentFile() {
    console.log('🔧 Checking environment configuration...');
    
    const envPath = '.env';
    const envExamplePath = '.env.example';
    
    try {
      await fs.access(envPath);
      console.log('   ✓ .env file exists');
    } catch {
      try {
        await fs.copyFile(envExamplePath, envPath);
        console.log('   ✓ Created .env file from .env.example');
      } catch {
        console.log('   ⚠️  Could not create .env file. Please copy .env.example to .env manually');
      }
    }
    console.log('');
  }

  async displaySetupInstructions() {
    console.log('📋 SETUP INSTRUCTIONS');
    console.log('='.repeat(50));
    
    console.log('\n🔑 REQUIRED API KEYS:');
    console.log('Please obtain the following API keys and add them to your .env file:\n');
    
    const apiInstructions = [
      {
        name: 'Companies House API',
        env: 'COMPANIES_HOUSE_API_KEY',
        url: 'https://developer.company-information.service.gov.uk/get-started/',
        cost: 'FREE',
        description: 'UK company and MAT information'
      },
      {
        name: 'Apollo.io API',
        env: 'APOLLO_API_KEY', 
        url: 'https://www.apollo.io/',
        cost: '$49-149/month',
        description: 'Contact enrichment and email finding'
      },
      {
        name: 'Hunter.io API',
        env: 'HUNTER_API_KEY',
        url: 'https://hunter.io/',
        cost: '$49-399/month', 
        description: 'Email verification and domain search'
      },
      {
        name: 'HubSpot API',
        env: 'HUBSPOT_API_KEY',
        url: 'https://www.hubspot.com/',
        cost: 'FREE tier available',
        description: 'CRM and email campaigns'
      },
      {
        name: 'SendGrid API',
        env: 'SENDGRID_API_KEY',
        url: 'https://sendgrid.com/',
        cost: 'FREE tier available',
        description: 'Email delivery infrastructure'
      },
      {
        name: 'OpenAI API',
        env: 'OPENAI_API_KEY',
        url: 'https://platform.openai.com/',
        cost: 'Pay-per-use (~$20/month)',
        description: 'AI content generation'
      }
    ];

    apiInstructions.forEach((api, index) => {
      console.log(`${index + 1}. ${api.name}`);
      console.log(`   URL: ${api.url}`);
      console.log(`   Cost: ${api.cost}`);
      console.log(`   Purpose: ${api.description}`);
      console.log(`   Environment Variable: ${api.env}`);
      console.log('');
    });

    console.log('💰 ESTIMATED MONTHLY COSTS:');
    console.log('   Minimum Setup: ~$118/month');
    console.log('   Recommended Setup: ~$533/month');
    console.log('');

    console.log('🎯 PRIORITY ORDER:');
    console.log('   1. Companies House API (FREE) - Start here');
    console.log('   2. Apollo.io API - Essential for contact data');
    console.log('   3. HubSpot API - CRM functionality');
    console.log('   4. SendGrid API - Email delivery');
    console.log('   5. OpenAI API - AI personalization');
    console.log('   6. Hunter.io API - Email verification');
    console.log('');

    console.log('🚀 QUICK START:');
    console.log('   1. Get Companies House API key (FREE)');
    console.log('   2. Add it to .env file');
    console.log('   3. Run: npm run data:collect');
    console.log('   4. This will collect basic MAT data to get started');
    console.log('');
  }

  async checkSystemRequirements() {
    console.log('🔍 Checking system requirements...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      console.log(`   ✓ Node.js ${nodeVersion} (required: 18+)`);
    } else {
      console.log(`   ❌ Node.js ${nodeVersion} (required: 18+)`);
      throw new Error('Node.js 18 or higher is required');
    }
    
    console.log('');
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new SystemSetup();
  setup.run();
}
