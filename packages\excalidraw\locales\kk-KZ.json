{"labels": {"paste": "Қою", "pasteAsPlaintext": "", "pasteCharts": "Диаграммаларды қою", "selectAll": "Бәрін таңдау", "multiSelect": "", "moveCanvas": "", "cut": "Қию", "copy": "Көшіру", "copyAsPng": "", "copyAsSvg": "", "copyText": "", "copySource": "", "convertToCode": "", "bringForward": "", "sendToBack": "", "bringToFront": "", "sendBackward": "", "delete": "<PERSON><PERSON><PERSON>", "copyStyles": "Стильдерді көшіру", "pasteStyles": "Стильдерді қою", "stroke": "", "background": "", "fill": "", "strokeWidth": "", "strokeStyle": "", "strokeStyle_solid": "", "strokeStyle_dashed": "", "strokeStyle_dotted": "", "sloppiness": "", "opacity": "", "textAlign": "", "edges": "", "sharp": "", "round": "", "arrowheads": "Нұсқар ұштары", "arrowhead_none": "Жоқ", "arrowhead_arrow": "Нұсқар", "arrowhead_bar": "Тосқауыл", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Қаріп өлшемі", "fontFamily": "Қаріп тобы", "addWatermark": "", "handDrawn": "", "normal": "Қалыпты", "code": "", "small": "Кіш<PERSON>", "medium": "Орта", "large": "Үлкен", "veryLarge": "Өте үлкен", "solid": "", "hachure": "", "zigzag": "", "crossHatch": "", "thin": "", "bold": "", "left": "Солға", "center": "Ортаға", "right": "Оңға", "extraBold": "", "architect": "", "artist": "", "cartoonist": "", "fileTitle": "Файл атауы", "colorPicker": "", "canvasColors": "", "canvasBackground": "", "drawingCanvas": "", "layers": "", "actions": "", "language": "<PERSON><PERSON><PERSON>", "liveCollaboration": "", "duplicateSelection": "Көшірме", "untitled": "Атау<PERSON>сыз", "name": "", "yourName": "", "madeWithExcalidraw": "", "group": "", "ungroup": "", "collaborators": "", "showGrid": "", "addToLibrary": "", "removeFromLibrary": "", "libraryLoadingMessage": "", "libraries": "", "loadingScene": "", "align": "", "alignTop": "", "alignBottom": "", "alignLeft": "", "alignRight": "", "centerVertically": "", "centerHorizontally": "", "distributeHorizontally": "", "distributeVertically": "", "flipHorizontal": "", "flipVertical": "", "viewMode": "", "share": "", "showStroke": "", "showBackground": "", "toggleTheme": "", "personalLib": "", "excalidrawLib": "", "decreaseFontSize": "", "increaseFontSize": "", "unbindText": "", "bindText": "", "createContainerFromText": "", "link": {"edit": "", "editEmbed": "", "create": "", "createEmbed": "", "label": "", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "", "exit": ""}, "elementLock": {"lock": "", "unlock": "", "lockAll": "", "unlockAll": ""}, "statusPublished": "", "sidebarLock": "", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "", "hint_emptyLibrary": "", "hint_emptyPrivateLibrary": ""}, "buttons": {"clearReset": "", "exportJSON": "", "exportImage": "", "export": "", "copyToClipboard": "", "save": "", "saveAs": "", "load": "", "getShareableLink": "", "close": "Ж<PERSON><PERSON><PERSON>", "selectLanguage": "Тілді таңдау", "scrollBackToContent": "", "zoomIn": "", "zoomOut": "", "resetZoom": "", "menu": "Mәзір", "done": "<PERSON>а<PERSON><PERSON><PERSON>", "edit": "", "undo": "", "redo": "", "resetLibrary": "", "createNewRoom": "", "fullScreen": "", "darkMode": "", "lightMode": "", "zenMode": "", "objectsSnapMode": "", "exitZenMode": "", "cancel": "", "clear": "", "remove": "", "embed": "", "publishLibrary": "", "submit": "", "confirm": "", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "", "couldNotCreateShareableLink": "", "couldNotCreateShareableLinkTooBig": "", "couldNotLoadInvalidFile": "", "importBackendFailed": "", "cannotExportEmptyCanvas": "", "couldNotCopyToClipboard": "", "decryptFailed": "", "uploadedSecurly": "", "loadSceneOverridePrompt": "", "collabStopOverridePrompt": "", "errorAddingToLibrary": "", "errorRemovingFromLibrary": "", "confirmAddLibrary": "", "imageDoesNotContainScene": "", "cannotRestoreFromImage": "", "invalidSceneUrl": "", "resetLibrary": "", "removeItemsFromsLibrary": "", "invalidEncryptionKey": "", "collabOfflineWarning": ""}, "errors": {"unsupportedFileType": "", "imageInsertError": "Суретті жүктеу мүмкін болмады. Кейінірек қайталап көріңіз...", "fileTooBig": "Файл өте үлкен. Максималды рұқсат етілген көлем {{maxSize}}.", "svgImageInsertError": "", "failedToFetchImage": "", "invalidSVGString": "", "cannotResolveCollabServer": "", "importLibraryError": "", "collabSaveFailed": "", "collabSaveFailed_sizeExceeded": "", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "", "image": "Суретті қою", "rectangle": "", "diamond": "", "ellipse": "", "arrow": "Нұсқар", "line": "", "freedraw": "", "text": "Мәтін", "library": "", "lock": "", "penMode": "", "link": "", "eraser": "", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "", "selectedShapeActions": "", "shapes": ""}, "hints": {"canvasPanning": "", "linearElement": "", "freeDraw": "", "text": "", "embeddable": "", "text_selected": "", "text_editing": "", "linearElementMulti": "", "lockAngle": "", "resize": "", "resizeImage": "", "rotate": "", "lineEditor_info": "", "lineEditor_pointSelected": "", "lineEditor_nothingSelected": "", "placeImage": "", "publishLibrary": "", "bindTextToElement": "", "deepBoxSelect": "", "eraserRevert": "", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "", "canvasTooBig": "", "canvasTooBigTip": ""}, "errorSplash": {"headingMain": "", "clearCanvasMessage": "", "clearCanvasCaveat": "", "trackedToSentry": "", "openIssueMessage": "", "sceneContent": ""}, "roomDialog": {"desc_intro": "", "desc_privacy": "", "button_startSession": "", "button_stopSession": "", "desc_inProgressIntro": "", "desc_shareLink": "", "desc_exitSession": "", "shareTitle": ""}, "errorDialog": {"title": "Қате"}, "exportDialog": {"disk_title": "", "disk_details": "Сахна деректерін кейін қайта импорттауға болатын файлға экспорттаңыз.", "disk_button": "Файлға сақтау", "link_title": "Ортақ сілтеме", "link_details": "Тек оқуға арналған сілтеме ретінде экспорттау.", "link_button": "Сілтемеге экспорттау", "excalidrawplus_description": "Сахнаны өзіңіздің Excalidraw+ жұмыс кеңістігінде сақтаңыз.", "excalidrawplus_button": "Экспорт", "excalidrawplus_exportError": "Қазіргі уақытта Excalidraw+ үшін экспорттау мүмкін емес..."}, "helpDialog": {"blog": "Біздің блогты оқу", "click": "шерту", "deepSelect": "", "deepBoxSelect": "", "curvedArrow": "Майысқан нұсқар", "curvedLine": "Майысқан сызық", "documentation": "Құжаттама", "doubleClick": "қос шерту", "drag": "апару", "editor": "Өңдеу", "editLineArrowPoints": "", "editText": "", "github": "Қате таптыңыз ба? Жолдаңыз", "howto": "Біздің нұсқаулықтарды орындаңыз", "or": "немесе", "preventBinding": "Нұсқарды байланыстыруға жол бермеу", "tools": "", "shortcuts": "Пернетақта пәрмендері", "textFinish": "Өңдеуді аяқтау (мәтіндік редактор)", "textNewLine": "Жаңа жолға көшу (мәтіндік редактор)", "title": "Көмек", "view": "Көру", "zoomToFit": "Барлық элементтердің көлеміне сәйкес үлкейту", "zoomToSelection": "Таңдалғанды үлкейту", "toggleElementLock": "", "movePageUpDown": "", "movePageLeftRight": ""}, "clearCanvasDialog": {"title": ""}, "publishDialog": {"title": "", "itemName": "", "authorName": "", "githubUsername": "", "twitterUsername": "", "libraryName": "", "libraryDesc": "", "website": "", "placeholder": {"authorName": "", "libraryName": "", "libraryDesc": "", "githubHandle": "", "twitterHandle": "", "website": ""}, "errors": {"required": "", "website": ""}, "noteDescription": "", "noteGuidelines": "", "noteLicense": "", "noteItems": "", "atleastOneLibItem": "", "republishWarning": ""}, "publishSuccessDialog": {"title": "", "content": ""}, "confirmDialog": {"resetLibrary": "", "removeItemsFromLib": ""}, "imageExportDialog": {"header": "", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "", "scale": "", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "Сіздің сызбаларыңыз өтпелі шифрлеу арқылы шифрланған, сондықтан Excalidraw серверлері оларды ешқашан көрмейді.", "link": "Excalidraw қолданатын өтпелі шифрлеу туралы блог жазбасы"}, "stats": {"angle": "Бұрыш", "element": "Элемент", "elements": "Элементтер", "height": "Биіктігі", "scene": "Сахна", "selected": "Таңдалды", "storage": "Сақтау көлемі", "title": "", "total": "Барлығы", "version": "Нұсқа", "versionCopy": "Көшіру үшін басыңыз", "versionNotAvailable": "Бұл нұсқа қолжетімсіз", "width": "<PERSON>н<PERSON>"}, "toast": {"addedToLibrary": "", "copyStyles": "Стильдер көшірілді.", "copyToClipboard": "", "copyToClipboardAsPng": "", "fileSaved": "Файл сақталды.", "fileSavedToFilename": "{filename} сақталды", "canvas": "", "selection": "таңдау", "pasteAsSingleElement": "", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "", "black": "", "white": "", "red": "", "pink": "", "grape": "", "violet": "", "gray": "", "blue": "", "cyan": "", "teal": "", "green": "", "yellow": "", "orange": "", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "", "center_heading_plus": "", "menuHint": ""}, "defaults": {"menuHint": "", "center_heading": "", "toolbarHint": "", "helpHint": ""}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}