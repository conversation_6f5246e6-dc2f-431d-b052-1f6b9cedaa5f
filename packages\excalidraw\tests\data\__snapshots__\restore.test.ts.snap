// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`restoreElements > should restore arrow element correctly 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": null,
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id-arrow01",
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      100,
      100,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`restoreElements > should restore correctly with rectangle, ellipse and diamond elements 1`] = `
{
  "angle": 0,
  "backgroundColor": "blue",
  "boundElements": [],
  "customData": undefined,
  "fillStyle": "cross-hatch",
  "frameId": null,
  "groupIds": [
    "1",
    "2",
    "3",
  ],
  "height": 200,
  "id": "1",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 10,
  "roughness": 2,
  "roundness": {
    "type": 3,
  },
  "seed": Any<Number>,
  "strokeColor": "red",
  "strokeStyle": "dashed",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 10,
  "y": 20,
}
`;

exports[`restoreElements > should restore correctly with rectangle, ellipse and diamond elements 2`] = `
{
  "angle": 0,
  "backgroundColor": "blue",
  "boundElements": [],
  "customData": undefined,
  "fillStyle": "cross-hatch",
  "frameId": null,
  "groupIds": [
    "1",
    "2",
    "3",
  ],
  "height": 200,
  "id": "2",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 10,
  "roughness": 2,
  "roundness": {
    "type": 3,
  },
  "seed": Any<Number>,
  "strokeColor": "red",
  "strokeStyle": "dashed",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 10,
  "y": 20,
}
`;

exports[`restoreElements > should restore correctly with rectangle, ellipse and diamond elements 3`] = `
{
  "angle": 0,
  "backgroundColor": "blue",
  "boundElements": [],
  "customData": undefined,
  "fillStyle": "cross-hatch",
  "frameId": null,
  "groupIds": [
    "1",
    "2",
    "3",
  ],
  "height": 200,
  "id": "3",
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 10,
  "roughness": 2,
  "roundness": {
    "type": 3,
  },
  "seed": Any<Number>,
  "strokeColor": "red",
  "strokeStyle": "dashed",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 10,
  "y": 20,
}
`;

exports[`restoreElements > should restore freedraw element correctly 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id-freedraw01",
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      10,
      10,
    ],
  ],
  "pressures": [],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "simulatePressure": true,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "freedraw",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`restoreElements > should restore line and draw elements correctly 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [],
  "customData": undefined,
  "endArrowhead": null,
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id-line01",
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      100,
      100,
    ],
  ],
  "polygon": false,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "line",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`restoreElements > should restore line and draw elements correctly 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [],
  "customData": undefined,
  "endArrowhead": null,
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id-draw01",
  "index": "a1",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      100,
      100,
    ],
  ],
  "polygon": false,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "line",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`restoreElements > should restore text element correctly passing value for each attribute 1`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": [],
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 1,
  "fontSize": 14,
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id-text01",
  "index": "a0",
  "isDeleted": false,
  "lineHeight": "1.25000",
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "text",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "text",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 100,
  "x": -20,
  "y": "-8.75000",
}
`;

exports[`restoreElements > should restore text element correctly with unknown font family, null text and undefined alignment 1`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": [],
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "font": "10 unknown",
  "fontFamily": 5,
  "fontSize": 10,
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": "id-text01",
  "index": "a0",
  "isDeleted": true,
  "lineHeight": "1.25000",
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 100,
  "x": 0,
  "y": 0,
}
`;
