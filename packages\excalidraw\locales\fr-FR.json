{"labels": {"paste": "<PERSON><PERSON>", "pasteAsPlaintext": "Coller comme texte brut", "pasteCharts": "Coller les graphiques", "selectAll": "<PERSON><PERSON>", "multiSelect": "Ajouter l'élément à la sélection", "moveCanvas": "<PERSON><PERSON><PERSON><PERSON>", "cut": "Couper", "copy": "<PERSON><PERSON><PERSON>", "copyAsPng": "Copier dans le presse-papier en PNG", "copyAsSvg": "Copier dans le presse-papier en SVG", "copyText": "Co<PERSON>r dans le presse-papier en tant que texte", "copySource": "Copier la source dans le presse-papiers", "convertToCode": "Convertir en code", "bringForward": "Envoyer vers l'avant", "sendToBack": "Déplacer à l'arrière-plan", "bringToFront": "Mettre au premier plan", "sendBackward": "Reculer d'un plan", "delete": "<PERSON><PERSON><PERSON><PERSON>", "copyStyles": "Copier les styles", "pasteStyles": "Coller les styles", "stroke": "<PERSON><PERSON><PERSON>", "background": "Arrière-plan", "fill": "Remplissage", "strokeWidth": "<PERSON><PERSON> du contour", "strokeStyle": "Style du trait", "strokeStyle_solid": "<PERSON><PERSON><PERSON>", "strokeStyle_dashed": "<PERSON>ire<PERSON>", "strokeStyle_dotted": "Pointillés", "sloppiness": "Style de tracé", "opacity": "Transparence", "textAlign": "Alignement du texte", "edges": "<PERSON><PERSON>", "sharp": "<PERSON><PERSON>", "round": "<PERSON><PERSON><PERSON><PERSON>", "arrowheads": "Extrémités", "arrowhead_none": "Sans", "arrowhead_arrow": "Flèche", "arrowhead_bar": "<PERSON><PERSON>", "arrowhead_circle": "Cercle", "arrowhead_circle_outline": "<PERSON><PERSON> du cercle", "arrowhead_triangle": "Triangle", "arrowhead_triangle_outline": "Triangle (contour)", "arrowhead_diamond": "<PERSON><PERSON><PERSON>", "arrowhead_diamond_outline": "", "fontSize": "Taille de la police", "fontFamily": "Police", "addWatermark": "Ajouter \"Réalisé avec Excalidraw\"", "handDrawn": "À main levée", "normal": "Normale", "code": "Code", "small": "Petite", "medium": "<PERSON><PERSON><PERSON>", "large": "Grande", "veryLarge": "Très grande", "solid": "Solide", "hachure": "<PERSON><PERSON><PERSON>", "zigzag": "Zigzag", "crossHatch": "Hachures croisées", "thin": "Fine", "bold": "<PERSON><PERSON><PERSON>", "left": "À gauche", "center": "Au centre", "right": "À droite", "extraBold": "<PERSON><PERSON><PERSON>", "architect": "Architecte", "artist": "Artiste", "cartoonist": "Caricaturiste", "fileTitle": "Nom du fichier", "colorPicker": "<PERSON><PERSON><PERSON><PERSON> de couleur", "canvasColors": "Utilisé sur la zone de dessin", "canvasBackground": "Arrière-plan du canevas", "drawingCanvas": "Zone de dessin", "layers": "Disposition", "actions": "Actions", "language": "<PERSON><PERSON>", "liveCollaboration": "Collaboration en direct...", "duplicateSelection": "<PERSON><PERSON><PERSON><PERSON>", "untitled": "Sans-titre", "name": "Nom", "yourName": "Votre nom", "madeWithExcalidraw": "Fait avec Excalidraw", "group": "Grouper la sélection", "ungroup": "Dégrouper la sélection", "collaborators": "Collaborateurs", "showGrid": "Afficher la grille", "addToLibrary": "Ajouter à la bibliothèque", "removeFromLibrary": "Supprimer de la bibliothèque", "libraryLoadingMessage": "Chargement de la bibliothèque…", "libraries": "Parcourir les bibliothèques", "loadingScene": "Chargement de la scène…", "align": "Alignement", "alignTop": "Aligner en haut", "alignBottom": "Aligner en bas", "alignLeft": "<PERSON><PERSON><PERSON> à gauche", "alignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "centerVertically": "Centrer verticalement", "centerHorizontally": "Centrer horizontalement", "distributeHorizontally": "Répartir horizontalement", "distributeVertically": "Répartir verticalement", "flipHorizontal": "Retourner horizontalement", "flipVertical": "Retourner verticalement", "viewMode": "Mode présentation", "share": "Partager", "showStroke": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de couleur de trait", "showBackground": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de couleur de fond", "toggleTheme": "Changer le thème", "personalLib": "Bibliothèque personnelle", "excalidrawLib": "Bibliothèque Excalidraw", "decreaseFontSize": "Diminuer la taille de police", "increaseFontSize": "Augmenter la taille de la police", "unbindText": "Dissocier le texte", "bindText": "Associer le texte au conteneur", "createContainerFromText": "Encadrer le texte dans un conteneur", "link": {"edit": "Modifier le lien", "editEmbed": "Éditer le lien & intégrer", "create": "Ajouter un lien", "createEmbed": "Créer un lien & intégrer", "label": "<PERSON><PERSON>", "labelEmbed": "Lier & intégrer", "empty": "Aucun lien défini"}, "lineEditor": {"edit": "Modifier la ligne", "exit": "<PERSON><PERSON><PERSON> l'éditeur de ligne"}, "elementLock": {"lock": "Verrouiller", "unlock": "Déverrouiller", "lockAll": "<PERSON><PERSON> ve<PERSON>", "unlockAll": "<PERSON><PERSON>"}, "statusPublished": "<PERSON><PERSON><PERSON>", "sidebarLock": "Maintenir la barre latérale ouverte", "selectAllElementsInFrame": "Sélectionner tous les éléments du cadre", "removeAllElementsFromFrame": "Supprimer tous les éléments du cadre", "eyeDropper": "<PERSON><PERSON> la couleur depuis la toile", "textToDiagram": "Texte vers Diagramme", "prompt": "Consignes"}, "library": {"noItems": "Aucun élément n'a encore été ajouté ...", "hint_emptyLibrary": "Sélectionnez un élément sur le canevas pour l'ajouter ici ou installez une bibliothèque depuis le dépôt public, ci-dessous.", "hint_emptyPrivateLibrary": "Sélectionnez un élément sur le canevas pour l'ajouter ici."}, "buttons": {"clearReset": "Réinitialiser le canevas", "exportJSON": "Exporter comme fichier", "exportImage": "Exporter l'image...", "export": "Enregistrer sous...", "copyToClipboard": "Copier dans le presse-papier", "save": "Enregistrer dans le fichier actuel", "saveAs": "Enregistrer sous", "load": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getShareableLink": "Obtenir un lien de partage", "close": "<PERSON><PERSON><PERSON>", "selectLanguage": "Choisir une langue", "scrollBackToContent": "Revenir au contenu", "zoomIn": "<PERSON>mer", "zoomOut": "Dézoomer", "resetZoom": "Réinitialiser le zoom", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "undo": "Annuler", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetLibrary": "Réinitialiser la bibliothèque", "createNewRoom": "<PERSON><PERSON>er une nouvelle salle", "fullScreen": "Plein écran", "darkMode": "Mode sombre", "lightMode": "Mode clair", "zenMode": "Mode zen", "objectsSnapMode": "Aimanter aux objets", "exitZenMode": "<PERSON><PERSON><PERSON> le mode zen", "cancel": "Annuler", "clear": "<PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "embed": "Activer/Désactiver l'intégration", "publishLibrary": "Publier", "submit": "Envoyer", "confirm": "Confirmer", "embeddableInteractionButton": "Cliquez pour interagir"}, "alerts": {"clearReset": "L'intégralité du canevas va être effacée. Êtes-vous sûr ?", "couldNotCreateShareableLink": "Impossible de créer un lien de partage.", "couldNotCreateShareableLinkTooBig": "Impossible de créer un lien de partage : la scène est trop volumineuse", "couldNotLoadInvalidFile": "Impossible de charger un fichier invalide", "importBackendFailed": "L'importation depuis le serveur a échoué.", "cannotExportEmptyCanvas": "Impossible d'exporter un canevas vide.", "couldNotCopyToClipboard": "Impossible de copier dans le presse-papiers.", "decryptFailed": "Les données n'ont pas pu être déchiffrées.", "uploadedSecurly": "Le téléchargement a été sécurisé avec un chiffrement de bout en bout, ce qui signifie que ni Excalidraw ni personne d'autre ne peut en lire le contenu.", "loadSceneOverridePrompt": "Le chargement d'un dessin externe remplacera votre contenu actuel. Souhaitez-vous continuer ?", "collabStopOverridePrompt": "Arrêter la session écrasera votre précédent dessin stocké localement. Êtes-vous sûr·e ?\n\n(Si vous voulez garder votre dessin local, fermez simplement l'onglet du navigateur à la place.)", "errorAddingToLibrary": "Impossible d'ajouter l'élément à la bibliothèque", "errorRemovingFromLibrary": "Impossible de retirer l'élément de la bibliothèque", "confirmAddLibrary": "Cela va ajouter {{numShapes}} forme(s) à votre bibliothèque. Êtes-vous sûr·e ?", "imageDoesNotContainScene": "Cette image ne semble pas contenir de données de scène. Avez-vous activé l'intégration de scène lors de l'exportation ?", "cannotRestoreFromImage": "Impossible de restaurer la scène depuis ce fichier image", "invalidSceneUrl": "Impossible d'importer la scène depuis l'URL fournie. Elle est soit incorrecte, soit ne contient pas de données JSON Excalidraw valides.", "resetLibrary": "<PERSON>la va effacer votre bibliothèque. Êtes-vous sûr·e ?", "removeItemsFromsLibrary": "Supprimer {{count}} élément(s) de la bibliothèque ?", "invalidEncryptionKey": "La clé de chiffrement doit comporter 22 caractères. La collaboration en direct est désactivée.", "collabOfflineWarning": "Aucune connexion internet disponible.\nVos modifications ne seront pas enregistrées !"}, "errors": {"unsupportedFileType": "Type de fichier non supporté.", "imageInsertError": "Impossible d'insérer l'image. Réessayez plus tard...", "fileTooBig": "Le fichier est trop volumineux. La taille maximale autorisée est de {{maxSize}}.", "svgImageInsertError": "Impossible d'insérer l'image SVG. Le balisage SVG semble invalide.", "failedToFetchImage": "Échec de récupération de l'image.", "invalidSVGString": "SVG invalide.", "cannotResolveCollabServer": "Impossible de se connecter au serveur collaboratif. Veuillez recharger la page et réessayer.", "importLibraryError": "Impossible de charger la bibliothèque", "collabSaveFailed": "Impossible d'enregistrer dans la base de données en arrière-plan. Si des problèmes persistent, vous devriez enregistrer votre fichier localement pour vous assurer de ne pas perdre votre travail.", "collabSaveFailed_sizeExceeded": "Impossible d'enregistrer dans la base de données en arrière-plan, le tableau semble trop grand. Vous devriez enregistrer le fichier localement pour vous assurer de ne pas perdre votre travail.", "imageToolNotSupported": "Les images sont désactivées.", "brave_measure_text_error": {"line1": "On dirait que vous utilisez le navigateur Brave avec l'option <bold>Bloquer agressivement le fichage</bold> activée.", "line2": "<PERSON><PERSON> pourrait entraîner des problèmes avec les <bold>Éléments Textuels</bold> dans vos dessins.", "line3": "Nous recommandons fortement de désactiver cette option. Vous pouvez suivre <link>ces instructions</link> pour savoir comment faire.", "line4": "Si désactiver cette option de résout pas le problème d'affichage des éléments textuels, veuillez ouvrir un <issueLink>ticket</issueLink> sur notre GitHub, ou écrivez-nous sur notre <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "Les éléments intégrés ne peuvent pas être ajoutés à la librairie.", "iframe": "", "image": "Le support pour l'ajout d'images à la librairie arrive bientôt !"}, "asyncPasteFailedOnRead": "Impossible de coller (impossible de lire le presse-papiers système).", "asyncPasteFailedOnParse": "Impossible de coller.", "copyToSystemClipboardFailed": "Échec de la copie dans le presse-papiers."}, "toolBar": {"selection": "Sélection", "image": "Insérer une image", "rectangle": "Rectangle", "diamond": "<PERSON><PERSON><PERSON>", "ellipse": "Ellipse", "arrow": "Flèche", "line": "Ligne", "freedraw": "<PERSON><PERSON><PERSON>", "text": "Texte", "library": "Bibliothèque", "lock": "Garder l'outil sélectionné actif après le dessin", "penMode": "Mode stylo - <PERSON><PERSON><PERSON> le toucher", "link": "Ajouter/mettre à jour le lien pour une forme sélectionnée", "eraser": "<PERSON><PERSON>", "frame": "<PERSON>il de cadre", "magicframe": "", "embeddable": "Intégration Web", "laser": "Pointeur laser", "hand": "Mains (outil de déplacement de la vue)", "extraTools": "Plus d'outils", "mermaidToExcalidraw": "De Mermaid à Excalidraw", "magicSettings": "Paramètres IA"}, "headings": {"canvasActions": "Actions du canevas", "selectedShapeActions": "Actions pour la forme sélectionnée", "shapes": "Formes"}, "hints": {"canvasPanning": "Pour déplacer la zone de dessin, maintenez la molette de la souris enfoncée ou la barre d'espace tout en faisant glisser, ou utiliser l'outil main.", "linearElement": "Cliquez pour démarrer plusieurs points, faites glisser pour une seule ligne", "freeDraw": "Cliquez et faites glissez, rel<PERSON><PERSON>z quand vous avez terminé", "text": "Astuce : vous pouvez aussi ajouter du texte en double-cliquant n'importe où avec l'outil de sélection", "embeddable": "Cliquez et glissez pour créer une intégration de site web", "text_selected": "Double-cliquez ou appuyez sur ENTRÉE pour modifier le texte", "text_editing": "Appuyez sur ÉCHAP ou Ctrl/Cmd+ENTRÉE pour terminer l'édition", "linearElementMulti": "Cliquez sur le dernier point ou appuyez sur Échap ou Entrée pour terminer", "lockAngle": "Vous pouvez restreindre l'angle en maintenant MAJ", "resize": "V<PERSON> pouvez conserver les proportions en maintenant la touche MAJ pendant le redimensionnement, maintenez la touche ALT pour redimensionner par rapport au centre", "resizeImage": "Vous pouvez redimensionner librement en maintenant SHIFT,\nmaintenez ALT pour redimensionner depuis le centre", "rotate": "Vous pouvez restreindre les angles en maintenant MAJ pendant la rotation", "lineEditor_info": "Maintenez CtrlOrCmd et Double-cliquez ou appuyez sur CtrlOrCmd + Entrée pour modifier les points", "lineEditor_pointSelected": "Appuyer sur Suppr. pour supprimer des points, Ctrl ou Cmd+D pour dupliquer, ou faire glisser pour déplacer", "lineEditor_nothingSelected": "Sélectionner un point pour éditer (maintenir la touche MAJ pour en sélectionner plusieurs),\nou maintenir la touche Alt enfoncée et cliquer pour ajouter de nouveaux points", "placeImage": "Cliquez pour placer l'image, ou cliquez et faites glisser pour définir sa taille manuellement", "publishLibrary": "Publier votre propre bibliothèque", "bindTextToElement": "Appuyer sur Entrée pour ajouter du texte", "deepBoxSelect": "Maintenir Ctrl ou Cmd pour sélectionner dans les groupes et empêcher le déplacement", "eraserRevert": "Maintenez Alt enfoncé pour annuler les éléments marqués pour suppression", "firefox_clipboard_write": "Cette fonctionnalité devrait pouvoir être activée en définissant l'option \"dom.events.asyncClipboard.clipboard.clipboardItem\" à \"true\". Pour modifier les paramètres du navigateur dans Firefox, visitez la page \"about:config\".", "disableSnapping": "Maintenez CtrlOuCmd pour désactiver l'aimantation"}, "canvasError": {"cannotShowPreview": "Impossible d’afficher l’aperçu", "canvasTooBig": "Le canevas est peut-être trop grand.", "canvasTooBigTip": "Astuce : essayez de rapprocher un peu les éléments les plus éloignés."}, "errorSplash": {"headingMain": "Une erreur est survenue. Essayez <button>de recharger la page.</button>", "clearCanvasMessage": "Si le rechargement ne résout pas l'erreur, essayez <button>effacement du canevas.</button>", "clearCanvasCaveat": " <PERSON><PERSON> entraînera une perte du travail ", "trackedToSentry": "L'erreur avec l'identifiant {{eventId}} a été enregistrée dans notre système.", "openIssueMessage": "Nous avons fait très attention à ne pas inclure les informations de votre scène dans l'erreur. Si votre scène n'est pas privée, veuillez envisager de poursuivre sur notre <button>outil de suivi des bugs.</button> Veuillez inclure les informations ci-dessous en les copiant-collant dans le ticket GitHub.", "sceneContent": "Contenu de la scène :"}, "roomDialog": {"desc_intro": "Vous pouvez inviter des personnes à collaborer avec vous sur votre scène actuelle.", "desc_privacy": "Pas d'inquiétude, la session utilise le chiffrement de bout en bout, donc tout ce que vous dessinez restera privé. Même notre serveur ne pourra voir ce que vous faites.", "button_startSession": "<PERSON><PERSON><PERSON><PERSON> la <PERSON>", "button_stopSession": "<PERSON><PERSON><PERSON><PERSON>", "desc_inProgressIntro": "La session de collaboration en direct est maintenant en cours.", "desc_shareLink": "Partagez ce lien avec les personnes avec lesquelles vous souhaitez collaborer :", "desc_exitSession": "Arrêter la session vous déconnectera de la salle, mais vous pourrez continuer à travailler avec la scène, localement. Notez que cela n'affectera pas les autres personnes, et ils pourront toujours collaborer sur leur version.", "shareTitle": "Rejoindre une session de collaboration en direct sur Excalidraw"}, "errorDialog": {"title": "<PERSON><PERSON><PERSON>"}, "exportDialog": {"disk_title": "Enregistrer sur le disque", "disk_details": "Exporter les données de la scène comme un fichier que vous pourrez importer ultérieurement.", "disk_button": "Enregistrer comme fichier", "link_title": "Lien partageable", "link_details": "Exporter comme un lien en lecture seule.", "link_button": "Exporter comme lien", "excalidrawplus_description": "Enregistrer la scène dans votre espace de travail Excalidraw+.", "excalidrawplus_button": "Exporter", "excalidrawplus_exportError": "Impossible d'exporter vers Excalidraw+ pour le moment..."}, "helpDialog": {"blog": "Lire notre blog", "click": "clic", "deepSelect": "Sélection dans les groupes", "deepBoxSelect": "Sélectionner dans les groupes, et empêcher le déplacement", "curvedArrow": "<PERSON><PERSON><PERSON><PERSON> cour<PERSON>", "curvedLine": "Ligne cour<PERSON>ée", "documentation": "Documentation", "doubleClick": "double-clic", "drag": "glisser", "editor": "<PERSON><PERSON><PERSON>", "editLineArrowPoints": "Modifier les points de ligne/flèche", "editText": "Modifier le texte / ajouter un libellé", "github": "Problème trouvé ? Soumettre", "howto": "Suivez nos guides", "or": "ou", "preventBinding": "Empêcher la liaison de flèche", "tools": "Outils", "shortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "textFinish": "Terminer l'édition (éditeur de texte)", "textNewLine": "Ajouter une nouvelle ligne (éditeur de texte)", "title": "Aide", "view": "Affichage", "zoomToFit": "Zoomer pour voir tous les éléments", "zoomToSelection": "Zoomer sur la sélection", "toggleElementLock": "Verrouiller/déverrouiller la sélection", "movePageUpDown": "<PERSON><PERSON><PERSON><PERSON> la page vers le haut/bas", "movePageLeftRight": "<PERSON><PERSON><PERSON>r la page vers la gauche/droite"}, "clearCanvasDialog": {"title": "Effacer la zone de dessin"}, "publishDialog": {"title": "Publier la bibliothèque", "itemName": "Nom de l’élément", "authorName": "Nom de l'auteur", "githubUsername": "Nom d'utilisateur GitHub", "twitterUsername": "Nom d'utilisateur Twitter", "libraryName": "Nom de la bibliothèque", "libraryDesc": "Description de la bibliothèque", "website": "Site web", "placeholder": {"authorName": "Votre nom ou nom d'utilisateur", "libraryName": "Nom de votre bibliothèque", "libraryDesc": "Description de votre bibliothèque pour aider les gens à comprendre son usage", "githubHandle": "Nom d'utilisateur GitHub (optionnel), pour que tu puisses modifier la bibliothèque une fois soumise pour vérification", "twitterHandle": "Nom d'utilisateur Twitter (optionnel), pour savoir qui créditer lors de la promotion sur Twitter", "website": "Lien vers votre site web personnel ou autre (optionnel)"}, "errors": {"required": "Requis", "website": "Entrer une URL valide"}, "noteDescription": "Soumets ta bibliothèque pour l'inclure au <link>dépôt de bibliothèque publique</link>pour permettre son utilisation par autrui dans leurs dessins.", "noteGuidelines": "La bibliothèque doit d'abord être approuvée manuellement. Veuillez lire les <link>lignes directrices</link> avant de la soumettre. Vous aurez besoin d'un compte GitHub pour communiquer et apporter des modifications si demandé, mais ce n'est pas obligatoire.", "noteLicense": "En soumettant, vous acceptez que la bibliothèque soit publiée sous la <link>Licence MIT, </link>ce qui en gros signifie que tout le monde peut l'utiliser sans restrictions.", "noteItems": "Chaque élément de la bibliothèque doit avoir son propre nom afin qu'il soit filtrable. Les éléments de bibliothèque suivants seront inclus :", "atleastOneLibItem": "Veuillez sélectionner au moins un élément de bibliothèque pour commencer", "republishWarning": "Remarque : certains des éléments sélectionnés sont marqués comme étant déjà publiés/soumis. V<PERSON> devez uniquement resoumettre des éléments lors de la mise à jour d'une bibliothèque ou d'une soumission existante."}, "publishSuccessDialog": {"title": "Bibliothèque soumise", "content": "<PERSON><PERSON><PERSON> {{author<PERSON><PERSON>}}. Votre bibliothèque a été soumise pour examen. Vous pouvez suivre le statut<link>ici</link>"}, "confirmDialog": {"resetLibrary": "Réinitialiser la bibliothèque", "removeItemsFromLib": "Enlever les éléments sélectionnés de la bibliothèque"}, "imageExportDialog": {"header": "Exporter l'image", "label": {"withBackground": "<PERSON>ond", "onlySelected": "Uniquement la sélection", "darkMode": "Mode sombre", "embedScene": "Intégrer la scène", "scale": "<PERSON><PERSON><PERSON>", "padding": "Marge interne"}, "tooltip": {"embedScene": "Les données de la scène seront sauvegardées dans le fichier PNG/SVG exporté afin que la scène puisse être restaurée depuis celui-ci.\nCela augmentera la taille du fichier exporté."}, "title": {"exportToPng": "Exporter en PNG", "exportToSvg": "Exporter en SVG", "copyPngToClipboard": "Co<PERSON>r le PNG dans le presse-papier"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Copier dans le presse-papier"}}, "encrypted": {"tooltip": "Vos dessins sont chiffrés de bout en bout, les serveurs d'Excalidraw ne les verront jamais.", "link": "Article de blog sur le chiffrement de bout en bout dans Excalidraw"}, "stats": {"angle": "<PERSON><PERSON>", "element": "É<PERSON>ment", "elements": "Éléments", "height": "<PERSON><PERSON>", "scene": "<PERSON><PERSON>", "selected": "Sélection", "storage": "Stockage", "title": "Stats pour les nerds", "total": "Total", "version": "Version", "versionCopy": "Cliquer pour copier", "versionNotAvailable": "Version non disponible", "width": "<PERSON><PERSON>"}, "toast": {"addedToLibrary": "Ajouté à la bibliothèque", "copyStyles": "Styles copiés.", "copyToClipboard": "Copié dans le presse-papier.", "copyToClipboardAsPng": "{{exportSelection}} copié dans le presse-papier en PNG\n({{exportColorScheme}})", "fileSaved": "<PERSON><PERSON><PERSON> enregistré.", "fileSavedToFilename": "Enregistré sous {filename}", "canvas": "<PERSON><PERSON>", "selection": "sélection", "pasteAsSingleElement": "Utiliser {{shortcut}} pour coller comme un seul élément,\nou coller dans un éditeur de texte existant", "unableToEmbed": "Intégrer cet URL n'est actuellement pas autorisé. Ouvrez un ticket sur GitHub pour demander son ajout à la liste blanche", "unrecognizedLinkFormat": "Le lien que vous avez intégré ne correspond pas au format attendu. <PERSON><PERSON><PERSON>z essayer de coller la chaîne d'intégration fournie par le site source"}, "colors": {"transparent": "Transparent", "black": "Noir", "white": "<PERSON>", "red": "Rouge", "pink": "<PERSON>", "grape": "<PERSON><PERSON>", "violet": "Violet", "gray": "<PERSON><PERSON>", "blue": "Bleu", "cyan": "<PERSON><PERSON>", "teal": "Turquoise", "green": "<PERSON>ert", "yellow": "Jaune", "orange": "Orange", "bronze": "Bronze"}, "welcomeScreen": {"app": {"center_heading": "Toutes vos données sont sauvegardées en local dans votre navigateur.", "center_heading_plus": "<PERSON><PERSON><PERSON><PERSON>-vous plutôt aller à Excalidraw+ à la place ?", "menuHint": "Exportation, préférences, langues, ..."}, "defaults": {"menuHint": "Exportation, préférences et plus...", "center_heading": "Diagrammes. Rendus. Simples.", "toolbarHint": "Choisissez un outil et commencez à dessiner !", "helpHint": "<PERSON><PERSON><PERSON><PERSON> et aide"}}, "colorPicker": {"mostUsedCustomColors": "Couleurs personnalisées les plus fréquemment utilisées", "colors": "Couleurs", "shades": "Nuances", "hexCode": "Code hex", "noShades": "Aucune nuance disponible pour cette couleur"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Exporter en image", "button": "Exporter en image", "description": "Exporter les données de la scène comme une image que vous pourrez importer ultérieurement."}, "saveToDisk": {"title": "Sauvegarder sur le disque", "button": "Sauvegarder sur le disque", "description": "Exporter les données de la scène comme un fichier que vous pourrez importer ultérieurement."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Exporter vers Excalidraw+", "description": "Enregistrer la scène dans votre espace de travail Excalidraw+."}}, "modal": {"loadFromFile": {"title": "Charger depuis un fichier", "button": "Charger depuis un fichier", "description": "Charger depuis un fichier va <bold>remplacer votre contenu existant</bold>.<br></br><PERSON><PERSON> pouvez d'abord sauvegarder votre dessin en utilisant l'une des options ci-dessous."}, "shareableLink": {"title": "Charger depuis un lien", "button": "Remplacer mon contenu", "description": "Charger un dessin externe va <bold>remplacer votre contenu existant</bold>.<br></br><PERSON><PERSON> pouvez d'abord sauvegarder votre dessin en utilisant l'une des options ci-dessous."}}}, "mermaid": {"title": "De Mermaid à Excalidraw", "button": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "syntax": "Syntaxe Mermaid", "preview": "Prévisualisation"}}