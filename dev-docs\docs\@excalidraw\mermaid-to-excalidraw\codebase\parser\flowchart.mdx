# Flowchart Parser

As mentioned in the previous section, we use [getDiagramFromText](https://github.com/mermaid-js/mermaid/blob/00d06c7282a701849793680c1e97da1cfdfcce62/packages/mermaid/src/Diagram.ts#L80) to parse the full defination and get the [Diagram](https://github.com/mermaid-js/mermaid/blob/00d06c7282a701849793680c1e97da1cfdfcce62/packages/mermaid/src/Diagram.ts#L15) json from it.

In this section we will be diving into how the [flowchart parser](https://github.com/excalidraw/mermaid-to-excalidraw/blob/master/src/parser/flowchart.ts#L256) works behind the scenes.

![image](https://github.com/excalidraw/excalidraw/assets/11256141/2a097bbb-64bf-49d6-bf7f-21172bdb538d)

We use `diagram.parser.yy` attribute to parse the data. If you want to know more about how the `diagram.parse.yy` attribute looks like, you can check it [here](https://github.com/mermaid-js/mermaid/blob/00d06c7282a701849793680c1e97da1cfdfcce62/packages/mermaid/src/diagrams/flowchart/flowDb.js#L768), however for scope of flowchart we are using **3** APIs from this parser to compute `vertices`, `edges` and `clusters` as we need these data to transform to [ExcalidrawElementSkeleton](https://github.com/excalidraw/excalidraw/blob/master/packages/excalidraw/data/transform.ts#L133C13-L133C38).


For computing `vertices` and `edge`s lets consider the below svg generated by mermaid

![image](https://github.com/excalidraw/excalidraw/assets/11256141/d7013305-0b90-4fa0-a66e-b4f4604ad0b2)


## Computing the vertices

We use `getVertices` API from `diagram.parse.yy` to get the vertices for a given flowchart.

Considering the same example this is the response from the API

```js
{
	"start": {
		"id": "start",
		"labelType": "text",
		"domId": "flowchart-start-1414",
		"styles": [],
		"classes": [],
		"text": "start",
		"props": {}
	},
	"stop": {
		"id": "stop",
		"labelType": "text",
		"domId": "flowchart-stop-1415",
		"styles": [],
		"classes": [],
		"text": "stop",
		"props": {}
	}
}
```
The dimensions and position is missing in this response and we need that to transform to [ExcalidrawElementSkeleton](https://github.com/excalidraw/excalidraw/blob/master/packages/excalidraw/data/transform.ts#L133C13-L133C38), for this we have our own parser [`parseVertex`](https://github.com/excalidraw/mermaid-to-excalidraw/blob/master/src/parseMermaid.ts#L178) which takes the above response and uses the `svg` together to compute position, dimensions and cleans up the response.

 The final output from `parseVertex` looks like :point_down:

```js
{
	"start": {
		"id": "start",
		"labelType": "text",
		"text": "start",
		"x": 0,
		"y": 0,
		"width": 67.796875,
		"height": 44,
		"containerStyle": {},
		"labelStyle": {}
	},
	"stop": {
		"id": "stop",
		"labelType": "text",
		"text": "stop",
		"x": 117.796875,
		"y": 0,
		"width": 62.3828125,
		"height": 44,
		"containerStyle": {},
		"labelStyle": {}
	}
}
```


## Computing the edges

The lines and arrows are considered as `edges` in mermaid as shown in the above diagram.
We use `getEdges` API from `diagram.parse.yy` to get the edges for a given flowchart.
Considering the same example this is the response from the API

```js
[
	{
		"start": "start",
		"end": "stop",
		"type": "arrow_point",
		"text": "",
		"labelType": "text",
		"stroke": "normal",
		"length": 1
	}
]
```

Similarly here the dimensions and position is missing and we compute that from the svg. The [`parseEdge`](https://github.com/excalidraw/mermaid-to-excalidraw/blob/master/src/parseMermaid.ts#L245) takes the above response along with `svg` and computes the position, dimensions and cleans up the response.

 The final output from `parseEdge` looks like :point_down:

```js
[
	{
		"start": "start",
		"end": "stop",
		"type": "arrow_point",
		"text": "",
		"labelType": "text",
		"stroke": "normal",
		"startX": 67.797,
		"startY": 22,
		"endX": 117.797,
		"endY": 22,
		"reflectionPoints": [
			{
				"x": 67.797,
				"y": 22
			},
			{
				"x": 117.797,
				"y": 22
			}
		]
	}
]
```
## Computing the Subgraphs

`Subgraphs` is collection of elements grouped together. The Subgraphs map to `grouping` elements in Excalidraw.

Lets consider the below example :point_down:

![image](https://github.com/excalidraw/excalidraw/assets/11256141/5243ce4c-beaa-43d2-812a-0577b0a574d7)

We use `getSubgraphs` API to get the subgraph data for a given flowchart.
Considering the same example this is the response from the API

```js
[
	{
		"id": "one",
		"nodes": [
				"flowchart-a2-1399",
				"flowchart-a1-1400"
		],
		"title": "one",
		"classes": [],
		"labelType": "text"
	}
]
```

For position and dimensions we use the svg to compute. The [`parseSubgraph`](https://github.com/excalidraw/mermaid-to-excalidraw/blob/master/src/parseMermaid.ts#L139) takes the above response along with `svg` and computes the position, dimensions and cleans up the response.


```js
[
	{
		"id": "one",
		"nodes": [
				"flowchart-a2-1399",
				"flowchart-a1-1400"
		],
		"title": "one",
		"labelType": "text",
		"nodeIds": [
				"a2",
				"a1"
		],
		"x": 75.4921875,
		"y": 0,
		"width": 121.25,
		"height": 188,
		"text": "one"
	}
]
```