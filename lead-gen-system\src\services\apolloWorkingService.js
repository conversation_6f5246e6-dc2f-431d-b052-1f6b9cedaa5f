import axios from 'axios';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import { rateLimiter } from '../utils/rateLimiter.js';

class ApolloWorkingService {
  constructor() {
    this.baseUrl = 'https://api.apollo.io/v1';
    this.apiKey = config.apollo.apiKey;
    
    // Create axios instance with working authentication
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'x-api-key': this.apiKey
      }
    });
  }

  /**
   * Test API connection
   * @returns {Promise<Object>} Connection test result
   */
  async testConnection() {
    try {
      await rateLimiter.consume('apollo');
      
      const response = await this.client.get('/auth/health');
      
      return {
        success: true,
        message: 'Apollo API connection successful',
        health: response.data,
        rate_limit: response.headers['x-hourly-requests-left'] || 'Unknown'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Apollo API connection failed',
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Enrich organization data by domain
   * @param {string} domain - Organization domain
   * @returns {Promise<Object>} Organization enrichment result
   */
  async enrichOrganization(domain) {
    try {
      await rateLimiter.consume('apollo');
      
      const response = await this.client.get('/organizations/enrich', {
        params: { domain }
      });
      
      logger.info(`Organization enrichment successful for ${domain}`);
      
      return {
        success: true,
        domain: domain,
        organization: this.formatOrganization(response.data.organization),
        enriched_at: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Organization enrichment failed for ${domain}:`, error.response?.data || error.message);
      return {
        success: false,
        domain: domain,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Search for contacts in Apollo CRM
   * @param {Object} searchParams - Search parameters
   * @returns {Promise<Object>} Contact search results
   */
  async searchContacts(searchParams = {}) {
    try {
      await rateLimiter.consume('apollo');
      
      const defaultParams = {
        q_keywords: '',
        sort_by_field: 'contact_last_activity_date',
        sort_ascending: false,
        page: 1,
        per_page: 25
      };

      const params = { ...defaultParams, ...searchParams };
      
      logger.info(`Searching Apollo contacts with keywords: ${params.q_keywords}`);
      
      const response = await this.client.post('/contacts/search', params);
      
      const contacts = response.data.contacts || [];
      logger.info(`Found ${contacts.length} contacts in Apollo CRM`);
      
      return {
        success: true,
        contacts: contacts.map(contact => this.formatContact(contact)),
        pagination: response.data.pagination,
        search_params: params
      };
    } catch (error) {
      logger.error('Error searching Apollo contacts:', error.response?.data || error.message);
      return {
        success: false,
        contacts: [],
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Search for accounts in Apollo CRM
   * @param {Object} searchParams - Search parameters
   * @returns {Promise<Object>} Account search results
   */
  async searchAccounts(searchParams = {}) {
    try {
      await rateLimiter.consume('apollo');
      
      const defaultParams = {
        q_keywords: '',
        sort_by_field: 'account_last_activity_date',
        sort_ascending: false,
        page: 1,
        per_page: 25
      };

      const params = { ...defaultParams, ...searchParams };
      
      logger.info(`Searching Apollo accounts with keywords: ${params.q_keywords}`);
      
      const response = await this.client.post('/accounts/search', params);
      
      const accounts = response.data.accounts || [];
      logger.info(`Found ${accounts.length} accounts in Apollo CRM`);
      
      return {
        success: true,
        accounts: accounts.map(account => this.formatAccount(account)),
        pagination: response.data.pagination,
        search_params: params
      };
    } catch (error) {
      logger.error('Error searching Apollo accounts:', error.response?.data || error.message);
      return {
        success: false,
        accounts: [],
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Enrich school data with organization information
   * @param {Array} schools - Array of school objects with domains
   * @returns {Promise<Array>} Enriched school data
   */
  async enrichSchoolData(schools) {
    logger.info(`Starting organization enrichment for ${schools.length} schools`);
    
    const enrichedSchools = [];
    const batchSize = 5; // Process in small batches to respect rate limits
    
    for (let i = 0; i < schools.length; i += batchSize) {
      const batch = schools.slice(i, i + batchSize);
      logger.info(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(schools.length/batchSize)}`);
      
      const batchPromises = batch.map(async (school) => {
        try {
          const domain = this.extractDomainFromSchool(school);
          if (!domain) {
            return {
              ...school,
              apollo_enrichment: null,
              apollo_error: 'No domain found'
            };
          }

          const enrichment = await this.enrichOrganization(domain);
          return {
            ...school,
            apollo_enrichment: enrichment.success ? enrichment.organization : null,
            apollo_error: enrichment.success ? null : enrichment.error,
            apollo_enriched_at: new Date().toISOString()
          };
        } catch (error) {
          logger.error(`Error enriching school ${school.name}:`, error.message);
          return {
            ...school,
            apollo_enrichment: null,
            apollo_error: error.message,
            apollo_enriched_at: new Date().toISOString()
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      enrichedSchools.push(...batchResults);
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < schools.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    logger.info(`Organization enrichment completed. Processed ${enrichedSchools.length} schools`);
    return enrichedSchools;
  }

  /**
   * Generate email suggestions for schools based on common patterns
   * @param {Object} school - School object
   * @param {Array} contactTypes - Types of contacts to generate
   * @returns {Array} Email suggestions
   */
  generateEmailSuggestions(school, contactTypes = ['headteacher', 'admin', 'it']) {
    const domain = this.extractDomainFromSchool(school);
    if (!domain) return [];

    const suggestions = [];
    const emailPatterns = {
      headteacher: ['head', 'headteacher', 'principal', 'head.teacher'],
      admin: ['admin', 'office', 'reception', 'secretary', 'enquiries'],
      it: ['it', 'ict', 'computing', 'technology', 'digital'],
      business: ['business', 'finance', 'bursar', 'manager'],
      deputy: ['deputy', 'assistant', 'vice']
    };

    for (const contactType of contactTypes) {
      const patterns = emailPatterns[contactType] || [contactType];
      
      for (const pattern of patterns) {
        suggestions.push({
          email: `${pattern}@${domain}`,
          type: contactType,
          pattern: pattern,
          confidence: 'medium',
          school: school.name
        });
      }
    }

    return suggestions;
  }

  /**
   * Extract domain from school information
   * @param {Object} school - School object
   * @returns {string|null} Domain or null if not found
   */
  extractDomainFromSchool(school) {
    // Try to extract domain from various fields
    const possibleSources = [
      school.website,
      school.domain,
      school.url,
      school.email
    ];

    for (const source of possibleSources) {
      if (source && typeof source === 'string') {
        // Extract domain from URL
        const urlMatch = source.match(/https?:\/\/(?:www\.)?([^\/]+)/);
        if (urlMatch) {
          return urlMatch[1];
        }
        
        // Extract domain from email
        const emailMatch = source.match(/@([^@]+)$/);
        if (emailMatch) {
          return emailMatch[1];
        }
        
        // If it looks like a domain already
        if (source.includes('.') && !source.includes('/') && !source.includes('@')) {
          return source;
        }
      }
    }

    // Generate likely domain from school name
    if (school.name) {
      const cleanName = school.name
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '')
        .replace(/(school|academy|college|trust|primary|secondary|high)$/g, '');
      
      return `${cleanName}.sch.uk`; // Common UK school domain pattern
    }

    return null;
  }

  /**
   * Format organization data for consistency
   * @param {Object} org - Raw Apollo organization data
   * @returns {Object} Formatted organization
   */
  formatOrganization(org) {
    if (!org) return null;
    
    return {
      name: org.name,
      domain: org.primary_domain,
      website: org.website_url,
      industry: org.industry,
      size: org.estimated_num_employees,
      founded_year: org.founded_year,
      location: {
        city: org.city,
        state: org.state,
        country: org.country
      },
      phone: org.phone,
      description: org.short_description,
      apollo_id: org.id,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Format contact data for consistency
   * @param {Object} contact - Raw Apollo contact data
   * @returns {Object} Formatted contact
   */
  formatContact(contact) {
    return {
      id: contact.id,
      name: contact.name,
      first_name: contact.first_name,
      last_name: contact.last_name,
      title: contact.title,
      email: contact.email,
      phone: contact.sanitized_phone,
      organization: contact.account?.name,
      apollo_id: contact.id,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Format account data for consistency
   * @param {Object} account - Raw Apollo account data
   * @returns {Object} Formatted account
   */
  formatAccount(account) {
    return {
      id: account.id,
      name: account.name,
      domain: account.domain,
      industry: account.industry,
      size: account.estimated_num_employees,
      apollo_id: account.id,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Get available features for current API key
   * @returns {Object} Available features
   */
  getAvailableFeatures() {
    return {
      organization_enrichment: true,
      contacts_search: true,
      accounts_search: true,
      people_search: false,
      email_finder: false,
      email_verifier: false,
      plan_type: 'Limited Professional'
    };
  }
}

export const apolloWorkingService = new ApolloWorkingService();
