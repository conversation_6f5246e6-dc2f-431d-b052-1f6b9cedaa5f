import axios from 'axios';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import { rateLimiter } from '../utils/rateLimiter.js';

class CompaniesHouseService {
  constructor() {
    this.baseUrl = config.companiesHouse.baseUrl;
    this.apiKey = config.companiesHouse.apiKey;
    
    // Create axios instance with authentication
    this.client = axios.create({
      baseURL: this.baseUrl,
      auth: {
        username: this.api<PERSON><PERSON>,
        password: ''
      },
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'UK-Schools-Lead-Gen/1.0'
      }
    });
  }

  /**
   * Search for Multi Academy Trusts by name or keywords
   * @param {string} query - Search query
   * @param {number} itemsPerPage - Number of results per page (max 100)
   * @param {number} startIndex - Starting index for pagination
   * @returns {Promise<Object>} Search results
   */
  async searchMATs(query = 'academy trust', itemsPerPage = 100, startIndex = 0) {
    try {
      await rateLimiter.consume('companies-house');
      
      const response = await this.client.get('/search/companies', {
        params: {
          q: query,
          items_per_page: itemsPerPage,
          start_index: startIndex
        }
      });

      logger.info(`Found ${response.data.total_results} companies matching "${query}"`);
      
      // Filter for companies that are likely MATs
      const mats = response.data.items.filter(company => 
        this.isLikelyMAT(company)
      );

      return {
        total_results: response.data.total_results,
        items_per_page: response.data.items_per_page,
        start_index: response.data.start_index,
        mats: mats,
        raw_items: response.data.items
      };
    } catch (error) {
      logger.error('Error searching for MATs:', error.message);
      throw error;
    }
  }

  /**
   * Get detailed company information
   * @param {string} companyNumber - Company registration number
   * @returns {Promise<Object>} Company details
   */
  async getCompanyDetails(companyNumber) {
    try {
      await rateLimiter.consume('companies-house');
      
      const response = await this.client.get(`/company/${companyNumber}`);
      
      logger.info(`Retrieved details for company ${companyNumber}`);
      return response.data;
    } catch (error) {
      logger.error(`Error getting company details for ${companyNumber}:`, error.message);
      throw error;
    }
  }

  /**
   * Get company officers (directors, secretaries, etc.)
   * @param {string} companyNumber - Company registration number
   * @returns {Promise<Object>} Officers list
   */
  async getCompanyOfficers(companyNumber) {
    try {
      await rateLimiter.consume('companies-house');
      
      const response = await this.client.get(`/company/${companyNumber}/officers`);
      
      logger.info(`Retrieved ${response.data.total_results} officers for company ${companyNumber}`);
      return response.data;
    } catch (error) {
      logger.error(`Error getting officers for ${companyNumber}:`, error.message);
      throw error;
    }
  }

  /**
   * Get company filing history
   * @param {string} companyNumber - Company registration number
   * @param {number} itemsPerPage - Number of results per page
   * @returns {Promise<Object>} Filing history
   */
  async getFilingHistory(companyNumber, itemsPerPage = 25) {
    try {
      await rateLimiter.consume('companies-house');
      
      const response = await this.client.get(`/company/${companyNumber}/filing-history`, {
        params: {
          items_per_page: itemsPerPage
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Error getting filing history for ${companyNumber}:`, error.message);
      throw error;
    }
  }

  /**
   * Check if a company is likely a Multi Academy Trust
   * @param {Object} company - Company object from search results
   * @returns {boolean} True if likely a MAT
   */
  isLikelyMAT(company) {
    const name = company.title.toLowerCase();
    const matKeywords = [
      'academy trust',
      'multi academy trust',
      'mat',
      'academies trust',
      'education trust',
      'schools trust'
    ];

    return matKeywords.some(keyword => name.includes(keyword)) ||
           (company.company_type === 'charitable-incorporated-organisation' && 
            name.includes('academy'));
  }

  /**
   * Extract key decision makers from officers list
   * @param {Object} officers - Officers data from Companies House
   * @returns {Array} List of key decision makers
   */
  extractDecisionMakers(officers) {
    const keyRoles = [
      'director',
      'chief executive officer',
      'ceo',
      'managing director',
      'executive director',
      'chair',
      'chairman',
      'chairwoman',
      'secretary'
    ];

    return officers.items
      .filter(officer => officer.resigned_on === undefined) // Active officers only
      .filter(officer => {
        const role = officer.officer_role.toLowerCase();
        return keyRoles.some(keyRole => role.includes(keyRole));
      })
      .map(officer => ({
        name: officer.name,
        role: officer.officer_role,
        appointed_on: officer.appointed_on,
        address: officer.address,
        date_of_birth: officer.date_of_birth
      }));
  }

  /**
   * Get comprehensive MAT data including officers and filing history
   * @param {string} companyNumber - Company registration number
   * @returns {Promise<Object>} Complete MAT profile
   */
  async getComprehensiveMatData(companyNumber) {
    try {
      const [company, officers, filings] = await Promise.all([
        this.getCompanyDetails(companyNumber),
        this.getCompanyOfficers(companyNumber),
        this.getFilingHistory(companyNumber, 10)
      ]);

      const decisionMakers = this.extractDecisionMakers(officers);

      return {
        company,
        officers: officers.items,
        decisionMakers,
        recentFilings: filings.items,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Error getting comprehensive MAT data for ${companyNumber}:`, error.message);
      throw error;
    }
  }
}

export const companiesHouseService = new CompaniesHouseService();
