// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`exportToSvg > with default arguments 1`] = `
{
  "activeEmbeddable": null,
  "activeTool": {
    "customType": null,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "currentChartType": "bar",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 1,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "round",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "draggingElement": null,
  "editingElement": null,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportPadding": undefined,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridSize": null,
  "isBindingEnabled": true,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "multiElement": null,
  "name": "name",
  "objectsSnapModeEnabled": false,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "pendingImageElementId": null,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showStats": false,
  "showWelcomeScreen": false,
  "snapLines": [],
  "startBoundElement": null,
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;
