﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excalidraw + Google Slides Integration Demo</title>
    <style>
        body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .app-container { height: 100vh; display: flex; flex-direction: column; }
        .toolbar { background: white; border-bottom: 1px solid #e0e0e0; padding: 12px 20px; display: flex; align-items: center; gap: 16px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); z-index: 100; }
        .toolbar h1 { margin: 0; font-size: 20px; color: #333; font-weight: 600; }
        .toolbar-buttons { display: flex; gap: 12px; margin-left: auto; }
        .btn { background: #4285f4; color: white; border: none; border-radius: 6px; padding: 8px 16px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.2s ease; display: flex; align-items: center; gap: 6px; }
        .btn:hover { background: #3367d6; transform: translateY(-1px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); }
        .btn.secondary { background: #34a853; }
        .btn.secondary:hover { background: #2d8f47; }
        .main-content { flex: 1; display: flex; position: relative; }
        .excalidraw-panel { flex: 1; background: white; border-right: 1px solid #e0e0e0; display: flex; align-items: center; justify-content: center; color: #666; font-size: 18px; flex-direction: column; text-align: center; padding: 40px; }
        .excalidraw-panel h2 { color: #333; margin-bottom: 16px; }
        .excalidraw-panel p { margin: 8px 0; line-height: 1.6; }
        .slides-panel { width: 400px; background: #f8f9fa; border-left: 1px solid #e0e0e0; display: flex; flex-direction: column; transition: width 0.3s ease; }
        .slides-panel.collapsed { width: 0; overflow: hidden; }
        .slides-header { padding: 16px; border-bottom: 1px solid #e0e0e0; background: white; display: flex; justify-content: space-between; align-items: center; }
        .slides-content { flex: 1; padding: 16px; }
        .url-input { width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; margin-bottom: 12px; box-sizing: border-box; }
        .url-input:focus { outline: none; border-color: #4285f4; box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1); }
        .slide-controls { display: flex; gap: 8px; margin-bottom: 12px; flex-wrap: wrap; }
        .slide-controls .btn { font-size: 12px; padding: 6px 12px; }
        .slide-info { display: flex; align-items: center; gap: 8px; font-size: 12px; color: #666; margin-bottom: 12px; }
        .slide-input { width: 50px; padding: 4px 6px; border: 1px solid #ddd; border-radius: 4px; text-align: center; font-size: 12px; }
        .iframe-container { width: 100%; height: 300px; border: 2px solid #e0e0e0; border-radius: 8px; overflow: hidden; background: white; }
        .slides-iframe { width: 100%; height: 100%; border: none; }
        .instructions { background: #e8f0fe; padding: 12px; border-radius: 6px; font-size: 12px; color: #1a73e8; line-height: 1.4; }
        .feature-list { background: #f0f8ff; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .feature-list h3 { color: #1a73e8; margin-top: 0; }
        .feature-list ul { margin: 0; padding-left: 20px; }
        .feature-list li { margin-bottom: 8px; color: #333; }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="toolbar">
            <h1> Excalidraw + Google Slides Integration</h1>
            <div class="toolbar-buttons">
                <button class="btn" onclick="toggleSlidesPanel()"> Toggle Slides Panel</button>
                <button class="btn secondary" onclick="openFullDemo()"> Full Demo</button>
            </div>
        </div>
        
        <div class="main-content">
            <div class="excalidraw-panel">
                <div>
                    <h2> Excalidraw Canvas Area</h2>
                    <p>This is where the Excalidraw whiteboard would be integrated</p>
                    <p>Users can draw, annotate, and collaborate while viewing slides</p>
                    
                    <div class="feature-list">
                        <h3> Integration Features:</h3>
                        <ul>
                            <li> Side-by-side Google Slides viewing</li>
                            <li> Keyboard navigation (arrow keys)</li>
                            <li> Present in new tab (no iframe restrictions)</li>
                            <li> Real-time slide synchronization</li>
                            <li> Responsive design for mobile/desktop</li>
                            <li> Direct link to original presentation</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="slides-panel" id="slidesPanel">
                <div class="slides-header">
                    <h3> Google Slides</h3>
                    <button class="btn" onclick="toggleSlidesPanel()" style="padding: 4px 8px; font-size: 12px;"></button>
                </div>
                <div class="slides-content">
                    <div class="instructions">
                         <strong>How to use:</strong><br>
                        1. Open your Google Slides presentation<br>
                        2. Click "Share"  "Anyone with the link can view"<br>
                        3. Copy the URL and paste it below<br>
                        4. Use "Present in New Tab" for full presentation mode
                    </div>
                    
                    <input type="url" id="urlInput" class="url-input" placeholder="Paste Google Slides URL here..." />
                    
                    <div class="slide-controls">
                        <button class="btn" onclick="loadPresentation()">Load</button>
                        <button class="btn" onclick="navigateSlide(-1)" id="prevBtn" disabled></button>
                        <button class="btn" onclick="navigateSlide(1)" id="nextBtn" disabled></button>
                        <button class="btn secondary" onclick="openPresentationInNewTab()" id="presentBtn" disabled>Present in New Tab</button>
                    </div>
                    
                    <div class="slide-info">
                        <span>Slide</span>
                        <input type="number" id="slideInput" class="slide-input" value="1" min="1" onchange="goToSlide()">
                        <span>of <span id="totalSlides">1</span></span>
                    </div>
                    
                    <div class="iframe-container">
                        <iframe id="slidesFrame" class="slides-iframe" src="about:blank"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        var slidesPanelVisible = true;
        var currentSlide = 1;
        var totalSlides = 20;
        var presentationId = '';
        
        function extractPresentationId(url) {
            var patterns = [
                /\/presentation\/d\/([a-zA-Z0-9-_]+)/,
                /\/presentation\/d\/([a-zA-Z0-9-_]+)\/edit/,
                /\/presentation\/d\/([a-zA-Z0-9-_]+)\/view/
            ];
            
            for (var i = 0; i < patterns.length; i++) {
                var match = url.match(patterns[i]);
                if (match) return match[1];
            }
            return null;
        }
        
        function generateEmbedUrl(id, slide) {
            slide = slide || 1;
            // ALWAYS use embed URL - no present mode URL switching
            return 'https://docs.google.com/presentation/d/' + id + '/embed?start=false&loop=false&delayms=3000&slide=' + slide + '&rm=minimal';
        }
        
        function toggleSlidesPanel() {
            var panel = document.getElementById('slidesPanel');
            slidesPanelVisible = !slidesPanelVisible;
            if (slidesPanelVisible) {
                panel.classList.remove('collapsed');
            } else {
                panel.classList.add('collapsed');
            }
        }
        
        function loadPresentation() {
            var url = document.getElementById('urlInput').value.trim();
            var id = extractPresentationId(url);
            
            if (!id) {
                alert('Invalid Google Slides URL. Please make sure the presentation is shared publicly.');
                return;
            }
            
            presentationId = id;
            currentSlide = 1;
            totalSlides = 20;
            updateControls();
            updateSlideDisplay();
            
            var iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(id, currentSlide);
        }
        
        function navigateSlide(direction) {
            if (!presentationId) return;
            
            var newSlide = currentSlide + direction;
            if (newSlide < 1 || newSlide > totalSlides) return;
            
            currentSlide = newSlide;
            updateSlideDisplay();
            updateControls();
            
            var iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }
        
        function goToSlide() {
            if (!presentationId) return;
            
            var slideInput = document.getElementById('slideInput');
            var slide = parseInt(slideInput.value) || 1;
            
            if (slide < 1 || slide > totalSlides) {
                slideInput.value = currentSlide;
                return;
            }
            
            currentSlide = slide;
            updateControls();
            updateSlideDisplay();
            
            var iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }
        
        function openPresentationInNewTab() {
            if (!presentationId) return;
            
            // Open Google Slides in new tab - NO iframe restrictions
            var presentUrl = 'https://docs.google.com/presentation/d/' + presentationId + '/present?start=false&loop=false&delayms=3000&slide=' + currentSlide;
            window.open(presentUrl, '_blank');
        }
        
        function updateSlideDisplay() {
            document.getElementById('slideInput').value = currentSlide;
            document.getElementById('totalSlides').textContent = totalSlides;
        }
        
        function updateControls() {
            document.getElementById('prevBtn').disabled = currentSlide <= 1 || !presentationId;
            document.getElementById('nextBtn').disabled = currentSlide >= totalSlides || !presentationId;
            document.getElementById('presentBtn').disabled = !presentationId;
        }
        
        function openFullDemo() {
            window.open('google-slides-demo.html', '_blank');
        }
        
        window.onload = function() {
            updateControls();
            updateSlideDisplay();
        };
        
        document.addEventListener('keydown', function(e) {
            if (e.target.tagName === 'INPUT') return;
            
            if (e.key === 'ArrowLeft' && presentationId) {
                e.preventDefault();
                navigateSlide(-1);
            } else if (e.key === 'ArrowRight' && presentationId) {
                e.preventDefault();
                navigateSlide(1);
            }
        });
    </script>
</body>
</html>
