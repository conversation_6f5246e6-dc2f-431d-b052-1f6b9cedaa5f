{"name": "with-script-in-browser", "version": "1.0.0", "private": true, "dependencies": {"react": "19.0.0", "react-dom": "19.0.0", "@excalidraw/excalidraw": "*", "browser-fs-access": "0.29.1"}, "devDependencies": {"vite": "5.0.12", "typescript": "^5"}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview --port 5002", "build:preview": "yarn build && yarn preview", "build:package": "yarn workspace @excalidraw/excalidraw run build:esm"}}