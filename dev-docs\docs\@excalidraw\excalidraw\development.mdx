---
pagination_prev: "@excalidraw/excalidraw/installation"
---

# Development

This page relates to developing the `@excalidraw/excalidraw` package itself.

## Example app

To start the example app using the `@excalidraw/excalidraw` package, follow the below steps:

1. Install the dependencies

   ```bash
   yarn
   ```

2. Start the example app

   ```bash
   yarn start:example
   ```

   [http://localhost:3001](http://localhost:3001) will open in your default browser.
   
   This is the same example as the [CodeSandbox](https://codesandbox.io/p/sandbox/github/excalidraw/excalidraw/tree/master/examples/with-script-in-browser) example.

## Releasing

### Create a test release

You can create a test release by posting the below comment in your pull request:

```bash
@excalibot trigger release
```

Once the version is released `@excalibot` will post a comment with the release version.

### Creating a production release

To release the next stable version follow the below steps:

```bash
yarn prerelease:excalidraw
```

You need to pass the `version` for which you want to create the release. This will make the changes needed before making the release like updating `package.json`, `changelog` and more.

The next step is to run the `release` script:

```bash
yarn release:excalidraw
```

This will publish the package.

Right now there are two steps to create a production release but once this works fine these scripts will be combined and more automation will be done.
