name: Build locales coverage

on:
  push:
    branches:
      - l10n_master

jobs:
  locales:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.PUSH_TRANSLATIONS_COVERAGE_PAT }}

      - name: Setup Node.js 18.x
        uses: actions/setup-node@v2
        with:
          node-version: 18.x

      - name: Create report file
        run: |
          yarn locales-coverage
          FILE_CHANGED=$(git diff packages/excalidraw/locales/percentages.json)
          if [ ! -z "${FILE_CHANGED}" ]; then
            git config --global user.name 'Excalidraw Bot'
            git config --global user.email '<EMAIL>'
            git add packages/excalidraw/locales/percentages.json
            git commit -am "Auto commit: Calculate translation coverage"
            git push
          fi
      - name: Construct comment body
        id: getCommentBody
        run: |
          body=$(npm run locales-coverage:description | grep '^[^>]')
          body="${body//'%'/'%25'}"
          body="${body//$'\n'/'%0A'}"
          body="${body//$'\r'/'%0D'}"
          echo ::set-output name=body::$body

      - name: Update description with coverage
        uses: kt3k/update-pr-description@v1.0.1
        with:
          pr_body: ${{ steps.getCommentBody.outputs.body }}
          pr_title: "chore: Update translations from Crowdin"
          github_token: ${{ secrets.PUSH_TRANSLATIONS_COVERAGE_PAT }}
