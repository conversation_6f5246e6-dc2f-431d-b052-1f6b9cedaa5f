import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

export const config = {
  // Server Configuration
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // UK Government APIs
  companiesHouse: {
    apiKey: process.env.COMPANIES_HOUSE_API_KEY,
    baseUrl: 'https://api.company-information.service.gov.uk'
  },
  
  // Contact Enrichment APIs
  apollo: {
    apiKey: process.env.APOLLO_API_KEY,
    baseUrl: 'https://api.apollo.io/v1'
  },
  
  hunter: {
    apiKey: process.env.HUNTER_API_KEY,
    baseUrl: 'https://api.hunter.io/v2'
  },
  
  neverBounce: {
    apiKey: process.env.NEVERBOUNCE_API_KEY,
    baseUrl: 'https://api.neverbounce.com/v4'
  },
  
  // CRM Integration
  hubspot: {
    apiKey: process.env.HUBSPOT_API_KEY,
    portalId: process.env.HUBSPOT_PORTAL_ID,
    baseUrl: 'https://api.hubapi.com'
  },
  
  pipedrive: {
    apiToken: process.env.PIPEDRIVE_API_TOKEN,
    companyDomain: process.env.PIPEDRIVE_COMPANY_DOMAIN
  },
  
  // Email Infrastructure
  sendgrid: {
    apiKey: process.env.SENDGRID_API_KEY,
    fromEmail: process.env.SENDGRID_FROM_EMAIL,
    fromName: process.env.SENDGRID_FROM_NAME
  },
  
  mailgun: {
    apiKey: process.env.MAILGUN_API_KEY,
    domain: process.env.MAILGUN_DOMAIN
  },
  
  // AI Services
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    model: 'gpt-4',
    maxTokens: 1000
  },
  
  // Database
  database: {
    url: process.env.DATABASE_URL || './data/leads.db'
  },
  
  // Rate Limiting
  rateLimiting: {
    requests: parseInt(process.env.API_RATE_LIMIT_REQUESTS) || 100,
    windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW_MS) || 900000
  },
  
  // Email Campaign Settings
  email: {
    batchSize: parseInt(process.env.EMAIL_BATCH_SIZE) || 50,
    delayBetweenBatches: parseInt(process.env.EMAIL_DELAY_BETWEEN_BATCHES_MS) || 60000
  },
  
  // Data Collection Settings
  dataCollection: {
    batchSize: parseInt(process.env.DATA_COLLECTION_BATCH_SIZE) || 100,
    maxRetries: parseInt(process.env.MAX_RETRIES) || 3
  },
  
  // Compliance & Security
  compliance: {
    gdprConsentRequired: process.env.GDPR_CONSENT_REQUIRED === 'true',
    dataRetentionDays: parseInt(process.env.DATA_RETENTION_DAYS) || 730
  },
  
  security: {
    jwtSecret: process.env.JWT_SECRET,
    encryptionKey: process.env.ENCRYPTION_KEY
  },
  
  // Development & Testing
  development: {
    testMode: process.env.TEST_MODE === 'true',
    debugLevel: process.env.DEBUG_LEVEL || 'info',
    mockApis: process.env.MOCK_APIS === 'true'
  },
  
  // Monitoring
  monitoring: {
    gaTrackingId: process.env.GA_TRACKING_ID,
    slackWebhook: process.env.SLACK_WEBHOOK_URL,
    discordWebhook: process.env.DISCORD_WEBHOOK_URL
  }
};

// Validation function to check required environment variables
export function validateConfig() {
  const requiredVars = [
    'COMPANIES_HOUSE_API_KEY',
    'APOLLO_API_KEY',
    'HUNTER_API_KEY',
    'HUBSPOT_API_KEY',
    'SENDGRID_API_KEY',
    'OPENAI_API_KEY'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(`   - ${varName}`));
    console.error('\n📋 Please check LEAD_GEN_SETUP.md for setup instructions');
    return false;
  }
  
  console.log('✅ All required environment variables are configured');
  return true;
}

export default config;
