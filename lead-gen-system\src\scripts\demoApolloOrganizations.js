#!/usr/bin/env node

import { apolloLimitedService } from '../services/apolloLimitedService.js';
import fs from 'fs/promises';

async function demoOrganizationSearch() {
  console.log('🏫 Demonstrating Apollo Organization Search for UK Schools...\n');

  // Test searches for well-known UK MATs and schools
  const searchTerms = [
    'Harris Federation',
    'Ark Schools', 
    'United Learning',
    'Oasis Community Learning',
    'Star Academies',
    'Academy Trust',
    'Primary School London',
    'Secondary School Manchester'
  ];

  const allResults = [];

  for (const term of searchTerms) {
    try {
      console.log(`🔍 Searching for: "${term}"`);
      
      const results = await apolloLimitedService.searchOrganizations(term);
      
      console.log(`   ✅ Found ${results.organizations.length} organizations`);
      
      if (results.organizations.length > 0) {
        const sample = results.organizations[0];
        console.log(`   📋 Sample: ${sample.name}`);
        console.log(`   🌐 Domain: ${sample.domain || 'N/A'}`);
        console.log(`   📍 Location: ${sample.location.city || 'N/A'}, ${sample.location.country || 'N/A'}`);
        console.log(`   👥 Size: ${sample.size || 'N/A'} employees`);
        
        // Generate email suggestions for this organization
        if (sample.domain) {
          console.log(`   📧 Suggested emails:`);
          const emailSuggestions = generateEmailSuggestions(sample);
          emailSuggestions.slice(0, 3).forEach(email => {
            console.log(`      • ${email}`);
          });
        }
      }
      
      allResults.push({
        search_term: term,
        results: results.organizations,
        found_count: results.total_found
      });
      
      console.log('');
      
      // Add delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Error searching for "${term}":`, error.message);
    }
  }

  // Save all results
  await fs.writeFile('./data/apollo_organization_demo.json', JSON.stringify(allResults, null, 2));
  console.log('💾 Results saved to ./data/apollo_organization_demo.json');

  // Generate summary
  const totalOrganizations = allResults.reduce((sum, result) => sum + result.results.length, 0);
  const organizationsWithDomains = allResults.reduce((sum, result) => 
    sum + result.results.filter(org => org.domain).length, 0);

  console.log('\n📊 SUMMARY:');
  console.log(`   Total searches: ${searchTerms.length}`);
  console.log(`   Total organizations found: ${totalOrganizations}`);
  console.log(`   Organizations with domains: ${organizationsWithDomains}`);
  console.log(`   Success rate: ${Math.round((organizationsWithDomains / totalOrganizations) * 100)}%`);

  return allResults;
}

function generateEmailSuggestions(organization) {
  const domain = organization.domain;
  if (!domain) return [];

  const suggestions = [
    `head@${domain}`,
    `headteacher@${domain}`,
    `principal@${domain}`,
    `admin@${domain}`,
    `office@${domain}`,
    `reception@${domain}`,
    `it@${domain}`,
    `ict@${domain}`,
    `info@${domain}`,
    `enquiries@${domain}`,
    `admissions@${domain}`,
    `business@${domain}`,
    `finance@${domain}`
  ];

  return suggestions;
}

// Run the demo
demoOrganizationSearch().catch(error => {
  console.error('Demo failed:', error);
  process.exit(1);
});
