#!/usr/bin/env node

import { apolloLimitedService } from '../services/apolloLimitedService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';

async function testApolloLimited() {
  console.log('🧪 Testing Apollo.io Limited API Access...\n');

  try {
    // Test 1: Connection and feature detection
    console.log('1. Testing connection and detecting available features...');
    const connectionTest = await apolloLimitedService.testConnection();
    
    if (connectionTest.success) {
      console.log('✅ Connection successful!');
      console.log(`   Plan Type: ${connectionTest.plan_type}`);
      console.log('   Available Features:');
      
      Object.entries(connectionTest.available_features).forEach(([feature, available]) => {
        const status = available === true ? '✅' : available === false ? '❌' : '⚠️';
        console.log(`     ${status} ${feature}`);
      });
    } else {
      console.log('❌ Connection failed:', connectionTest.error);
      return;
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Email finder (if available)
    if (connectionTest.available_features.email_finder) {
      console.log('2. Testing Email Finder...');
      
      const testDomains = ['example.com', 'google.com'];
      
      for (const domain of testDomains) {
        console.log(`   Testing domain: ${domain}`);
        const emailResult = await apolloLimitedService.findEmail(domain, 'john', 'doe');
        
        if (emailResult.success) {
          console.log(`   ✅ Email found: ${emailResult.email || 'None'}`);
          console.log(`   Confidence: ${emailResult.confidence || 'Unknown'}`);
        } else {
          console.log(`   ❌ Email finder failed: ${emailResult.error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } else {
      console.log('2. Email Finder not available with current plan');
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Email verification (if available)
    if (connectionTest.available_features.email_verifier) {
      console.log('3. Testing Email Verifier...');
      
      const testEmails = ['<EMAIL>', '<EMAIL>'];
      
      for (const email of testEmails) {
        console.log(`   Verifying: ${email}`);
        const verifyResult = await apolloLimitedService.verifyEmail(email);
        
        if (verifyResult.success) {
          console.log(`   ✅ Valid: ${verifyResult.is_valid}`);
          console.log(`   Deliverable: ${verifyResult.is_deliverable}`);
          console.log(`   Risky: ${verifyResult.is_risky}`);
        } else {
          console.log(`   ❌ Verification failed: ${verifyResult.error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } else {
      console.log('3. Email Verifier not available with current plan');
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: School email suggestions
    console.log('4. Testing School Email Suggestions...');
    
    const testSchool = {
      name: 'Example Primary School',
      website: 'https://www.exampleschool.sch.uk'
    };
    
    console.log(`   Generating suggestions for: ${testSchool.name}`);
    const suggestions = await apolloLimitedService.generateSchoolEmailSuggestions(testSchool);
    
    console.log(`   ✅ Generated ${suggestions.length} email suggestions:`);
    suggestions.forEach(suggestion => {
      console.log(`     ${suggestion.email} (${suggestion.type}) - Verified: ${suggestion.verified}`);
    });

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 5: Usage statistics
    console.log('5. Getting Usage Statistics...');
    const stats = await apolloLimitedService.getUsageStats();
    
    console.log(`   Plan Type: ${stats.plan_type}`);
    console.log('   Recommendations:');
    stats.recommendations.forEach(rec => {
      console.log(`     • ${rec}`);
    });

    // Save test results
    const testResults = {
      test_date: new Date().toISOString(),
      connection_test: connectionTest,
      usage_stats: stats,
      test_status: 'completed'
    };

    await fs.writeFile('./data/apollo_limited_test_results.json', JSON.stringify(testResults, null, 2));
    console.log('\n💾 Test results saved to ./data/apollo_limited_test_results.json');

    console.log('\n✅ Apollo Limited API tests completed successfully!');

  } catch (error) {
    console.error('❌ Apollo tests failed:', error);
    throw error;
  }
}

// Run the test
testApolloLimited().catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
