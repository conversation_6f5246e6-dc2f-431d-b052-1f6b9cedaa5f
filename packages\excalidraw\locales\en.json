{"labels": {"paste": "Paste", "pasteAsPlaintext": "Paste as plaintext", "pasteCharts": "Paste charts", "selectAll": "Select all", "multiSelect": "Add element to selection", "moveCanvas": "Move canvas", "cut": "Cut", "copy": "Copy", "copyAsPng": "Copy to clipboard as PNG", "copyAsSvg": "Copy to clipboard as SVG", "copyText": "Copy to clipboard as text", "copySource": "Copy source to clipboard", "convertToCode": "Convert to code", "bringForward": "Bring forward", "sendToBack": "Send to back", "bringToFront": "Bring to front", "sendBackward": "Send backward", "delete": "Delete", "copyStyles": "Copy styles", "pasteStyles": "Paste styles", "stroke": "Stroke", "changeStroke": "Change stroke color", "background": "Background", "changeBackground": "Change background color", "fill": "Fill", "strokeWidth": "Stroke width", "strokeStyle": "Stroke style", "strokeStyle_solid": "Solid", "strokeStyle_dashed": "Dashed", "strokeStyle_dotted": "Dotted", "sloppiness": "<PERSON><PERSON><PERSON><PERSON>", "opacity": "Opacity", "textAlign": "Text align", "edges": "<PERSON>s", "sharp": "<PERSON>", "round": "Round", "arrowheads": "Arrowheads", "arrowhead_none": "None", "arrowhead_arrow": "Arrow", "arrowhead_bar": "Bar", "arrowhead_circle": "Circle", "arrowhead_circle_outline": "Circle (outline)", "arrowhead_triangle": "Triangle", "arrowhead_triangle_outline": "Triangle (outline)", "arrowhead_diamond": "Diamond", "arrowhead_diamond_outline": "Diamond (outline)", "arrowhead_crowfoot_many": "Crow's foot (many)", "arrowhead_crowfoot_one": "<PERSON>'s foot (one)", "arrowhead_crowfoot_one_or_many": "Crow's foot (one or many)", "more_options": "More options", "arrowtypes": "Arrow type", "arrowtype_sharp": "Sharp arrow", "arrowtype_round": "Curved arrow", "arrowtype_elbowed": "Elbow arrow", "fontSize": "Font size", "fontFamily": "<PERSON>ont family", "addWatermark": "Add \"Made with Excalidraw\"", "handDrawn": "Hand-drawn", "normal": "Normal", "code": "Code", "small": "Small", "medium": "Medium", "large": "Large", "veryLarge": "Very large", "solid": "Solid", "hachure": "<PERSON><PERSON><PERSON>", "zigzag": "Zigzag", "crossHatch": "Cross-hatch", "thin": "Thin", "bold": "Bold", "left": "Left", "center": "Center", "right": "Right", "extraBold": "Extra bold", "architect": "Architect", "artist": "Artist", "cartoonist": "Cartoonist", "fileTitle": "File name", "colorPicker": "Color picker", "canvasColors": "Used on canvas", "canvasBackground": "Canvas background", "drawingCanvas": "Drawing canvas", "clearCanvas": "Clear canvas", "layers": "Layers", "actions": "Actions", "language": "Language", "liveCollaboration": "Live collaboration...", "duplicateSelection": "Duplicate", "untitled": "Untitled", "name": "Name", "yourName": "Your name", "madeWithExcalidraw": "Made with Excalidraw", "group": "Group selection", "ungroup": "Ungroup selection", "collaborators": "Collaborators", "toggleGrid": "Toggle grid", "addToLibrary": "Add to library", "removeFromLibrary": "Remove from library", "libraryLoadingMessage": "Loading library…", "libraries": "Browse libraries", "loadingScene": "Loading scene…", "loadScene": "Load scene from file", "align": "Align", "alignTop": "Align top", "alignBottom": "Align bottom", "alignLeft": "<PERSON><PERSON> left", "alignRight": "Align right", "centerVertically": "Center vertically", "centerHorizontally": "Center horizontally", "distributeHorizontally": "Distribute horizontally", "distributeVertically": "Distribute vertically", "flipHorizontal": "Flip horizontal", "flipVertical": "Flip vertical", "viewMode": "View mode", "share": "Share", "showStroke": "Show stroke color picker", "showBackground": "Show background color picker", "showFonts": "Show font picker", "toggleTheme": "Toggle light/dark theme", "theme": "Theme", "personalLib": "Personal Library", "excalidrawLib": "Excalidraw Library", "decreaseFontSize": "Decrease font size", "increaseFontSize": "Increase font size", "unbindText": "Unbind text", "bindText": "Bind text to the container", "createContainerFromText": "Wrap text in a container", "link": {"edit": "Edit link", "editEmbed": "Edit embeddable link", "create": "Add link", "label": "Link", "labelEmbed": "Link & embed", "empty": "No link is set", "hint": "Type or paste your link here", "goToElement": "Go to target element"}, "lineEditor": {"edit": "Edit line", "editArrow": "Edit arrow"}, "polygon": {"breakPolygon": "Break polygon", "convertToPolygon": "Convert to polygon"}, "elementLock": {"lock": "Lock", "unlock": "Unlock", "lockAll": "Lock all", "unlockAll": "Unlock all"}, "statusPublished": "Published", "sidebarLock": "Keep sidebar open", "selectAllElementsInFrame": "Select all elements in frame", "removeAllElementsFromFrame": "Remove all elements from frame", "eyeDropper": "Pick color from canvas", "textToDiagram": "Text to diagram", "prompt": "Prompt", "followUs": "Follow us", "discordChat": "Discord chat", "zoomToFitViewport": "Zoom to fit in viewport", "zoomToFitSelection": "Zoom to fit selection", "zoomToFit": "Zoom to fit all elements", "installPWA": "Install Excalidraw locally (PWA)", "autoResize": "Enable text auto-resizing", "imageCropping": "Image cropping", "unCroppedDimension": "Uncropped dimension", "copyElementLink": "Copy link to object", "linkToElement": "Link to object", "wrapSelectionInFrame": "Wrap selection in frame", "tab": "Tab", "shapeSwitch": "Switch shape"}, "elementLink": {"title": "Link to object", "desc": "Click on a shape on canvas or paste a link.", "notFound": "Linked object wasn't found on canvas."}, "library": {"noItems": "No items added yet...", "hint_emptyLibrary": "Select an item on canvas to add it here, or install a library from the public repository, below.", "hint_emptyPrivateLibrary": "Select an item on canvas to add it here."}, "search": {"title": "Find on canvas", "noMatch": "No matches found...", "singleResult": "result", "multipleResults": "results", "placeholder": "Find text on canvas...", "frames": "Frames", "texts": "Texts"}, "buttons": {"clearReset": "Reset the canvas", "exportJSON": "Export to file", "exportImage": "Export image...", "export": "Save to...", "copyToClipboard": "Copy to clipboard", "copyLink": "Copy link", "save": "Save to current file", "saveAs": "Save as", "load": "Open", "getShareableLink": "Get shareable link", "close": "Close", "selectLanguage": "Select language", "scrollBackToContent": "Scroll back to content", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "resetZoom": "Reset zoom", "menu": "<PERSON><PERSON>", "done": "Done", "edit": "Edit", "undo": "Undo", "redo": "Redo", "resetLibrary": "Reset library", "createNewRoom": "Create new room", "fullScreen": "Full screen", "darkMode": "Dark mode", "lightMode": "Light mode", "systemMode": "System mode", "zenMode": "Zen mode", "objectsSnapMode": "Snap to objects", "exitZenMode": "Exit zen mode", "cancel": "Cancel", "clear": "Clear", "remove": "Remove", "embed": "Toggle embedding", "publishLibrary": "Publish", "submit": "Submit", "confirm": "Confirm", "embeddableInteractionButton": "Click to interact"}, "alerts": {"clearReset": "This will clear the whole canvas. Are you sure?", "couldNotCreateShareableLink": "Couldn't create shareable link.", "couldNotCreateShareableLinkTooBig": "Couldn't create shareable link: the scene is too big", "couldNotLoadInvalidFile": "Couldn't load invalid file", "importBackendFailed": "Importing from backend failed.", "cannotExportEmptyCanvas": "Cannot export empty canvas.", "couldNotCopyToClipboard": "Couldn't copy to clipboard.", "decryptFailed": "Couldn't decrypt data.", "uploadedSecurly": "The upload has been secured with end-to-end encryption, which means that Excalidraw server and third parties can't read the content.", "loadSceneOverridePrompt": "Loading external drawing will replace your existing content. Do you wish to continue?", "collabStopOverridePrompt": "Stopping the session will overwrite your previous, locally stored drawing. Are you sure?\n\n(If you want to keep your local drawing, simply close the browser tab instead.)", "errorAddingToLibrary": "Couldn't add item to the library", "errorRemovingFromLibrary": "Couldn't remove item from the library", "confirmAddLibrary": "This will add {{numShapes}} shape(s) to your library. Are you sure?", "imageDoesNotContainScene": "This image does not seem to contain any scene data. Have you enabled scene embedding during export?", "cannotRestoreFromImage": "Scene couldn't be restored from this image file", "invalidSceneUrl": "Couldn't import scene from the supplied URL. It's either malformed, or doesn't contain valid Excalidraw JSON data.", "resetLibrary": "This will clear your library. Are you sure?", "removeItemsFromsLibrary": "Delete {{count}} item(s) from library?", "invalidEncryptionKey": "Encryption key must be of 22 characters. Live collaboration is disabled.", "collabOfflineWarning": "No internet connection available.\nYour changes will not be saved!"}, "errors": {"unsupportedFileType": "Unsupported file type.", "imageInsertError": "Couldn't insert image. Try again later...", "fileTooBig": "File is too big. Maximum allowed size is {{maxSize}}.", "svgImageInsertError": "Couldn't insert SVG image. The SVG markup looks invalid.", "failedToFetchImage": "Failed to fetch image.", "cannotResolveCollabServer": "Couldn't connect to the collab server. Please reload the page and try again.", "importLibraryError": "Couldn't load library", "saveLibraryError": "Couldn't save library to storage. Please save your library to a file locally to make sure you don't lose changes.", "collabSaveFailed": "Couldn't save to the backend database. If problems persist, you should save your file locally to ensure you don't lose your work.", "collabSaveFailed_sizeExceeded": "Couldn't save to the backend database, the canvas seems to be too big. You should save the file locally to ensure you don't lose your work.", "imageToolNotSupported": "Images are disabled.", "brave_measure_text_error": {"line1": "Looks like you are using Brave browser with the <bold>Aggressively Block Fingerprinting</bold> setting enabled.", "line2": "This could result in breaking the <bold>Text Elements</bold> in your drawings.", "line3": "We strongly recommend disabling this setting. You can follow <link>these steps</link> on how to do so.", "line4": "If disabling this setting doesn't fix the display of text elements, please open an <issueLink>issue</issueLink> on our GitHub, or write us on <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "Embeddable elements cannot be added to the library.", "iframe": "IFrame elements cannot be added to the library.", "image": "Support for adding images to the library coming soon!"}, "asyncPasteFailedOnRead": "Couldn't paste (couldn't read from system clipboard).", "asyncPasteFailedOnParse": "Couldn't paste.", "copyToSystemClipboardFailed": "Couldn't copy to clipboard."}, "toolBar": {"selection": "Selection", "lasso": "Lasso selection", "image": "Insert image", "rectangle": "Rectangle", "diamond": "Diamond", "ellipse": "Ellipse", "arrow": "Arrow", "line": "Line", "freedraw": "Draw", "text": "Text", "library": "Library", "lock": "Keep selected tool active after drawing", "penMode": "Pen mode - prevent touch", "link": "Add / Update link for a selected shape", "eraser": "Eraser", "frame": "Frame tool", "magicframe": "Wireframe to code", "embeddable": "Web Embed", "laser": "Laser pointer", "hand": "Hand (panning tool)", "extraTools": "More tools", "mermaidToExcalidraw": "Mermaid to Excalidraw", "convertElementType": "Toggle shape type"}, "element": {"rectangle": "Rectangle", "diamond": "Diamond", "ellipse": "Ellipse", "arrow": "Arrow", "line": "Line", "freedraw": "Freedraw", "text": "Text", "image": "Image", "group": "Group", "frame": "<PERSON>ame", "magicframe": "Wireframe to code", "embeddable": "Web Embed", "selection": "Selection", "iframe": "IFrame"}, "headings": {"canvasActions": "Canvas actions", "selectedShapeActions": "Selected shape actions", "shapes": "<PERSON><PERSON><PERSON>"}, "hints": {"dismissSearch": "Escape to dismiss search", "canvasPanning": "To move canvas, hold mouse wheel or spacebar while dragging, or use the hand tool", "linearElement": "Click to start multiple points, drag for single line", "arrowTool": "Click to start multiple points, drag for single line. Press {{arrowShortcut}} again to change arrow type.", "freeDraw": "Click and drag, release when you're finished", "text": "Tip: you can also add text by double-clicking anywhere with the selection tool", "embeddable": "Click-drag to create a website embed", "text_selected": "Double-click or press ENTER to edit text", "text_editing": "Press Escape or CtrlOrCmd+ENTER to finish editing", "linearElementMulti": "Click on last point or press Escape or Enter to finish", "lockAngle": "You can constrain angle by holding SHIFT", "resize": "You can constrain proportions by holding SHIFT while resizing,\nhold ALT to resize from the center", "resizeImage": "You can resize freely by holding SHIFT,\nhold ALT to resize from the center", "rotate": "You can constrain angles by holding SHIFT while rotating", "lineEditor_info": "Hold CtrlOrCmd and Double-click or press CtrlOrCmd + Enter to edit points", "lineEditor_line_info": "Double-click or press Enter to edit points", "lineEditor_pointSelected": "Press Delete to remove point(s),\nCtrlOrCmd+D to duplicate, or drag to move", "lineEditor_nothingSelected": "Select a point to edit (hold SHIFT to select multiple),\nor hold Alt and click to add new points", "publishLibrary": "Publish your own library", "bindTextToElement": "Press enter to add text", "createFlowchart": "Hold CtrlOrCmd and Arrow key to create a flowchart", "deepBoxSelect": "Hold CtrlOrCmd to deep select, and to prevent dragging", "eraserRevert": "Hold Alt to revert the elements marked for deletion", "firefox_clipboard_write": "This feature can likely be enabled by setting the \"dom.events.asyncClipboard.clipboardItem\" flag to \"true\". To change the browser flags in Firefox, visit the \"about:config\" page.", "disableSnapping": "Hold CtrlOrCmd to disable snapping", "enterCropEditor": "Double click the image or press ENTER to crop the image", "leaveCropEditor": "Click outside the image or press ENTER or ESCAPE to finish cropping"}, "canvasError": {"cannotShowPreview": "Cannot show preview", "canvasTooBig": "The canvas may be too big.", "canvasTooBigTip": "Tip: try moving the farthest elements a bit closer together."}, "errorSplash": {"headingMain": "Encountered an error. Try <button>reloading the page</button>.", "clearCanvasMessage": "If reloading doesn't work, try <button>clearing the canvas</button>.", "clearCanvasCaveat": " This will result in loss of work ", "trackedToSentry": "The error with identifier {{eventId}} was tracked on our system.", "openIssueMessage": "We were very cautious not to include your scene information on the error. If your scene is not private, please consider following up on our <button>bug tracker</button>. Please include information below by copying and pasting into the GitHub issue.", "sceneContent": "Scene content:"}, "shareDialog": {"or": "Or"}, "roomDialog": {"desc_intro": "Invite people to collaborate on your drawing.", "desc_privacy": "Don't worry, the session is end-to-end encrypted, and fully private. Not even our server can see what you draw.", "button_startSession": "Start session", "button_stopSession": "Stop session", "desc_inProgressIntro": "Live-collaboration session is now in progress.", "desc_shareLink": "Share this link with anyone you want to collaborate with:", "desc_exitSession": "Stopping the session will disconnect you from the room, but you'll be able to continue working with the scene, locally. Note that this won't affect other people, and they'll still be able to collaborate on their version.", "shareTitle": "Join a live collaboration session on Excalidraw"}, "errorDialog": {"title": "Error"}, "exportDialog": {"disk_title": "Save to disk", "disk_details": "Export the scene data to a file from which you can import later.", "disk_button": "Save to file", "link_title": "Shareable link", "link_details": "Export as a read-only link.", "link_button": "Export to Link", "excalidrawplus_description": "Save the scene to your Excalidraw+ workspace.", "excalidrawplus_button": "Export", "excalidrawplus_exportError": "Couldn't export to Excalidraw+ at this moment..."}, "helpDialog": {"blog": "Read our blog", "click": "click", "deepSelect": "Deep select", "deepBoxSelect": "Deep select within box, and prevent dragging", "createFlowchart": "Create a flowchart from a generic element", "navigateFlowchart": "Navigate a flowchart", "curvedArrow": "Curved arrow", "curvedLine": "Curved line", "documentation": "Documentation", "doubleClick": "double-click", "drag": "drag", "editor": "Editor", "editLineArrowPoints": "Edit line/arrow points", "editText": "Edit text / add label", "github": "Found an issue? Submit", "howto": "Follow our guides", "or": "or", "preventBinding": "Prevent arrow binding", "tools": "Tools", "shortcuts": "Keyboard shortcuts", "textFinish": "Finish editing (text editor)", "textNewLine": "Add new line (text editor)", "title": "Help", "view": "View", "zoomToFit": "Zoom to fit all elements", "zoomToSelection": "Zoom to selection", "toggleElementLock": "Lock/unlock selection", "movePageUpDown": "Move page up/down", "movePageLeftRight": "Move page left/right", "cropStart": "Crop image", "cropFinish": "Finish image cropping"}, "clearCanvasDialog": {"title": "Clear canvas"}, "publishDialog": {"title": "Publish library", "itemName": "Item name", "authorName": "Author name", "githubUsername": "GitHub username", "twitterUsername": "Twitter username", "libraryName": "Library name", "libraryDesc": "Library description", "website": "Website", "placeholder": {"authorName": "Your name or username", "libraryName": "Name of your library", "libraryDesc": "Description of your library to help people understand its usage", "githubHandle": "GitHub handle (optional), so you can edit the library once submitted for review", "twitterHandle": "Twitter username (optional), so we know who to credit when promoting over Twitter", "website": "Link to your personal website or elsewhere (optional)"}, "errors": {"required": "Required", "website": "Enter a valid URL"}, "noteDescription": "Submit your library to be included in the <link>public library repository</link> for other people to use in their drawings.", "noteGuidelines": "The library needs to be manually approved first. Please read the <link>guidelines</link> before submitting. You will need a GitHub account to communicate and make changes if requested, but it is not strictly required.", "noteLicense": "By submitting, you agree the library will be published under the <link>MIT License</link>, which in short means anyone can use them without restrictions.", "noteItems": "Each library item must have its own name so it's filterable. The following library items will be included:", "atleastOneLibItem": "Please select at least one library item to get started", "republishWarning": "Note: some of the selected items are marked as already published/submitted. You should only resubmit items when updating an existing library or submission."}, "publishSuccessDialog": {"title": "Library submitted", "content": "Thank you {{authorName}}. Your library has been submitted for review. You can track the status <link>here</link>"}, "confirmDialog": {"resetLibrary": "Reset library", "removeItemsFromLib": "Remove selected items from library"}, "imageExportDialog": {"header": "Export image", "label": {"withBackground": "Background", "onlySelected": "Only selected", "darkMode": "Dark mode", "embedScene": "Embed scene", "scale": "Scale", "padding": "Padding"}, "tooltip": {"embedScene": "Scene data will be saved into the exported PNG/SVG file so that the scene can be restored from it.\nWill increase exported file size."}, "title": {"exportToPng": "Export to PNG", "exportToSvg": "Export to SVG", "copyPngToClipboard": "Copy PNG to clipboard"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Copy to clipboard"}}, "encrypted": {"tooltip": "Your drawings are end-to-end encrypted so Excalidraw's servers will never see them.", "link": "Blog post on end-to-end encryption in Excalidraw"}, "stats": {"angle": "<PERSON><PERSON>", "shapes": "<PERSON><PERSON><PERSON>", "height": "Height", "scene": "Scene", "selected": "Selected", "storage": "Storage", "fullTitle": "Canvas & Shape properties", "title": "Properties", "generalStats": "General", "elementProperties": "Shape properties", "total": "Total", "version": "Version", "versionCopy": "Click to copy", "versionNotAvailable": "Version not available", "width": "<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Added to library", "copyStyles": "Copied styles.", "copyToClipboard": "Copied to clipboard.", "copyToClipboardAsPng": "Copied {{exportSelection}} to clipboard as PNG\n({{exportColorScheme}})", "copyToClipboardAsSvg": "Copied {{exportSelection}} to clipboard as SVG\n({{exportColorScheme}})", "fileSaved": "File saved.", "fileSavedToFilename": "Saved to {filename}", "canvas": "canvas", "selection": "selection", "pasteAsSingleElement": "Use {{shortcut}} to paste as a single element,\nor paste into an existing text editor", "unableToEmbed": "Embedding this url is currently not allowed. Raise an issue on GitHub to request the url whitelisted", "unrecognizedLinkFormat": "The link you embedded does not match the expected format. Please try to paste the 'embed' string provided by the source site", "elementLinkCopied": "Link copied to clipboard"}, "colors": {"transparent": "Transparent", "black": "Black", "white": "White", "red": "Red", "pink": "Pink", "grape": "Grape", "violet": "Violet", "gray": "<PERSON>", "blue": "Blue", "cyan": "<PERSON><PERSON>", "teal": "<PERSON><PERSON>", "green": "Green", "yellow": "Yellow", "orange": "Orange", "bronze": "Bronze"}, "welcomeScreen": {"app": {"center_heading": "All your data is saved locally in your browser.", "center_heading_plus": "Did you want to go to the Excalidraw+ instead?", "menuHint": "Export, preferences, languages, ..."}, "defaults": {"menuHint": "Export, preferences, and more...", "center_heading": "Diagrams. Made. Simple.", "toolbarHint": "Pick a tool & Start drawing!", "helpHint": "Shortcuts & help"}}, "colorPicker": {"color": "Color", "mostUsedCustomColors": "Most used custom colors", "colors": "Colors", "shades": "<PERSON>s", "hexCode": "Hex code", "noShades": "No shades available for this color"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Export as image", "button": "Export as image", "description": "Export the scene data as an image from which you can import later."}, "saveToDisk": {"title": "Save to disk", "button": "Save to disk", "description": "Export the scene data to a file from which you can import later."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Export to Excalidraw+", "description": "Save the scene to your Excalidraw+ workspace."}}, "modal": {"loadFromFile": {"title": "Load from file", "button": "Load from file", "description": "Loading from a file will <bold>replace your existing content</bold>.<br></br>You can back up your drawing first using one of the options below."}, "shareableLink": {"title": "Load from link", "button": "Replace my content", "description": "Loading external drawing will <bold>replace your existing content</bold>.<br></br>You can back up your drawing first by using one of the options below."}}}, "mermaid": {"title": "Mermaid to Excalidraw", "button": "Insert", "description": "Currently only <flowchartLink>Flowchart</flowchartLink>,<sequenceLink> Sequence, </sequenceLink> and <classLink>Class </classLink>Diagrams are supported. The other types will be rendered as image in Excalidraw.", "syntax": "Mermaid Syntax", "preview": "Preview"}, "quickSearch": {"placeholder": "Quick search"}, "fontList": {"badge": {"old": "old"}, "sceneFonts": "In this scene", "availableFonts": "Available fonts", "empty": "No fonts found"}, "userList": {"empty": "No users found", "hint": {"text": "Click on user to follow", "followStatus": "You're currently following this user", "inCall": "User is in a voice call", "micMuted": "User's microphone is muted", "isSpeaking": "User is speaking"}}, "commandPalette": {"title": "Command palette", "shortcuts": {"select": "Select", "confirm": "Confirm", "close": "Close"}, "recents": "Recently used", "search": {"placeholder": "Search menus, commands, and discover hidden gems", "noMatch": "No matching commands..."}, "itemNotAvailable": "Command is not available...", "shortcutHint": "For Command palette, use {{shortcut}}"}}