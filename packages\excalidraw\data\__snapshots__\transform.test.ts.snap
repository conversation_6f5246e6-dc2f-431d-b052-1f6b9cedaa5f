// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Test Transform > Test arrow bindings > should bind arrows to existing shapes when start / end provided with ids 1`] = `
{
  "angle": 0,
  "backgroundColor": "#d8f5a2",
  "boundElements": [
    {
      "id": "id47",
      "type": "arrow",
    },
    {
      "id": "id48",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 300,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#66a80f",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 300,
  "x": 630,
  "y": 316,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing shapes when start / end provided with ids 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id48",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#9c36b5",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 140,
  "x": 96,
  "y": 400,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing shapes when start / end provided with ids 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "ellipse-1",
    "focus": -0.007519379844961235,
    "gap": 11.562288374879595,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 35,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      394,
      34,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "id49",
    "focus": -0.0813953488372095,
    "gap": 1,
  },
  "strokeColor": "#1864ab",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 395,
  "x": 247.5,
  "y": 420.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing shapes when start / end provided with ids 4`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "ellipse-1",
    "focus": 0.10666666666666667,
    "gap": 3.8343264684446097,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      399,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "diamond-1",
    "focus": 0,
    "gap": 4.535423522449215,
  },
  "strokeColor": "#e67700",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 400,
  "x": 227.5,
  "y": 450,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing shapes when start / end provided with ids 5`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id47",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 300,
  "id": Any<String>,
  "index": "a4",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 300,
  "x": -53,
  "y": 270,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing text elements when start / end provided with ids 1`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id50",
      "type": "arrow",
    },
  ],
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "HEYYYYY",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#c2255c",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "HEYYYYY",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 70,
  "x": 185,
  "y": 226.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing text elements when start / end provided with ids 2`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id50",
      "type": "arrow",
    },
  ],
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "Whats up ?",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "Whats up ?",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 100,
  "x": 560,
  "y": 226.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing text elements when start / end provided with ids 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id51",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "text-2",
    "focus": 0,
    "gap": 16,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "text-1",
    "focus": 0,
    "gap": 1,
  },
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 255.5,
  "y": 239,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to existing text elements when start / end provided with ids 4`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id50",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "HELLO WORLD!!",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "HELLO WORLD!!",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 130,
  "x": 240,
  "y": 226.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to shapes when start / end provided without ids 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id40",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "id42",
    "focus": -0,
    "gap": 1,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "id41",
    "focus": 0,
    "gap": 1,
  },
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 255.5,
  "y": 239,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to shapes when start / end provided without ids 2`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id39",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "HELLO WORLD!!",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "HELLO WORLD!!",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 130,
  "x": 240,
  "y": 226.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to shapes when start / end provided without ids 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id39",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 155,
  "y": 189,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to shapes when start / end provided without ids 4`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id39",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 355,
  "y": 189,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to text when start / end provided without ids 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id44",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "id46",
    "focus": -0,
    "gap": 1,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "id45",
    "focus": 0,
    "gap": 1,
  },
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 255.5,
  "y": 239,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to text when start / end provided without ids 2`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id43",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "HELLO WORLD!!",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "HELLO WORLD!!",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 130,
  "x": 240,
  "y": 226.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to text when start / end provided without ids 3`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id43",
      "type": "arrow",
    },
  ],
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "HEYYYYY",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "HEYYYYY",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 70,
  "x": 185,
  "y": 226.5,
}
`;

exports[`Test Transform > Test arrow bindings > should bind arrows to text when start / end provided without ids 4`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id43",
      "type": "arrow",
    },
  ],
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "WHATS UP ?",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "WHATS UP ?",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 100,
  "x": 355,
  "y": 226.5,
}
`;

exports[`Test Transform > should not allow duplicate ids 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 200,
  "id": "rect-1",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 300,
  "y": 100,
}
`;

exports[`Test Transform > should transform linear elements 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100.5,
  "y": 20,
}
`;

exports[`Test Transform > should transform linear elements 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "triangle",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": "dot",
  "startBinding": null,
  "strokeColor": "#1971c2",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 450.5,
  "y": 20,
}
`;

exports[`Test Transform > should transform linear elements 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "endArrowhead": null,
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      100,
      0,
    ],
  ],
  "polygon": false,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "line",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100,
  "y": 60,
}
`;

exports[`Test Transform > should transform linear elements 4`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "endArrowhead": null,
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      100,
      0,
    ],
  ],
  "polygon": false,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#2f9e44",
  "strokeStyle": "dotted",
  "strokeWidth": 2,
  "type": "line",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 450,
  "y": 60,
}
`;

exports[`Test Transform > should transform regular shapes 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100,
  "y": 100,
}
`;

exports[`Test Transform > should transform regular shapes 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100,
  "y": 250,
}
`;

exports[`Test Transform > should transform regular shapes 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100,
  "y": 400,
}
`;

exports[`Test Transform > should transform regular shapes 4`] = `
{
  "angle": 0,
  "backgroundColor": "#c0eb75",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 200,
  "x": 300,
  "y": 100,
}
`;

exports[`Test Transform > should transform regular shapes 5`] = `
{
  "angle": 0,
  "backgroundColor": "#ffc9c9",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a4",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "dotted",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 200,
  "x": 300,
  "y": 250,
}
`;

exports[`Test Transform > should transform regular shapes 6`] = `
{
  "angle": 0,
  "backgroundColor": "#a5d8ff",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "cross-hatch",
  "frameId": null,
  "groupIds": [],
  "height": 100,
  "id": Any<String>,
  "index": "a5",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1971c2",
  "strokeStyle": "dashed",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 200,
  "x": 300,
  "y": 400,
}
`;

exports[`Test Transform > should transform text element 1`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "HELLO WORLD!",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "HELLO WORLD!",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 120,
  "x": 100,
  "y": 100,
}
`;

exports[`Test Transform > should transform text element 2`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": null,
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "STYLED HELLO WORLD!",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#5f3dc4",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "STYLED HELLO WORLD!",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 190,
  "x": 100,
  "y": 150,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id56",
      "type": "text",
    },
    {
      "id": "Bob_B",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "subgraph_group_B",
  ],
  "height": 163,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 166.03125,
  "x": 0,
  "y": 0,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id57",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "subgraph_group_A",
  ],
  "height": 114,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 120.265625,
  "x": 364.546875,
  "y": 0,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id58",
      "type": "text",
    },
    {
      "id": "Bob_Alice",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "subgraph_group_A",
  ],
  "height": 44,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 70.265625,
  "x": 389.546875,
  "y": 35,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 4`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id59",
      "type": "text",
    },
    {
      "id": "Bob_Alice",
      "type": "arrow",
    },
    {
      "id": "Bob_B",
      "type": "arrow",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "subgraph_group_B",
  ],
  "height": 44,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 56.4921875,
  "x": 54.76953125,
  "y": 35,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 5`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id60",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "Alice",
    "focus": -0,
    "gap": 5.299874999999986,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a4",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      271.985,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": {
    "type": 2,
  },
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "Bob",
    "focus": 0,
    "gap": 1,
  },
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 272.985,
  "x": 111.762,
  "y": 57,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 6`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id61",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": {
    "elementId": "B",
    "focus": 0,
    "gap": 32,
  },
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a5",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": {
    "type": 2,
  },
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": {
    "elementId": "Bob",
    "focus": 0,
    "gap": 1,
  },
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 0,
  "x": 77.017,
  "y": 79,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 7`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "B",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [
    "subgraph_group_B",
  ],
  "height": 25,
  "id": Any<String>,
  "index": "a6",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "B",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "B",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 10,
  "x": 78.015625,
  "y": 5,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 8`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "A",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [
    "subgraph_group_A",
  ],
  "height": 25,
  "id": Any<String>,
  "index": "a7",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "A",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "A",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 10,
  "x": 419.6796875,
  "y": 5,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 9`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "Alice",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [
    "subgraph_group_A",
  ],
  "height": 25,
  "id": Any<String>,
  "index": "a8",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "Alice",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "Alice",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 50,
  "x": 399.6796875,
  "y": 44.5,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 10`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "Bob",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [
    "subgraph_group_B",
  ],
  "height": 25,
  "id": Any<String>,
  "index": "a9",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "Bob",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "Bob",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 30,
  "x": 68.015625,
  "y": 44.5,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 11`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "Bob_Alice",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "aA",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "How are you?",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "How are you?",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 120,
  "x": 187.75450000000004,
  "y": 44.5,
}
`;

exports[`Test Transform > should transform the elements correctly when linear elements have single point 12`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "Bob_B",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "aB",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "Friendship",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "Friendship",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 100,
  "x": 27.016999999999996,
  "y": 66.5,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id29",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100.5,
  "y": 100,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id30",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100.5,
  "y": 200,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id31",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1098ad",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100.5,
  "y": 300,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 4`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id32",
      "type": "text",
    },
  ],
  "customData": undefined,
  "elbowed": false,
  "endArrowhead": "arrow",
  "endBinding": null,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 0,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "lastCommittedPoint": null,
  "link": null,
  "locked": false,
  "opacity": 100,
  "points": [
    [
      0,
      0,
    ],
    [
      99,
      0,
    ],
  ],
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "startArrowhead": null,
  "startBinding": null,
  "strokeColor": "#1098ad",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "arrow",
  "updated": 1,
  "version": 2,
  "versionNonce": Any<Number>,
  "width": 100,
  "x": 100.5,
  "y": 400,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 5`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id25",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a4",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "LABELED ARROW",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "LABELED ARROW",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 130,
  "x": 85,
  "y": 87.5,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 6`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id26",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a5",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "STYLED LABELED ARROW",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#099268",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "STYLED LABELED ARROW",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 200,
  "x": 50,
  "y": 187.5,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 7`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id27",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": Any<String>,
  "index": "a6",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "ANOTHER STYLED LABELLED ARROW",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1098ad",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "ANOTHER STYLED
LABELLED ARROW",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 140,
  "x": 80,
  "y": 275,
}
`;

exports[`Test Transform > should transform to labelled arrows when label provided for arrows 8`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id28",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": Any<String>,
  "index": "a7",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "ANOTHER STYLED LABELLED ARROW",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#099268",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "ANOTHER STYLED
LABELLED ARROW",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 140,
  "x": 80,
  "y": 375,
}
`;

exports[`Test Transform > should transform to text containers when label provided 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id19",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 35,
  "id": Any<String>,
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": Any<Number>,
  "width": 250,
  "x": 100,
  "y": 100,
}
`;

exports[`Test Transform > should transform to text containers when label provided 2`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id20",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 85,
  "id": Any<String>,
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 200,
  "x": 500,
  "y": 100,
}
`;

exports[`Test Transform > should transform to text containers when label provided 3`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id21",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 170,
  "id": Any<String>,
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 280,
  "x": 100,
  "y": 150,
}
`;

exports[`Test Transform > should transform to text containers when label provided 4`] = `
{
  "angle": 0,
  "backgroundColor": "#fff3bf",
  "boundElements": [
    {
      "id": "id22",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 120,
  "id": Any<String>,
  "index": "a3",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "diamond",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 300,
  "x": 100,
  "y": 400,
}
`;

exports[`Test Transform > should transform to text containers when label provided 5`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": [
    {
      "id": "id23",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 85,
  "id": Any<String>,
  "index": "a4",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#c2255c",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 200,
  "x": 500,
  "y": 300,
}
`;

exports[`Test Transform > should transform to text containers when label provided 6`] = `
{
  "angle": 0,
  "backgroundColor": "#ffec99",
  "boundElements": [
    {
      "id": "id24",
      "type": "text",
    },
  ],
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 120,
  "id": Any<String>,
  "index": "a5",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#f08c00",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "ellipse",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "width": 200,
  "x": 500,
  "y": 500,
}
`;

exports[`Test Transform > should transform to text containers when label provided 7`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id13",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 25,
  "id": Any<String>,
  "index": "a6",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "RECTANGLE TEXT CONTAINER",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "RECTANGLE TEXT CONTAINER",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 240,
  "x": 105,
  "y": 105,
}
`;

exports[`Test Transform > should transform to text containers when label provided 8`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id14",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": Any<String>,
  "index": "a7",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "ELLIPSE TEXT CONTAINER",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "ELLIPSE TEXT
CONTAINER",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 120,
  "x": 539.7893218813452,
  "y": 117.44796179957173,
}
`;

exports[`Test Transform > should transform to text containers when label provided 9`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id15",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 75,
  "id": Any<String>,
  "index": "a8",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "DIAMOND
TEXT CONTAINER",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "DIAMOND
TEXT
CONTAINER",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 90,
  "x": 195,
  "y": 197.5,
}
`;

exports[`Test Transform > should transform to text containers when label provided 10`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id16",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 50,
  "id": Any<String>,
  "index": "a9",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "STYLED DIAMOND TEXT CONTAINER",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#099268",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "STYLED DIAMOND
TEXT CONTAINER",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 140,
  "x": 180,
  "y": 435,
}
`;

exports[`Test Transform > should transform to text containers when label provided 11`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id17",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 75,
  "id": Any<String>,
  "index": "aA",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "TOP LEFT ALIGNED RECTANGLE TEXT CONTAINER",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#c2255c",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "TOP LEFT ALIGNED
RECTANGLE TEXT
CONTAINER",
  "textAlign": "left",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "top",
  "width": 160,
  "x": 505,
  "y": 305,
}
`;

exports[`Test Transform > should transform to text containers when label provided 12`] = `
{
  "angle": 0,
  "autoResize": true,
  "backgroundColor": "transparent",
  "boundElements": null,
  "containerId": "id18",
  "customData": undefined,
  "fillStyle": "solid",
  "fontFamily": 5,
  "fontSize": 20,
  "frameId": null,
  "groupIds": [],
  "height": 75,
  "id": Any<String>,
  "index": "aB",
  "isDeleted": false,
  "lineHeight": 1.25,
  "link": null,
  "locked": false,
  "opacity": 100,
  "originalText": "STYLED ELLIPSE TEXT CONTAINER",
  "roughness": 1,
  "roundness": null,
  "seed": Any<Number>,
  "strokeColor": "#c2255c",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "text": "STYLED
ELLIPSE TEXT
CONTAINER",
  "textAlign": "center",
  "type": "text",
  "updated": 1,
  "version": 3,
  "versionNonce": Any<Number>,
  "verticalAlign": "middle",
  "width": 120,
  "x": 539.7893218813452,
  "y": 522.5735931288071,
}
`;
