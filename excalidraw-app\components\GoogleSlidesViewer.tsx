﻿import React, { useState, useEffect, useRef } from 'react';

interface GoogleSlidesViewerProps {
  isVisible: boolean;
  onClose: () => void;
}

export const GoogleSlidesViewer: React.FC<GoogleSlidesViewerProps> = ({
  isVisible,
  onClose
}) => {
  const [presentationUrl, setPresentationUrl] = useState('');
  const [embedUrl, setEmbedUrl] = useState('');
  const [currentSlide, setCurrentSlide] = useState(1);
  const [totalSlides, setTotalSlides] = useState(20);
  const [status, setStatus] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [position, setPosition] = useState({ x: 20, y: 20 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const viewerRef = useRef<HTMLDivElement>(null);

  // Extract presentation ID from Google Slides URL
  const extractPresentationId = (url: string): string | null => {
    const patterns = [
      /\/presentation\/d\/([a-zA-Z0-9-_]+)/,
      /\/presentation\/d\/([a-zA-Z0-9-_]+)\/edit/,
      /\/presentation\/d\/([a-zA-Z0-9-_]+)\/view/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  // Generate embed URL for Google Slides
  const generateEmbedUrl = (url: string, slide: number = 1): string => {
    const presentationId = extractPresentationId(url);
    if (!presentationId) return '';
    
    return `https://docs.google.com/presentation/d/${presentationId}/embed?start=false&loop=false&delayms=3000&slide=${slide}&rm=minimal`;
  };

  // Show status message
  const showStatus = (message: string, type: 'success' | 'error' = 'success') => {
    setStatus({ message, type });
    setTimeout(() => setStatus(null), 3000);
  };

  // Handle URL input change
  const handleUrlChange = (url: string) => {
    setPresentationUrl(url);
    if (url && extractPresentationId(url)) {
      setEmbedUrl(generateEmbedUrl(url, currentSlide));
      showStatus(' Valid URL!', 'success');
    } else if (url) {
      setEmbedUrl('');
      showStatus(' Invalid URL', 'error');
    }
  };

  // Navigate slides
  const navigateSlide = (direction: number) => {
    const newSlide = Math.max(1, Math.min(totalSlides, currentSlide + direction));
    if (newSlide !== currentSlide) {
      setCurrentSlide(newSlide);
      if (presentationUrl) {
        setEmbedUrl(generateEmbedUrl(presentationUrl, newSlide));
      }
    }
  };

  // Open presentation in new tab
  const openInNewTab = () => {
    if (presentationUrl) {
      window.open(presentationUrl, '_blank');
    }
  };

  // Dragging functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {
      setIsDragging(true);
      const rect = viewerRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVisible || !embedUrl) return;
      
      switch(e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          navigateSlide(-1);
          break;
        case 'ArrowRight':
          e.preventDefault();
          navigateSlide(1);
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isVisible, embedUrl, currentSlide, totalSlides]);

  // Mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  if (!isVisible) return null;

  const viewerStyle: React.CSSProperties = {
    position: 'fixed',
    top: `${position.y}px`,
    left: `${position.x}px`,
    width: '400px',
    height: '500px',
    background: 'white',
    borderRadius: '8px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    zIndex: 1000,
    border: '1px solid #e0e0e0',
    resize: 'both',
    minWidth: '300px',
    minHeight: '350px',
    maxWidth: '600px',
    maxHeight: '80vh',
    cursor: isDragging ? 'grabbing' : 'default'
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 16px',
    borderBottom: '1px solid #e0e0e0',
    background: '#f8f9fa',
    cursor: 'grab',
    userSelect: 'none'
  };

  const inputStyle: React.CSSProperties = {
    width: '100%',
    padding: '8px 12px',
    border: '1px solid #e0e0e0',
    borderRadius: '4px',
    fontSize: '12px',
    boxSizing: 'border-box'
  };

  const buttonStyle: React.CSSProperties = {
    background: '#4285f4',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    padding: '6px 12px',
    cursor: 'pointer',
    fontSize: '12px',
    fontWeight: '500',
    margin: '0 2px'
  };

  const closeButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    background: '#ff4444',
    padding: '4px 8px'
  };

  return (
    <div 
      ref={viewerRef}
      style={viewerStyle}
      onMouseDown={handleMouseDown}
    >
      <div style={headerStyle} className="drag-handle">
        <h4 style={{ margin: 0, color: '#333', fontSize: '14px' }}> Google Slides</h4>
        <button style={closeButtonStyle} onClick={onClose}></button>
      </div>

      <div style={{ padding: '12px', borderBottom: '1px solid #e0e0e0' }}>
        <input
          type="url"
          placeholder="Paste Google Slides URL..."
          value={presentationUrl}
          onChange={(e) => handleUrlChange(e.target.value)}
          style={inputStyle}
        />
        
        {status && (
          <div style={{
            marginTop: '6px',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '11px',
            fontWeight: '500',
            background: status.type === 'success' ? '#d4edda' : '#f8d7da',
            color: status.type === 'success' ? '#155724' : '#721c24',
            border: `1px solid ${status.type === 'success' ? '#c3e6cb' : '#f5c6cb'}`
          }}>
            {status.message}
          </div>
        )}
      </div>

      {embedUrl && (
        <>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 12px',
            borderBottom: '1px solid #e0e0e0',
            background: '#fafafa',
            fontSize: '12px'
          }}>
            <button 
              onClick={() => navigateSlide(-1)}
              disabled={currentSlide <= 1}
              style={{
                ...buttonStyle,
                background: currentSlide <= 1 ? '#ccc' : '#4285f4',
                fontSize: '11px',
                padding: '4px 8px'
              }}
            >
              
            </button>
            
            <span style={{ fontSize: '11px', color: '#333', flex: 1, textAlign: 'center' }}>
              {currentSlide} / {totalSlides}
            </span>

            <button 
              onClick={() => navigateSlide(1)}
              disabled={currentSlide >= totalSlides}
              style={{
                ...buttonStyle,
                background: currentSlide >= totalSlides ? '#ccc' : '#4285f4',
                fontSize: '11px',
                padding: '4px 8px'
              }}
            >
              
            </button>

            <button
              onClick={openInNewTab}
              style={{
                ...buttonStyle,
                background: '#34a853',
                fontSize: '11px',
                padding: '4px 8px'
              }}
              title="Open in Google Slides"
            >
              
            </button>
          </div>

          <div style={{ flex: 1, position: 'relative' }}>
            <iframe
              src={embedUrl}
              width="100%"
              height="100%"
              frameBorder="0"
              allowFullScreen
              title="Google Slides Presentation"
              style={{ border: 'none', background: 'white' }}
            />
          </div>
        </>
      )}

      {!embedUrl && (
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px',
          textAlign: 'center'
        }}>
          <div>
            <div style={{ fontSize: '24px', marginBottom: '12px' }}></div>
            <div style={{ fontSize: '12px', color: '#666', lineHeight: 1.4 }}>
              Paste a Google Slides URL above to view slides while annotating
            </div>
            <div style={{
              background: '#f0f8ff',
              padding: '8px',
              borderRadius: '4px',
              marginTop: '12px',
              fontSize: '11px',
              color: '#1a73e8'
            }}>
              <strong> Tip:</strong> Use   keys to navigate
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
