// Global variables
let schoolData = null;
let filteredSchools = [];
let currentFilter = 'all';
let currentTab = 'schools';

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    loadSchoolData();
});

function initializeDashboard() {
    console.log('🚀 Initializing UK Schools Lead Generation Dashboard');
    
    // Show loading state
    showLoadingState();
    
    // Initialize tabs
    setupTabs();
    
    // Initialize filters
    setupFilters();
}

function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    // Filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', () => handleFilter(btn.dataset.filter));
    });
    
    // Tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });
    
    // Modal close on background click
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });
}

function setupTabs() {
    // Ensure schools tab is active by default
    switchTab('schools');
}

function setupFilters() {
    // Ensure 'all' filter is active by default
    handleFilter('all');
}

async function loadSchoolData() {
    try {
        console.log('📡 Loading school data...');
        
        // Try to load existing data first
        const response = await fetch('./data/school_data.json');
        
        if (response.ok) {
            schoolData = await response.json();
            console.log('✅ Loaded existing school data');
            processSchoolData();
        } else {
            console.log('📊 No existing data found, collecting new data...');
            await collectNewData();
        }
        
    } catch (error) {
        console.error('❌ Error loading school data:', error);
        showErrorState('Failed to load school data. Please try refreshing the page.');
    }
}

async function collectNewData() {
    try {
        // Show collection in progress
        updateLoadingMessage('Collecting school data from Apollo.io...');
        
        // This would typically call your backend API
        // For now, we'll simulate the data collection
        await simulateDataCollection();
        
    } catch (error) {
        console.error('❌ Error collecting new data:', error);
        showErrorState('Failed to collect new data from Apollo.io');
    }
}

async function simulateDataCollection() {
    // Simulate API calls and data collection
    const steps = [
        'Connecting to Apollo.io API...',
        'Enriching school organization data...',
        'Searching education contacts...',
        'Searching education accounts...',
        'Generating email suggestions...',
        'Compiling final dataset...'
    ];
    
    for (let i = 0; i < steps.length; i++) {
        updateLoadingMessage(steps[i]);
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Create sample data structure
    schoolData = {
        lastUpdated: new Date().toISOString(),
        stats: {
            total_schools_processed: 25,
            total_contacts_found: 12,
            total_accounts_found: 8,
            total_email_suggestions: 350,
            api_calls_used: 45,
            success_rate: 76
        },
        schools: generateSampleSchools(),
        contacts: generateSampleContacts(),
        accounts: generateSampleAccounts(),
        emailSuggestions: generateSampleEmailSuggestions()
    };
    
    console.log('✅ Data collection simulation completed');
    processSchoolData();
}

function generateSampleSchools() {
    return [
        {
            name: 'Harris Federation',
            domain: 'harrisacademies.org.uk',
            type: 'MAT',
            region: 'London',
            apollo_data: {
                name: 'Harris Federation',
                industry: 'Education',
                size: 500,
                location: { city: 'London', country: 'United Kingdom' },
                description: 'Multi Academy Trust operating schools across London'
            },
            apollo_error: null,
            enriched_at: new Date().toISOString()
        },
        {
            name: 'Ark Schools',
            domain: 'arkschools.org',
            type: 'MAT',
            region: 'National',
            apollo_data: {
                name: 'Ark Schools',
                industry: 'Education',
                size: 750,
                location: { city: 'London', country: 'United Kingdom' },
                description: 'Network of academy schools across England'
            },
            apollo_error: null,
            enriched_at: new Date().toISOString()
        },
        {
            name: 'United Learning',
            domain: 'unitedlearning.org.uk',
            type: 'MAT',
            region: 'National',
            apollo_data: {
                name: 'United Learning',
                industry: 'Education',
                size: 1200,
                location: { city: 'London', country: 'United Kingdom' },
                description: 'Leading education group with schools and colleges'
            },
            apollo_error: null,
            enriched_at: new Date().toISOString()
        },
        {
            name: 'Eton College',
            domain: 'etoncollege.org.uk',
            type: 'Independent',
            region: 'Berkshire',
            apollo_data: null,
            apollo_error: 'Organization not found in Apollo database',
            enriched_at: new Date().toISOString()
        },
        {
            name: 'Westminster School',
            domain: 'westminster.org.uk',
            type: 'Independent',
            region: 'London',
            apollo_data: {
                name: 'Westminster School',
                industry: 'Education',
                size: 200,
                location: { city: 'London', country: 'United Kingdom' },
                description: 'Independent school in Westminster'
            },
            apollo_error: null,
            enriched_at: new Date().toISOString()
        }
    ];
}

function generateSampleContacts() {
    return [
        {
            name: 'Sarah Johnson',
            title: 'Headteacher',
            organization: 'Harris Academy Bermondsey',
            email: '<EMAIL>',
            apollo_id: 'contact_001'
        },
        {
            name: 'Michael Chen',
            title: 'IT Director',
            organization: 'Ark Schools',
            email: '<EMAIL>',
            apollo_id: 'contact_002'
        },
        {
            name: 'Emma Williams',
            title: 'Business Manager',
            organization: 'United Learning',
            email: '<EMAIL>',
            apollo_id: 'contact_003'
        }
    ];
}

function generateSampleAccounts() {
    return [
        {
            name: 'Harris Federation',
            domain: 'harrisacademies.org.uk',
            industry: 'Education',
            size: 500,
            apollo_id: 'account_001'
        },
        {
            name: 'Ark Schools',
            domain: 'arkschools.org',
            industry: 'Education',
            size: 750,
            apollo_id: 'account_002'
        }
    ];
}

function generateSampleEmailSuggestions() {
    return [
        {
            school: 'Harris Federation',
            domain: 'harrisacademies.org.uk',
            type: 'MAT',
            region: 'London',
            suggestions: [
                { email: '<EMAIL>', type: 'headteacher', confidence: 'high' },
                { email: '<EMAIL>', type: 'admin', confidence: 'high' },
                { email: '<EMAIL>', type: 'it', confidence: 'medium' },
                { email: '<EMAIL>', type: 'business', confidence: 'medium' }
            ],
            total_emails: 4
        },
        {
            school: 'Ark Schools',
            domain: 'arkschools.org',
            type: 'MAT',
            region: 'National',
            suggestions: [
                { email: '<EMAIL>', type: 'headteacher', confidence: 'high' },
                { email: '<EMAIL>', type: 'admin', confidence: 'high' },
                { email: '<EMAIL>', type: 'it', confidence: 'medium' }
            ],
            total_emails: 3
        }
    ];
}

function processSchoolData() {
    if (!schoolData) {
        showErrorState('No school data available');
        return;
    }
    
    console.log('📊 Processing school data...');
    
    // Update header stats
    updateHeaderStats();
    
    // Update summary cards
    updateSummaryCards();
    
    // Initialize filtered schools
    filteredSchools = schoolData.schools || [];
    
    // Render current tab content
    renderCurrentTab();
    
    // Update footer
    updateFooter();
    
    // Hide loading state and show dashboard
    hideLoadingState();
    
    console.log('✅ School data processed successfully');
}

function updateHeaderStats() {
    const stats = schoolData.stats || {};
    
    document.getElementById('totalSchools').textContent = stats.total_schools_processed || 0;
    document.getElementById('totalContacts').textContent = stats.total_contacts_found || 0;
    document.getElementById('totalEmails').textContent = stats.total_email_suggestions || 0;
}

function updateSummaryCards() {
    const stats = schoolData.stats || {};
    
    document.getElementById('schoolsProcessed').textContent = stats.total_schools_processed || 0;
    document.getElementById('contactsFound').textContent = stats.total_contacts_found || 0;
    document.getElementById('emailSuggestions').textContent = stats.total_email_suggestions || 0;
    document.getElementById('successRate').textContent = `${stats.success_rate || 0}%`;
    
    // Update subtitles with more specific info
    const enrichedCount = schoolData.schools ? schoolData.schools.filter(s => s.apollo_data).length : 0;
    document.getElementById('schoolsSubtitle').textContent = `${enrichedCount} successfully enriched with Apollo data`;
    
    const contactsWithEmail = schoolData.contacts ? schoolData.contacts.filter(c => c.email).length : 0;
    document.getElementById('contactsSubtitle').textContent = `${contactsWithEmail} with verified email addresses`;
    
    const avgEmailsPerSchool = stats.total_schools_processed > 0 ? 
        Math.round(stats.total_email_suggestions / stats.total_schools_processed) : 0;
    document.getElementById('emailSubtitle').textContent = `Average ${avgEmailsPerSchool} emails per school`;
    
    document.getElementById('successSubtitle').textContent = `${stats.api_calls_used || 0} API calls used`;
}

function updateFooter() {
    const lastUpdated = schoolData.lastUpdated ? 
        new Date(schoolData.lastUpdated).toLocaleString() : 'Never';
    document.getElementById('lastUpdated').textContent = lastUpdated;
    
    const apiCalls = schoolData.stats ? schoolData.stats.api_calls_used || 0 : 0;
    document.getElementById('apiCallsUsed').textContent = apiCalls;
}

function showLoadingState() {
    document.getElementById('loadingState').style.display = 'flex';
    document.getElementById('dashboardContent').style.display = 'none';
}

function hideLoadingState() {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('dashboardContent').style.display = 'block';
    document.getElementById('dashboardContent').classList.add('fade-in');
}

function updateLoadingMessage(message) {
    const loadingElement = document.querySelector('.loading-state p');
    if (loadingElement) {
        loadingElement.textContent = message;
    }
}

function showErrorState(message) {
    const loadingState = document.getElementById('loadingState');
    loadingState.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-exclamation-triangle" style="color: #f56565;"></i>
        </div>
        <p style="color: #f56565;">${message}</p>
        <button class="btn btn-primary" onclick="location.reload()" style="margin-top: 1rem;">
            <i class="fas fa-refresh"></i> Retry
        </button>
    `;
}

function handleSearch(event) {
    const query = event.target.value.toLowerCase().trim();
    
    if (!schoolData || !schoolData.schools) return;
    
    if (query === '') {
        filteredSchools = schoolData.schools;
    } else {
        filteredSchools = schoolData.schools.filter(school => 
            school.name.toLowerCase().includes(query) ||
            school.domain.toLowerCase().includes(query) ||
            school.type.toLowerCase().includes(query) ||
            school.region.toLowerCase().includes(query)
        );
    }
    
    renderCurrentTab();
}

function handleFilter(filterType) {
    currentFilter = filterType;
    
    // Update filter button states
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');
    
    if (!schoolData || !schoolData.schools) return;
    
    // Apply filter
    if (filterType === 'all') {
        filteredSchools = schoolData.schools;
    } else if (filterType === 'enriched') {
        filteredSchools = schoolData.schools.filter(school => school.apollo_data);
    } else {
        filteredSchools = schoolData.schools.filter(school => school.type === filterType);
    }
    
    renderCurrentTab();
}

function switchTab(tabName) {
    currentTab = tabName;
    
    // Update tab button states
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content visibility
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}Tab`).classList.add('active');
    
    renderCurrentTab();
}

function renderCurrentTab() {
    switch (currentTab) {
        case 'schools':
            renderSchools();
            break;
        case 'contacts':
            renderContacts();
            break;
        case 'emails':
            renderEmailSuggestions();
            break;
        case 'analytics':
            renderAnalytics();
            break;
    }
}

function renderSchools() {
    const grid = document.getElementById('schoolsGrid');
    if (!grid || !filteredSchools) return;
    
    if (filteredSchools.length === 0) {
        grid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No schools found matching your criteria.</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = filteredSchools.map(school => `
        <div class="school-card slide-in" onclick="showSchoolDetails('${school.name}')">
            <div class="school-header">
                <div class="school-name">${school.name}</div>
                <div class="school-type ${school.type}">${school.type}</div>
            </div>
            <div class="school-info">
                <p><i class="fas fa-globe"></i> <span class="school-domain">${school.domain}</span></p>
                <p><i class="fas fa-map-marker-alt"></i> ${school.region}</p>
                ${school.apollo_data ? `
                    <p><i class="fas fa-industry"></i> ${school.apollo_data.industry || 'N/A'}</p>
                    <p><i class="fas fa-users"></i> ${school.apollo_data.size || 'N/A'} employees</p>
                ` : ''}
            </div>
            <div class="school-status">
                <div class="status-indicator ${school.apollo_data ? 'success' : 'error'}"></div>
                <span class="status-text">
                    ${school.apollo_data ? 'Enriched with Apollo data' : 'Apollo enrichment failed'}
                </span>
            </div>
        </div>
    `).join('');
}

function renderContacts() {
    const grid = document.getElementById('contactsGrid');
    if (!grid || !schoolData || !schoolData.contacts) return;
    
    const contacts = schoolData.contacts;
    
    if (contacts.length === 0) {
        grid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No contacts found in Apollo CRM.</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = contacts.map(contact => `
        <div class="contact-card slide-in">
            <div class="contact-name">${contact.name || 'N/A'}</div>
            <div class="contact-title">${contact.title || 'N/A'}</div>
            <div class="contact-organization">${contact.organization || 'N/A'}</div>
            <div class="contact-details">
                ${contact.email ? `
                    <div class="contact-detail">
                        <i class="fas fa-envelope"></i>
                        <span>${contact.email}</span>
                    </div>
                ` : ''}
                ${contact.phone ? `
                    <div class="contact-detail">
                        <i class="fas fa-phone"></i>
                        <span>${contact.phone}</span>
                    </div>
                ` : ''}
                <div class="contact-detail">
                    <i class="fas fa-id-badge"></i>
                    <span>Apollo ID: ${contact.apollo_id}</span>
                </div>
            </div>
        </div>
    `).join('');
}

function renderEmailSuggestions() {
    const grid = document.getElementById('emailsGrid');
    if (!grid || !schoolData || !schoolData.emailSuggestions) return;
    
    const emailSuggestions = schoolData.emailSuggestions;
    
    if (emailSuggestions.length === 0) {
        grid.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-envelope" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No email suggestions generated.</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = emailSuggestions.map(schoolEmails => `
        <div class="email-school-group slide-in">
            <div class="email-school-header">
                <div class="email-school-name">${schoolEmails.school}</div>
                <div class="email-count">${schoolEmails.total_emails} emails</div>
            </div>
            <div class="email-suggestions">
                ${schoolEmails.suggestions.map(suggestion => `
                    <div class="email-suggestion">
                        <div class="email-address">${suggestion.email}</div>
                        <div class="email-type">${suggestion.type}</div>
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('');
}

function renderAnalytics() {
    const content = document.getElementById('analyticsContent');
    if (!content || !schoolData) return;
    
    const stats = schoolData.stats || {};
    
    content.innerHTML = `
        <div class="analytics-grid">
            <div class="analytics-card slide-in">
                <h3>Collection Summary</h3>
                <div class="metric-item">
                    <span class="metric-label">Schools Processed</span>
                    <span class="metric-value">${stats.total_schools_processed || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">API Calls Used</span>
                    <span class="metric-value">${stats.api_calls_used || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Success Rate</span>
                    <span class="metric-value">${stats.success_rate || 0}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Collection Date</span>
                    <span class="metric-value">${new Date(schoolData.lastUpdated).toLocaleDateString()}</span>
                </div>
            </div>
            
            <div class="analytics-card slide-in">
                <h3>Data Quality</h3>
                <div class="metric-item">
                    <span class="metric-label">Schools with Apollo Data</span>
                    <span class="metric-value">${schoolData.schools ? schoolData.schools.filter(s => s.apollo_data).length : 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Contacts with Email</span>
                    <span class="metric-value">${schoolData.contacts ? schoolData.contacts.filter(c => c.email).length : 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Total Email Suggestions</span>
                    <span class="metric-value">${stats.total_email_suggestions || 0}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Average Emails per School</span>
                    <span class="metric-value">${stats.total_schools_processed > 0 ? Math.round(stats.total_email_suggestions / stats.total_schools_processed) : 0}</span>
                </div>
            </div>
            
            <div class="analytics-card slide-in">
                <h3>School Types</h3>
                ${generateSchoolTypeBreakdown()}
            </div>
            
            <div class="analytics-card slide-in">
                <h3>Regional Distribution</h3>
                ${generateRegionalBreakdown()}
            </div>
        </div>
    `;
}

function generateSchoolTypeBreakdown() {
    if (!schoolData || !schoolData.schools) return '<p>No data available</p>';
    
    const typeCount = {};
    schoolData.schools.forEach(school => {
        typeCount[school.type] = (typeCount[school.type] || 0) + 1;
    });
    
    return Object.entries(typeCount).map(([type, count]) => `
        <div class="metric-item">
            <span class="metric-label">${type}</span>
            <span class="metric-value">${count}</span>
        </div>
    `).join('');
}

function generateRegionalBreakdown() {
    if (!schoolData || !schoolData.schools) return '<p>No data available</p>';
    
    const regionCount = {};
    schoolData.schools.forEach(school => {
        regionCount[school.region] = (regionCount[school.region] || 0) + 1;
    });
    
    return Object.entries(regionCount).map(([region, count]) => `
        <div class="metric-item">
            <span class="metric-label">${region}</span>
            <span class="metric-value">${count}</span>
        </div>
    `).join('');
}

function showSchoolDetails(schoolName) {
    const school = schoolData.schools.find(s => s.name === schoolName);
    if (!school) return;
    
    const modal = document.getElementById('schoolModal');
    const modalTitle = document.getElementById('modalSchoolName');
    const modalContent = document.getElementById('modalSchoolContent');
    
    modalTitle.textContent = school.name;
    
    modalContent.innerHTML = `
        <div style="display: grid; gap: 1.5rem;">
            <div>
                <h4 style="margin-bottom: 0.5rem; color: #2d3748;">Basic Information</h4>
                <p><strong>Domain:</strong> ${school.domain}</p>
                <p><strong>Type:</strong> ${school.type}</p>
                <p><strong>Region:</strong> ${school.region}</p>
                <p><strong>Enriched:</strong> ${school.enriched_at ? new Date(school.enriched_at).toLocaleString() : 'N/A'}</p>
            </div>
            
            ${school.apollo_data ? `
                <div>
                    <h4 style="margin-bottom: 0.5rem; color: #2d3748;">Apollo Data</h4>
                    <p><strong>Organization Name:</strong> ${school.apollo_data.name || 'N/A'}</p>
                    <p><strong>Industry:</strong> ${school.apollo_data.industry || 'N/A'}</p>
                    <p><strong>Size:</strong> ${school.apollo_data.size || 'N/A'} employees</p>
                    <p><strong>Location:</strong> ${school.apollo_data.location?.city || 'N/A'}, ${school.apollo_data.location?.country || 'N/A'}</p>
                    ${school.apollo_data.description ? `<p><strong>Description:</strong> ${school.apollo_data.description}</p>` : ''}
                </div>
            ` : `
                <div>
                    <h4 style="margin-bottom: 0.5rem; color: #f56565;">Apollo Enrichment Failed</h4>
                    <p style="color: #718096;">${school.apollo_error || 'Unknown error occurred'}</p>
                </div>
            `}
            
            <div>
                <h4 style="margin-bottom: 0.5rem; color: #2d3748;">Generated Email Suggestions</h4>
                ${generateSchoolEmailSuggestions(school)}
            </div>
        </div>
    `;
    
    modal.style.display = 'block';
}

function generateSchoolEmailSuggestions(school) {
    const emailData = schoolData.emailSuggestions?.find(e => e.school === school.name);
    
    if (!emailData || !emailData.suggestions) {
        return '<p style="color: #718096;">No email suggestions available</p>';
    }
    
    return `
        <div style="display: grid; gap: 0.5rem;">
            ${emailData.suggestions.map(suggestion => `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem; background: #f7fafc; border-radius: 0.25rem;">
                    <span style="font-family: monospace; font-size: 0.875rem;">${suggestion.email}</span>
                    <span style="font-size: 0.75rem; color: #718096;">${suggestion.type}</span>
                </div>
            `).join('')}
        </div>
    `;
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

async function refreshData() {
    console.log('🔄 Refreshing data...');
    showLoadingState();
    updateLoadingMessage('Refreshing school data...');
    
    try {
        await collectNewData();
    } catch (error) {
        console.error('❌ Error refreshing data:', error);
        showErrorState('Failed to refresh data. Please try again.');
    }
}

function exportData() {
    if (!schoolData) {
        alert('No data available to export');
        return;
    }
    
    const dataStr = JSON.stringify(schoolData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `uk-schools-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    
    console.log('📁 Data exported successfully');
}
