import axios from 'axios';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import { rateLimiter } from '../utils/rateLimiter.js';

class ApolloService {
  constructor() {
    this.baseUrl = config.apollo.baseUrl;
    this.apiKey = config.apollo.apiKey;
    
    // Create axios instance with authentication
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'X-Api-Key': this.apiKey
      }
    });
  }

  /**
   * Search for people in UK education sector
   * @param {Object} searchParams - Search parameters
   * @returns {Promise<Object>} Search results
   */
  async searchEducationContacts(searchParams = {}) {
    try {
      await rateLimiter.consume('apollo');
      
      const defaultParams = {
        q_keywords: 'headteacher OR "head teacher" OR "IT director" OR "business manager" OR principal',
        person_locations: ['United Kingdom'],
        organization_industry_tag_ids: ['5567cd4e73696439b10b0000'], // Education industry
        page: 1,
        per_page: 25
      };

      const params = { ...defaultParams, ...searchParams };
      
      logger.info(`Searching Apollo for education contacts with keywords: ${params.q_keywords}`);
      
      const response = await this.client.get('/mixed_people/search', { params });
      
      logger.info(`Found ${response.data.pagination.total_entries} education contacts`);
      
      return {
        contacts: response.data.people || [],
        pagination: response.data.pagination,
        search_params: params
      };
    } catch (error) {
      logger.error('Error searching Apollo for education contacts:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Search for contacts at specific schools or MATs
   * @param {string} organizationName - School or MAT name
   * @param {Array} jobTitles - Job titles to search for
   * @returns {Promise<Object>} Contact results
   */
  async searchSchoolContacts(organizationName, jobTitles = []) {
    try {
      await rateLimiter.consume('apollo');
      
      const defaultJobTitles = [
        'headteacher',
        'head teacher', 
        'principal',
        'IT director',
        'ICT coordinator',
        'business manager',
        'deputy head',
        'assistant head',
        'CEO',
        'executive head'
      ];

      const searchTitles = jobTitles.length > 0 ? jobTitles : defaultJobTitles;
      const titleQuery = searchTitles.join(' OR ');
      
      const params = {
        q_organization_name: organizationName,
        q_person_titles: titleQuery,
        person_locations: ['United Kingdom'],
        page: 1,
        per_page: 25
      };
      
      logger.info(`Searching for contacts at "${organizationName}" with titles: ${titleQuery}`);
      
      const response = await this.client.get('/mixed_people/search', { params });
      
      const contacts = response.data.people || [];
      logger.info(`Found ${contacts.length} contacts at ${organizationName}`);
      
      return {
        organization: organizationName,
        contacts: contacts.map(contact => this.formatContact(contact)),
        total_found: response.data.pagination?.total_entries || contacts.length
      };
    } catch (error) {
      logger.error(`Error searching contacts for ${organizationName}:`, error.response?.data || error.message);
      return {
        organization: organizationName,
        contacts: [],
        total_found: 0,
        error: error.message
      };
    }
  }

  /**
   * Get detailed person information
   * @param {string} personId - Apollo person ID
   * @returns {Promise<Object>} Detailed person data
   */
  async getPersonDetails(personId) {
    try {
      await rateLimiter.consume('apollo');
      
      const response = await this.client.get(`/people/${personId}`);
      
      logger.info(`Retrieved detailed info for person ${personId}`);
      return this.formatContact(response.data.person);
    } catch (error) {
      logger.error(`Error getting person details for ${personId}:`, error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Search for organizations (schools/MATs) by name
   * @param {string} organizationName - Organization name to search
   * @returns {Promise<Object>} Organization results
   */
  async searchOrganizations(organizationName) {
    try {
      await rateLimiter.consume('apollo');
      
      const params = {
        q_organization_name: organizationName,
        organization_locations: ['United Kingdom'],
        organization_industry_tag_ids: ['5567cd4e73696439b10b0000'], // Education
        page: 1,
        per_page: 10
      };
      
      logger.info(`Searching for organizations matching: ${organizationName}`);
      
      const response = await this.client.get('/organizations/search', { params });
      
      const organizations = response.data.organizations || [];
      logger.info(`Found ${organizations.length} organizations matching "${organizationName}"`);
      
      return {
        query: organizationName,
        organizations: organizations.map(org => this.formatOrganization(org)),
        total_found: response.data.pagination?.total_entries || organizations.length
      };
    } catch (error) {
      logger.error(`Error searching organizations for ${organizationName}:`, error.response?.data || error.message);
      return {
        query: organizationName,
        organizations: [],
        total_found: 0,
        error: error.message
      };
    }
  }

  /**
   * Enrich contact data for multiple schools
   * @param {Array} schools - Array of school objects with names
   * @returns {Promise<Array>} Enriched school data with contacts
   */
  async enrichSchoolContacts(schools) {
    logger.info(`Starting contact enrichment for ${schools.length} schools`);
    
    const enrichedSchools = [];
    const batchSize = 5; // Process in small batches to respect rate limits
    
    for (let i = 0; i < schools.length; i += batchSize) {
      const batch = schools.slice(i, i + batchSize);
      logger.info(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(schools.length/batchSize)}`);
      
      const batchPromises = batch.map(async (school) => {
        try {
          const contacts = await this.searchSchoolContacts(school.name || school.title);
          return {
            ...school,
            apollo_contacts: contacts.contacts,
            apollo_total_found: contacts.total_found,
            apollo_enriched_at: new Date().toISOString()
          };
        } catch (error) {
          logger.error(`Error enriching contacts for ${school.name}:`, error.message);
          return {
            ...school,
            apollo_contacts: [],
            apollo_total_found: 0,
            apollo_error: error.message,
            apollo_enriched_at: new Date().toISOString()
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      enrichedSchools.push(...batchResults);
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < schools.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    logger.info(`Contact enrichment completed. Processed ${enrichedSchools.length} schools`);
    return enrichedSchools;
  }

  /**
   * Format contact data for consistency
   * @param {Object} contact - Raw Apollo contact data
   * @returns {Object} Formatted contact
   */
  formatContact(contact) {
    return {
      id: contact.id,
      name: contact.name,
      first_name: contact.first_name,
      last_name: contact.last_name,
      title: contact.title,
      email: contact.email,
      phone: contact.sanitized_phone,
      linkedin_url: contact.linkedin_url,
      organization: {
        name: contact.organization?.name,
        domain: contact.organization?.primary_domain,
        industry: contact.organization?.industry,
        size: contact.organization?.estimated_num_employees
      },
      location: {
        city: contact.city,
        state: contact.state,
        country: contact.country
      },
      seniority: contact.seniority,
      departments: contact.departments,
      apollo_id: contact.id,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Format organization data for consistency
   * @param {Object} org - Raw Apollo organization data
   * @returns {Object} Formatted organization
   */
  formatOrganization(org) {
    return {
      id: org.id,
      name: org.name,
      domain: org.primary_domain,
      website: org.website_url,
      industry: org.industry,
      size: org.estimated_num_employees,
      location: {
        city: org.city,
        state: org.state,
        country: org.country
      },
      phone: org.phone,
      apollo_id: org.id,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Test Apollo API connection
   * @returns {Promise<Object>} Connection test result
   */
  async testConnection() {
    try {
      await rateLimiter.consume('apollo');
      
      // Simple search to test the connection
      const response = await this.client.get('/mixed_people/search', {
        params: {
          q_keywords: 'test',
          person_locations: ['United Kingdom'],
          per_page: 1
        }
      });
      
      return {
        success: true,
        message: 'Apollo API connection successful',
        credits_remaining: response.headers['x-daily-requests-left'] || 'Unknown',
        rate_limit: response.headers['x-hourly-requests-left'] || 'Unknown'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Apollo API connection failed',
        error: error.response?.data || error.message
      };
    }
  }
}

export const apolloService = new ApolloService();
