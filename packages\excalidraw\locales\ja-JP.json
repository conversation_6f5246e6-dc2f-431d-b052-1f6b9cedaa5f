{"labels": {"paste": "貼り付け", "pasteAsPlaintext": "書式なしテキストとして貼り付け", "pasteCharts": "チャートの貼り付け", "selectAll": "すべて選択", "multiSelect": "複数選択", "moveCanvas": "キャンバスを移動", "cut": "切り取り", "copy": "コピー", "copyAsPng": "PNGとしてクリップボードへコピー", "copyAsSvg": "SVGとしてクリップボードへコピー", "copyText": "テキストとしてクリップボードにコピー", "copySource": "", "convertToCode": "", "bringForward": "前面に移動", "sendToBack": "最背面に移動", "bringToFront": "最前面に移動", "sendBackward": "背面に移動", "delete": "削除", "copyStyles": "スタイルのコピー", "pasteStyles": "スタイルの貼り付け", "stroke": "線", "background": "背景", "fill": "塗りつぶし", "strokeWidth": "線の太さ", "strokeStyle": "線の種類", "strokeStyle_solid": "実線", "strokeStyle_dashed": "破線", "strokeStyle_dotted": "点線", "sloppiness": "ばらつき加減", "opacity": "透明度", "textAlign": "文字の配置", "edges": "角", "sharp": "四角", "round": "丸", "arrowheads": "線の終点", "arrowhead_none": "なし", "arrowhead_arrow": "矢印", "arrowhead_bar": "バー", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "三角", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "フォントの大きさ", "fontFamily": "フォントの種類", "addWatermark": "\"Made with Excalidraw\"と表示", "handDrawn": "手描き風", "normal": "普通", "code": "コード", "small": "小", "medium": "中", "large": "大", "veryLarge": "特大", "solid": "ベタ塗り", "hachure": "斜線", "zigzag": "ジグザグ", "crossHatch": "網掛け", "thin": "細", "bold": "太字", "left": "左寄せ", "center": "中央寄せ", "right": "右寄せ", "extraBold": "極太", "architect": "正確", "artist": "アート", "cartoonist": "漫画風", "fileTitle": "ファイル名", "colorPicker": "色選択", "canvasColors": "キャンバス上で使用", "canvasBackground": "キャンバスの背景", "drawingCanvas": "キャンバスの描画", "layers": "レイヤー", "actions": "操作", "language": "言語", "liveCollaboration": "共同編集...", "duplicateSelection": "複製", "untitled": "無題", "name": "名前", "yourName": "あなたの名前", "madeWithExcalidraw": "Excalidrawで作成", "group": "グループ化", "ungroup": "グループ化を解除", "collaborators": "共同編集者", "showGrid": "グリッドを表示", "addToLibrary": "ライブラリに追加", "removeFromLibrary": "ライブラリから削除", "libraryLoadingMessage": "ライブラリを読み込み中…", "libraries": "ライブラリを参照する", "loadingScene": "シーンを読み込み中…", "align": "配置", "alignTop": "上揃え", "alignBottom": "下揃え", "alignLeft": "左揃え", "alignRight": "右揃え", "centerVertically": "縦方向に中央揃え", "centerHorizontally": "横方向に中央揃え", "distributeHorizontally": "水平方向に分散配置", "distributeVertically": "垂直方向に分散配置", "flipHorizontal": "水平方向に反転", "flipVertical": "垂直方向に反転", "viewMode": "閲覧モード", "share": "共有", "showStroke": "ストロークカラーピッカーを表示", "showBackground": "背景色ピッカーを表示", "toggleTheme": "テーマの切り替え", "personalLib": "個人ライブラリ", "excalidrawLib": "Excalidrawライブラリ", "decreaseFontSize": "フォントサイズを縮小", "increaseFontSize": "フォントサイズを拡大", "unbindText": "テキストのバインド解除", "bindText": "テキストをコンテナにバインド", "createContainerFromText": "コンテナ内でテキストを折り返す", "link": {"edit": "リンクを編集", "editEmbed": "リンクの編集と埋め込み", "create": "リンクを作成", "createEmbed": "リンクの作成と埋め込み", "label": "リンク", "labelEmbed": "リンクと埋め込み", "empty": "リンクが設定されていません"}, "lineEditor": {"edit": "行を編集", "exit": "行エディタを終了"}, "elementLock": {"lock": "ロック", "unlock": "ロック解除", "lockAll": "すべてロック", "unlockAll": "すべてのロックを解除"}, "statusPublished": "公開済み", "sidebarLock": "サイドバーを開いたままにする", "selectAllElementsInFrame": "フレーム内のすべての要素を選択", "removeAllElementsFromFrame": "フレーム内のすべての要素を削除", "eyeDropper": "キャンバスから色を選択", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "まだアイテムが追加されていません…", "hint_emptyLibrary": "キャンバス上のアイテムを選択してここに追加するか、以下の公開リポジトリからライブラリをインストールしてください。", "hint_emptyPrivateLibrary": "キャンバス上のアイテムを選択すると、ここに追加されます。"}, "buttons": {"clearReset": "キャンバスのリセット", "exportJSON": "ファイルへエクスポート", "exportImage": "画像のエクスポート...", "export": "名前を付けて保存...", "copyToClipboard": "クリップボードにコピー", "save": "現在のファイルに保存", "saveAs": "名前を付けて保存", "load": "開く", "getShareableLink": "共有URLの取得", "close": "閉じる", "selectLanguage": "言語の選択", "scrollBackToContent": "コンテンツまでスクロールで戻る", "zoomIn": "拡大", "zoomOut": "縮小", "resetZoom": "拡大/縮小をリセット", "menu": "メニュー", "done": "完了", "edit": "編集", "undo": "元に戻す", "redo": "やり直し", "resetLibrary": "ライブラリをリセット", "createNewRoom": "新しい部屋を作成する", "fullScreen": "フルスクリーン", "darkMode": "ダークモード", "lightMode": "ライトモード", "zenMode": "Zenモード", "objectsSnapMode": "", "exitZenMode": "集中モードをやめる", "cancel": "キャンセル", "clear": "消去", "remove": "削除", "embed": "埋め込みの切り替え", "publishLibrary": "公開", "submit": "送信", "confirm": "確認", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "この操作によってキャンバス全体が消えます。よろしいですか？", "couldNotCreateShareableLink": "共有URLを作成できませんでした。", "couldNotCreateShareableLinkTooBig": "共有可能なリンクを作成できませんでした: シーンが大きすぎます", "couldNotLoadInvalidFile": "無効なファイルを読み込めませんでした。", "importBackendFailed": "サーバーからの読み込みに失敗しました。", "cannotExportEmptyCanvas": "空のキャンバスはエクスポートできません。", "couldNotCopyToClipboard": "クリップボードにコピーできませんでした。", "decryptFailed": "データを復号できませんでした。", "uploadedSecurly": "データのアップロードはエンドツーエンド暗号化によって保護されています。Excalidrawサーバーと第三者はデータの内容を見ることができません。", "loadSceneOverridePrompt": "外部図面を読み込むと、既存のコンテンツが置き換わります。続行しますか？", "collabStopOverridePrompt": "セッションを停止すると、ローカルに保存されている図が上書きされます。 本当によろしいですか？\n\n(ローカルの図を保持したい場合は、セッションを停止せずにブラウザタブを閉じてください。)", "errorAddingToLibrary": "アイテムをライブラリに追加できませんでした", "errorRemovingFromLibrary": "ライブラリからアイテムを削除できませんでした", "confirmAddLibrary": "{{numShapes}} 個の図形をライブラリに追加します。よろしいですか？", "imageDoesNotContainScene": "この画像にはシーンデータが含まれていないようです。エクスポート時にシーンの埋め込みを有効にしましたか？", "cannotRestoreFromImage": "このイメージファイルからシーンを復元できませんでした", "invalidSceneUrl": "指定された URL からシーンをインポートできませんでした。不正な形式であるか、有効な Excalidraw JSON データが含まれていません。", "resetLibrary": "ライブラリを消去します。本当によろしいですか？", "removeItemsFromsLibrary": "{{count}} 個のアイテムをライブラリから削除しますか？", "invalidEncryptionKey": "暗号化キーは22文字でなければなりません。ライブコラボレーションは無効化されています。", "collabOfflineWarning": "インターネットに接続されていません。\n変更は保存されません！"}, "errors": {"unsupportedFileType": "サポートされていないファイル形式です。", "imageInsertError": "画像を挿入できませんでした。後でもう一度お試しください...", "fileTooBig": "ファイルが大きすぎます。許可される最大サイズは {{maxSize}} です。", "svgImageInsertError": "SVGイメージを挿入できませんでした。SVGマークアップは無効に見えます。", "failedToFetchImage": "", "invalidSVGString": "無効なSVGです。", "cannotResolveCollabServer": "コラボレーションサーバに接続できませんでした。ページを再読み込みして、もう一度お試しください。", "importLibraryError": "ライブラリを読み込めませんでした。", "collabSaveFailed": "バックエンドデータベースに保存できませんでした。問題が解決しない場合は、作業を失わないようにローカルにファイルを保存してください。", "collabSaveFailed_sizeExceeded": "キャンバスが大きすぎるため、バックエンドデータベースに保存できませんでした。問題が解決しない場合は、作業を失わないようにローカルにファイルを保存してください。", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "<bold>Aggressly Block Fingerprinting</bold> の設定が有効なBraveブラウザを使用しているようです。", "line2": "これにより、図面の <bold>テキスト要素</bold> が壊れる可能性があります。", "line3": "この設定を無効にすることを強く推奨します。 <link>設定手順</link> をこちらから確認できます。", "line4": "この設定を無効にすると、テキスト要素の表示が修正されません。 GitHub で <issueLink>Issue</issueLink> を開くか、 <discordLink>Discord</discordLink> にご記入ください"}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "選択", "image": "画像を挿入", "rectangle": "矩形", "diamond": "ひし形", "ellipse": "楕円", "arrow": "矢印", "line": "直線", "freedraw": "描画", "text": "テキスト", "library": "ライブラリ", "lock": "描画後も使用中のツールを選択したままにする", "penMode": "ペンモード - タッチ防止", "link": "選択した図形のリンクを追加/更新", "eraser": "消しゴム", "frame": "フレームツール", "magicframe": "", "embeddable": "Web埋め込み", "laser": "", "hand": "手 (パンニングツール)", "extraTools": "その他のツール", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "キャンバス操作", "selectedShapeActions": "選択された図形に対する操作", "shapes": "図形"}, "hints": {"canvasPanning": "キャンバスを移動するには、マウスホイールまたはスペースバーを押しながらドラッグするか、手ツールを使用します", "linearElement": "クリックすると複数の頂点からなる曲線を開始、ドラッグすると直線", "freeDraw": "クリックしてドラッグします。離すと終了します", "text": "ヒント: 選択ツールを使用して任意の場所をダブルクリックしてテキストを追加することもできます", "embeddable": "", "text_selected": "テキストを編集するには、ダブルクリックまたはEnterキーを押します", "text_editing": "Esc キーまたは CtrlOrCmd+ENTER キーを押して編集を終了します", "linearElementMulti": "最後のポイントをクリックするか、エスケープまたはEnterを押して終了します", "lockAngle": "SHIFTを押したままにすると、角度を制限することができます", "resize": "サイズを変更中にSHIFTを押すと縦横比を固定できます。Altを押すと中央からサイズを変更できます", "resizeImage": "SHIFTを長押しすると自由にサイズを変更できます。\n中央からサイズを変更するにはALTを長押しします", "rotate": "回転中にSHIFT キーを押すと角度を制限することができます", "lineEditor_info": "CtrlOrCmd を押したままダブルクリックするか、CtrlOrCmd + Enter を押して点を編集します", "lineEditor_pointSelected": "Deleteキーを押すと点を削除、CtrlOrCmd+Dで複製、マウスドラッグで移動", "lineEditor_nothingSelected": "編集する点を選択（SHIFTを押したままで複数選択）、\nAltキーを押しながらクリックすると新しい点を追加", "placeImage": "クリックして画像を配置するか、クリックしてドラッグしてサイズを手動で設定します", "publishLibrary": "自分のライブラリを公開", "bindTextToElement": "Enterを押してテキストを追加", "deepBoxSelect": "CtrlOrCmd を押し続けることでドラッグを抑止し、深い選択を行います", "eraserRevert": "Alt を押し続けることで削除マークされた要素を元に戻す", "firefox_clipboard_write": "この機能は、\"dom.events.asyncClipboard.clipboardItem\" フラグを \"true\" に設定することで有効になる可能性があります。Firefox でブラウザーの設定を変更するには、\"about:config\" ページを参照してください。", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "プレビューを表示できません", "canvasTooBig": "キャンバスが大きすぎます。", "canvasTooBigTip": "ヒント: 最も遠い要素をもう少し近づけてみてください。"}, "errorSplash": {"headingMain": "エラーが発生しました。もう一度やり直してください。 <button>ページを再読み込みする。</button>", "clearCanvasMessage": "再読み込みがうまくいかない場合は、 <button>キャンバスを消去しています</button>", "clearCanvasCaveat": " これにより作業が失われます ", "trackedToSentry": "識別子のエラー {{eventId}} が我々のシステムで追跡されました。", "openIssueMessage": "エラーに関するシーン情報を含めないように非常に慎重に設定しました。もしあなたのシーンがプライベートでない場合は、私たちのフォローアップを検討してください。 <button>バグ報告</button> GitHub のIssueに以下の情報をコピーして貼り付けてください。", "sceneContent": "シーンの内容:"}, "roomDialog": {"desc_intro": "他の人を編集中のあなたの画面に招待して共同編集することができます。", "desc_privacy": "このセッションはエンドツーエンド暗号化されており、描画内容は保護されています。運営サーバーからも内容は見えません。", "button_startSession": "セッションを開始する", "button_stopSession": "セッションを終了する", "desc_inProgressIntro": "共同編集セッションが有効になっています。", "desc_shareLink": "下記URLを共同編集したい人に共有してください：", "desc_exitSession": "セッションを終了するとあなたはルームから切断されますが、ローカルで作業を続けることができます。セッションを終了しても他のメンバには影響はなく、引き続き共同作業を行うことができます。", "shareTitle": "Excalidrawの共同編集セッションに参加する"}, "errorDialog": {"title": "エラー"}, "exportDialog": {"disk_title": "ディスクに保存", "disk_details": "シーンデータを後からインポートできるファイルにエクスポートします。", "disk_button": "ファイルへ保存", "link_title": "共有可能なリンク", "link_details": "読み取り専用リンクとしてエクスポート", "link_button": "リンクとしてエクスポート", "excalidrawplus_description": "Excalidraw+ ワークスペースにシーンを保存します。", "excalidrawplus_button": "エクスポート", "excalidrawplus_exportError": "Excalidraw+ にエクスポートできませんでした..."}, "helpDialog": {"blog": "公式ブログを読む", "click": "クリック", "deepSelect": "深い選択", "deepBoxSelect": "ボックス内の深い選択、およびドラッグの抑止", "curvedArrow": "カーブした矢印", "curvedLine": "曲線", "documentation": "ドキュメント", "doubleClick": "ダブルクリック", "drag": "ドラッグ", "editor": "エディタ", "editLineArrowPoints": "", "editText": "テキストの編集 / ラベルの追加", "github": "不具合報告はこちら", "howto": "ヘルプ・マニュアル", "or": "または", "preventBinding": "矢印を結合しない", "tools": "ツール", "shortcuts": "キーボードショートカット", "textFinish": "編集を終了 (テキストエディタ)", "textNewLine": "新しい行を追加 (テキスト)", "title": "ヘルプ", "view": "表示", "zoomToFit": "すべての要素が収まるようにズーム", "zoomToSelection": "選択要素にズーム", "toggleElementLock": "選択したアイテムをロック/ロック解除", "movePageUpDown": "ページを上下に移動", "movePageLeftRight": "ページを左右に移動"}, "clearCanvasDialog": {"title": "キャンバスを消去"}, "publishDialog": {"title": "ライブラリを公開", "itemName": "アイテム名", "authorName": "作成者名", "githubUsername": "GitHub ユーザ名", "twitterUsername": "Twitter ユーザ名", "libraryName": "ライブラリ名", "libraryDesc": "ライブラリの説明", "website": "Webサイト", "placeholder": {"authorName": "お名前またはユーザー名", "libraryName": "あなたのライブラリ名", "libraryDesc": "ライブラリの使い方を理解するための説明", "githubHandle": "GitHubハンドル(任意)。一度レビューのために送信されると、ライブラリを編集できます", "twitterHandle": "Twitterのユーザー名 (任意)。Twitterでプロモーションする際にクレジットする人を知っておくためのものです", "website": "個人のウェブサイトまたは他のサイトへのリンク (任意)"}, "errors": {"required": "必須項目", "website": "有効な URL を入力してください"}, "noteDescription": "以下に含めるライブラリを提出してください <link>公開ライブラリのリポジトリ</link>他の人が作図に使えるようにするためです", "noteGuidelines": "最初にライブラリを手動で承認する必要があります。次をお読みください <link>ガイドライン</link> 送信する前に、GitHubアカウントが必要になりますが、必須ではありません。", "noteLicense": "提出することにより、ライブラリが次の下で公開されることに同意します： <link>MIT ライセンス </link>つまり誰でも制限なく使えるということです", "noteItems": "各ライブラリ項目は、フィルタリングのために独自の名前を持つ必要があります。以下のライブラリアイテムが含まれます:", "atleastOneLibItem": "開始するには少なくとも1つのライブラリ項目を選択してください", "republishWarning": "注意: 選択された項目の中には、すでに公開/投稿済みと表示されているものがあります。既存のライブラリや投稿を更新する場合のみ、アイテムを再投稿してください。"}, "publishSuccessDialog": {"title": "ライブラリを送信しました", "content": "{{authorName}} さん、ありがとうございます。あなたのライブラリはレビューのために提出されました。状況を追跡できます。<link>こちら</link>"}, "confirmDialog": {"resetLibrary": "ライブラリをリセット", "removeItemsFromLib": "選択したアイテムをライブラリから削除"}, "imageExportDialog": {"header": "画像をエクスポート", "label": {"withBackground": "背景", "onlySelected": "", "darkMode": "ダークモード", "embedScene": "", "scale": "スケール", "padding": "余白"}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "PNG にエクスポート", "exportToSvg": "SVG にエクスポート", "copyPngToClipboard": "クリップボードにPNGをコピー"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "クリップボードにコピー"}}, "encrypted": {"tooltip": "描画内容はエンドツーエンド暗号化が施されており、Excalidrawサーバーが内容を見ることはできません。", "link": "Excalidrawのエンドツーエンド暗号化に関するブログ記事"}, "stats": {"angle": "角度", "element": "要素", "elements": "要素", "height": "高さ", "scene": "シーン", "selected": "選択済み", "storage": "ストレージ", "title": "詳細統計情報", "total": "合計", "version": "バージョン", "versionCopy": "クリックしてコピー", "versionNotAvailable": "利用できないバージョン", "width": "幅"}, "toast": {"addedToLibrary": "ライブラリに追加しました", "copyStyles": "スタイルをコピーしました。", "copyToClipboard": "クリップボードにコピー", "copyToClipboardAsPng": "{{exportSelection}} を PNG 形式でクリップボードにコピーしました\n({{exportColorScheme}})", "fileSaved": "ファイルを保存しました", "fileSavedToFilename": "{filename} に保存しました", "canvas": "キャンバス", "selection": "選択", "pasteAsSingleElement": "{{shortcut}} を使用して単一の要素として貼り付けるか、\n既存のテキストエディタに貼り付け", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "透明", "black": "黒", "white": "白", "red": "赤", "pink": "ピンク", "grape": "グレープ", "violet": "バイオレット", "gray": "灰色", "blue": "青", "cyan": "シアン", "teal": "ティール", "green": "緑", "yellow": "黄", "orange": "オレンジ", "bronze": "ブロンズ"}, "welcomeScreen": {"app": {"center_heading": "すべてのデータはブラウザにローカル保存されます。", "center_heading_plus": "代わりにExcalidraw+を開きますか？", "menuHint": "エクスポート、設定、言語..."}, "defaults": {"menuHint": "エクスポート、設定、その他...", "center_heading": "ダイアグラムを簡単に。", "toolbarHint": "ツールを選んで描き始めよう！", "helpHint": "ショートカットとヘルプ"}}, "colorPicker": {"mostUsedCustomColors": "最も使用されているカスタム色", "colors": "色", "shades": "影", "hexCode": "Hexコード", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "画像としてエクスポート", "button": "画像としてエクスポート", "description": ""}, "saveToDisk": {"title": "ディスクに保存", "button": "ディスクに保存", "description": ""}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Excalidraw+にエクスポート", "description": "Excalidraw+ ワークスペースにシーンを保存します。"}}, "modal": {"loadFromFile": {"title": "ファイルからロード", "button": "ファイルからロード", "description": ""}, "shareableLink": {"title": "リンクからロード", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}