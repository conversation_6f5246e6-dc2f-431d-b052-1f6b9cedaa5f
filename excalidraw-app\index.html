<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Excalidraw | Hand-drawn look & feel • Collaborative • Secure</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, shrink-to-fit=no"
    />
    <meta name="referrer" content="origin" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#121212" />

    <!-- Primary Meta Tags -->
    <meta
      name="title"
      content="Excalidraw — Collaborative whiteboarding made easy"
    />
    <meta
      name="description"
      content="Excalidraw is a virtual collaborative whiteboard tool that lets you easily sketch diagrams that have a hand-drawn feel to them."
    />
    <meta name="image" content="https://excalidraw.com/og-image-3.png" />

    <!-- Open Graph / Facebook -->
    <meta property="og:site_name" content="Excalidraw" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://excalidraw.com" />
    <meta
      property="og:title"
      content="Excalidraw — Collaborative whiteboarding made easy"
    />
    <meta property="og:image:alt" content="Excalidraw logo" />
    <meta
      property="og:description"
      content="Excalidraw is a virtual collaborative whiteboard tool that lets you easily sketch diagrams that have a hand-drawn feel to them."
    />
    <meta property="og:image" content="https://excalidraw.com/og-image-3.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:site" content="@excalidraw" />
    <meta property="twitter:url" content="https://excalidraw.com" />
    <meta
      property="twitter:title"
      content="Excalidraw — Collaborative whiteboarding made easy"
    />
    <meta
      property="twitter:description"
      content="Excalidraw is a virtual collaborative whiteboard tool that lets you easily sketch diagrams that have a hand-drawn feel to them."
    />
    <meta
      property="twitter:image"
      content="https://excalidraw.com/og-image-3.png"
    />

    <link rel="canonical" href="https://excalidraw.com" />

    <!------------------------------------------------------------------------->
    <!--   to minimize white flash on load when user has dark mode enabled   -->
    <script>
      try {
        function setTheme(theme) {
          if (theme === "dark") {
            document.documentElement.classList.add("dark");
          } else {
            document.documentElement.classList.remove("dark");
          }
        }

        function getTheme() {
          const theme = window.localStorage.getItem("excalidraw-theme");

          if (theme && theme === "system") {
            return window.matchMedia("(prefers-color-scheme: dark)").matches
              ? "dark"
              : "light";
          } else {
            return theme || "light";
          }
        }

        setTheme(getTheme());
      } catch (e) {
        console.error("Error setting dark mode", e);
      }
    </script>
    <style>
      html.dark {
        background-color: #121212;
        color: #fff;
      }
    </style>

    <!-- Warmup the connection for Google fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!------------------------------------------------------------------------->
    <% if (typeof PROD != 'undefined' && PROD == true) { %>
    <script>
      // Redirect Excalidraw+ users which have auto-redirect enabled.
      //
      // Redirect only the bare root path, so link/room/library urls are not
      // redirected.
      //
      // Putting into index.html for best performance (can't redirect on server
      // due to location.hash checks).
      if (
        window.location.pathname === "/" &&
        !window.location.hash &&
        !window.location.search &&
        // if its present redirect
        document.cookie.includes("excplus-autoredirect=true")
      ) {
        window.location.href = "https://app.excalidraw.com";
      }
    </script>

    <!-- Following placeholder is replaced during the build step -->
    <!-- PLACEHOLDER:EXCALIDRAW_APP_FONTS -->

    <!-- Register Assistant as the UI font, before the scene inits -->
    <link
      rel="stylesheet"
      href="../packages/excalidraw/fonts/fonts.css"
      type="text/css"
    />

    <% } else { %>
    <script>
      window.EXCALIDRAW_ASSET_PATH = window.origin;
    </script>
    <% } %>

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

    <!-- Excalidraw version -->
    <meta name="version" content="{version}" />

    <% if (typeof VITE_APP_DEV_DISABLE_LIVE_RELOAD != 'undefined' &&
    VITE_APP_DEV_DISABLE_LIVE_RELOAD == true) { %>
    <script>
      {
        const _WebSocket = window.WebSocket;
        window.WebSocket = function (url) {
          if (/ws:\/\/localhost:.+?\/sockjs-node/.test(url)) {
            console.info(
              "[!!!] Live reload is disabled via VITE_APP_DEV_DISABLE_LIVE_RELOAD [!!!]",
            );
          } else {
            return new _WebSocket(url);
          }
        };
      }
    </script>
    <% } %>
    <script>
      // setting this so that libraries installation reuses this window tab.
      window.name = "_excalidraw";
    </script>

    <!-- FIXME: remove this when we update CRA (fix SW caching) -->
    <style>
      body,
      html {
        margin: 0;
        -webkit-text-size-adjust: 100%;

        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .visually-hidden {
        position: absolute !important;
        height: 1px;
        width: 1px;
        overflow: hidden;
        clip: rect(1px, 1px, 1px, 1px);
        white-space: nowrap;
        user-select: none;
      }

      #root {
        height: 100%;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      @media screen and (min-width: 1200px) {
        #root {
          -webkit-touch-callout: default;
          -webkit-user-select: auto;
          -khtml-user-select: auto;
          -moz-user-select: auto;
          -ms-user-select: auto;
          user-select: auto;
        }
      }
    </style>
  </head>

  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>
    <header>
      <h1 class="visually-hidden">Excalidraw</h1>
    </header>
    <div id="root"></div>
    <script type="module" src="index.tsx"></script>
    <% if (typeof PROD != 'undefined' && PROD == true) { %>
    <!-- 100% privacy friendly analytics -->
    <script>
      // need to load this script dynamically bcs. of iframe embed tracking
      var scriptEle = document.createElement("script");
      scriptEle.setAttribute(
        "src",
        "https://scripts.simpleanalyticscdn.com/latest.js",
      );
      scriptEle.setAttribute("type", "text/javascript");
      scriptEle.setAttribute("defer", true);
      scriptEle.setAttribute("async", true);
      // if iframe
      if (window.self !== window.top) {
        scriptEle.setAttribute("data-auto-collect", true);
      }

      document.body.appendChild(scriptEle);

      // if iframe
      if (window.self !== window.top) {
        scriptEle.addEventListener("load", () => {
          if (window.sa_pageview) {
            window.window.sa_event(action, {
              category: "iframe",
              label: "embed",
              value: window.location.pathname,
            });
          }
        });
      }
    </script>
    <!-- end LEGACY GOOGLE ANALYTICS -->
    <% } %>
  </body>
</html>
