﻿# Google Slides Integration for Excalidraw

This integration adds Google Slides viewing capabilities to Excalidraw, allowing users to view and navigate through presentations while using the whiteboard.

##  Features

-  **Browser-based Google Slides embedding** (like ViewSonic myViewBoard)
-  **Side-by-side viewing** with Excalidraw canvas
-  **Floating presentation viewer** option
-  **Keyboard navigation** (arrow keys, Home, End, Escape)
-  **Present mode toggle** (edit vs presentation view)
-  **Real-time slide synchronization**
-  **Responsive design** for mobile and desktop
-  **Direct link to original presentation**

##  Files Created

### Demo Files
- google-slides-demo.html - Standalone Google Slides viewer demo
- excalidraw-integration-demo.html - Integration preview showing how it works with Excalidraw

### React Components
- excalidraw-app/components/GoogleSlidesViewer.tsx - Main viewer component
- excalidraw-app/components/GoogleSlidesViewer.scss - Styles for the viewer
- excalidraw-app/components/GoogleSlidesButton.tsx - Toolbar button component

##  Integration Steps

### Step 1: Add to Excalidraw Toolbar

Add the Google Slides button to the main Excalidraw toolbar by importing and using the component:

`	ypescript
// In excalidraw-app/App.tsx
import { GoogleSlidesButton } from './components/GoogleSlidesButton';

// Add to the toolbar render section
<GoogleSlidesButton />
`

### Step 2: Add Styles

Import the SCSS file in your main stylesheet:

`scss
// In excalidraw-app/index.scss
@import './components/GoogleSlidesViewer.scss';
`

### Step 3: Test Integration

1. Start the Excalidraw development server
2. Click the " Google Slides" button in the toolbar
3. Paste a Google Slides URL and test navigation

##  How to Use

### For Users:
1. **Open Google Slides** presentation
2. **Share the presentation** (make it viewable by anyone with the link)
3. **Copy the URL** from your browser
4. **Click the Google Slides button** in Excalidraw toolbar
5. **Paste the URL** and start navigating slides
6. **Use keyboard shortcuts** for quick navigation:
   - ` ` Navigate slides
   - Home End First/Last slide
   - Esc Close viewer

### Supported URL Formats:
- https://docs.google.com/presentation/d/[ID]/edit
- https://docs.google.com/presentation/d/[ID]/view
- https://docs.google.com/presentation/d/[ID]/present

##  Integration Options

### Option 1: Modal Overlay (Current Implementation)
- Full-screen overlay when activated
- Easy to implement and use
- Good for focused presentation viewing

### Option 2: Side Panel
`	ypescript
// Split screen layout
<div className="excalidraw-layout">
  <div className="canvas-area">
    <Excalidraw />
  </div>
  <div className="slides-panel">
    <GoogleSlidesViewer />
  </div>
</div>
`

### Option 3: Floating Window
`	ypescript
// Draggable, resizable floating window
<div className="excalidraw-container">
  <Excalidraw />
  <FloatingGoogleSlides 
    position={{ x: 100, y: 100 }}
    size={{ width: 400, height: 500 }}
  />
</div>
`

##  Customization

### Styling
The viewer uses CSS custom properties for easy theming:

`scss
.google-slides-viewer {
  --primary-color: #4285f4;
  --success-color: #34a853;
  --error-color: #ea4335;
  --border-radius: 8px;
}
`

### Keyboard Shortcuts
Customize keyboard shortcuts in the useEffect hook:

`	ypescript
const handleKeyDown = (e: KeyboardEvent) => {
  switch(e.key) {
    case 'ArrowLeft':
      navigateSlide(-1);
      break;
    // Add more shortcuts...
  }
};
`

##  Advanced Features (Future)

### PowerPoint Support
Extend to support PowerPoint Online URLs:

`	ypescript
const extractPresentationId = (url: string) => {
  // Add PowerPoint patterns
  const powerpointPatterns = [
    /office\.com.*\/([a-zA-Z0-9-_]+)/,
    // Add more patterns
  ];
  // Implementation...
};
`

### Slide Thumbnails
Add thumbnail navigation:

`	ypescript
const SlideThumbnails = ({ slides, currentSlide, onSlideClick }) => (
  <div className="slide-thumbnails">
    {slides.map((slide, index) => (
      <img 
        key={index}
        src={slide.thumbnail}
        onClick={() => onSlideClick(index + 1)}
        className={index + 1 === currentSlide ? 'active' : ''}
      />
    ))}
  </div>
);
`

### Annotation Sync
Sync annotations between slides and canvas:

`	ypescript
const syncAnnotations = (slideNumber: number, annotations: Annotation[]) => {
  // Save annotations for specific slide
  localStorage.setItem(slide--annotations, JSON.stringify(annotations));
};
`

##  Troubleshooting

### Common Issues:

1. **"Invalid Google Slides URL"**
   - Ensure the presentation is shared publicly
   - Check URL format matches supported patterns

2. **Slides not loading**
   - Verify internet connection
   - Check if presentation allows embedding

3. **Keyboard shortcuts not working**
   - Ensure the viewer has focus
   - Check for conflicting shortcuts

### Browser Compatibility:
-  Chrome/Chromium (recommended)
-  Firefox
-  Safari
-  Edge

##  Comparison with ViewSonic myViewBoard

| Feature | ViewSonic myViewBoard | Our Implementation |
|---------|----------------------|-------------------|
| Google Slides Support |  |  |
| Browser-based |  |  |
| Slide Navigation |  |  Enhanced |
| Present Mode |  |  |
| Mobile Support |  |  Better |
| Keyboard Shortcuts |  |  |
| Offline Capability |  |  |
| Open Source |  |  |
| Customizable |  |  |

##  Contributing

To extend this integration:

1. **Add new presentation platforms** (PowerPoint, Canva, etc.)
2. **Improve UI/UX** with better animations and interactions
3. **Add collaborative features** for multi-user slide viewing
4. **Implement slide caching** for offline access

##  License

This integration follows the same license as Excalidraw.
