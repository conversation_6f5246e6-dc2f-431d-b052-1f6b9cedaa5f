// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": {
    "items": [
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M9.15 14.85l8.85 -10.85"
            />
            <path
              d="M6 4l8.85 10.85"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.cut",
        "name": "cut",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": undefined,
        "label": "labels.copy",
        "name": "copy",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keyTest": undefined,
        "label": "labels.paste",
        "name": "paste",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.selectAllElementsInFrame",
        "name": "selectAllElementsInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
        },
      },
      {
        "label": "labels.removeAllElementsFromFrame",
        "name": "removeAllElementsFromFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "history",
        },
      },
      {
        "label": "labels.wrapSelectionInFrame",
        "name": "wrapSelectionInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 5v10a1 1 0 0 0 1 1h10"
            />
            <path
              d="M5 8h10a1 1 0 0 1 1 1v10"
            />
          </g>
        </svg>,
        "keywords": [
          "image",
          "crop",
        ],
        "label": "helpDialog.cropStart",
        "name": "cropEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "menu",
        },
        "viewMode": true,
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
            <path
              d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6"
            />
            <path
              d="M11 21v-6l3 6v-6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "png",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsPng",
        "name": "copyAsPng",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M4 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75"
            />
            <path
              d="M10 15l2 6l2 -6"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
          </g>
        </svg>,
        "keywords": [
          "svg",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsSvg",
        "name": "copyAsSvg",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keywords": [
          "text",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyText",
        "name": "copyText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.copyStyles",
        "name": "copyStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.pasteStyles",
        "name": "pasteStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.group",
        "name": "group",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": null,
        "label": "labels.autoResize",
        "name": "autoResize",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.unbindText",
        "name": "unbindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.bindText",
        "name": "bindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.createContainerFromText",
        "name": "wrapTextInContainer",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.ungroup",
        "name": "ungroup",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.addToLibrary",
        "name": "addToLibrary",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendBackward",
        "name": "sendBackward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringForward",
        "name": "bringForward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendToBack",
        "name": "sendToBack",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringToFront",
        "name": "bringToFront",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 3l0 18"
            />
            <path
              d="M16 7l0 10l5 0l-5 -10"
            />
            <path
              d="M8 7l0 10l-5 0l5 -10"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipHorizontal",
        "name": "flipHorizontal",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 12l18 0"
            />
            <path
              d="M7 16l10 0l-10 5l0 -5"
            />
            <path
              d="M7 8l10 0l-10 -5l0 5"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipVertical",
        "name": "flipVertical",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "category": "Elements",
        "keywords": [
          "line",
        ],
        "label": [Function],
        "name": "toggleLinearEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M8.333 11.667a2.917 2.917 0 0 0 4.167 0l3.333-3.334a2.946 2.946 0 1 0-4.166-4.166l-.417.416"
            />
            <path
              d="M11.667 8.333a2.917 2.917 0 0 0-4.167 0l-3.333 3.334a2.946 2.946 0 0 0 4.166 4.166l.417-.416"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": [Function],
        "name": "hyperlink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "action": "click",
          "category": "hyperlink",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <React.Fragment>
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"
            />
          </React.Fragment>
        </svg>,
        "label": "labels.copyElementLink",
        "name": "copyElementLink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.duplicateSelection",
        "name": "duplicateSelection",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": [Function],
        "keyTest": [Function],
        "label": [Function],
        "name": "toggleElementLock",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 5.833h13.334M8.333 9.167v5M11.667 9.167v5M4.167 5.833l.833 10c0 .92.746 1.667 1.667 1.667h6.666c.92 0 1.667-.746 1.667-1.667l.833-10M7.5 5.833v-2.5c0-.46.373-.833.833-.833h3.334c.46 0 .833.373.833.833v2.5"
            strokeWidth="1.25"
          />
        </svg>,
        "keyTest": [Function],
        "label": "labels.delete",
        "name": "deleteSelectedElements",
        "perform": [Function],
        "trackEvent": {
          "action": "delete",
          "category": "element",
        },
      },
    ],
    "left": 30,
    "top": 40,
  },
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id1": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "g1": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "red",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "g1",
  ],
  "height": 100,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": 1278240551,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "red",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "g1",
  ],
  "height": 100,
  "id": "id1",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": 449462985,
  "width": 100,
  "x": 0,
  "y": 0,
}
`;

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] number of renders 1`] = `5`;

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > right-clicking on a group should select whole group > [end of test] undo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Add to library' in context menu adds element to library > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": true,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": {
    "message": "Added to library",
  },
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Add to library' in context menu adds element to library > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 2019559783,
  "width": 10,
  "x": -20,
  "y": -10,
}
`;

exports[`contextMenu element > selecting 'Add to library' in context menu adds element to library > [end of test] number of elements 1`] = `1`;

exports[`contextMenu element > selecting 'Add to library' in context menu adds element to library > [end of test] number of renders 1`] = `5`;

exports[`contextMenu element > selecting 'Add to library' in context menu adds element to library > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Add to library' in context menu adds element to library > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -20,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 238820263,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 1505387817,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id0",
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 915032327,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] number of renders 1`] = `10`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Bring forward' in context menu brings element forward > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "index": "a2",
            "version": 4,
          },
          "inserted": {
            "index": "a0",
            "version": 3,
          },
        },
      },
    },
    "id": "id7",
  },
]
`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 238820263,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 1505387817,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id0",
  "index": "a2",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 915032327,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] number of renders 1`] = `10`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Bring to front' in context menu brings element to front > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "index": "a2",
            "version": 4,
          },
          "inserted": {
            "index": "a0",
            "version": 3,
          },
        },
      },
    },
    "id": "id7",
  },
]
`;

exports[`contextMenu element > selecting 'Copy styles' in context menu copies styles > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": true,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": {
    "message": "Copied styles.",
  },
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Copy styles' in context menu copies styles > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 2019559783,
  "width": 10,
  "x": -20,
  "y": -10,
}
`;

exports[`contextMenu element > selecting 'Copy styles' in context menu copies styles > [end of test] number of elements 1`] = `1`;

exports[`contextMenu element > selecting 'Copy styles' in context menu copies styles > [end of test] number of renders 1`] = `5`;

exports[`contextMenu element > selecting 'Copy styles' in context menu copies styles > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Copy styles' in context menu copies styles > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -20,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`contextMenu element > selecting 'Delete' in context menu deletes element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Delete' in context menu deletes element > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": true,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 1014066025,
  "width": 10,
  "x": -20,
  "y": -10,
}
`;

exports[`contextMenu element > selecting 'Delete' in context menu deletes element > [end of test] number of elements 1`] = `1`;

exports[`contextMenu element > selecting 'Delete' in context menu deletes element > [end of test] number of renders 1`] = `6`;

exports[`contextMenu element > selecting 'Delete' in context menu deletes element > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Delete' in context menu deletes element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -20,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {},
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {
        "id0": {
          "deleted": {
            "isDeleted": true,
            "version": 4,
          },
          "inserted": {
            "isDeleted": false,
            "version": 3,
          },
        },
      },
      "removed": {},
      "updated": {},
    },
    "id": "id4",
  },
]
`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 2019559783,
  "width": 10,
  "x": -20,
  "y": -10,
}
`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 238820263,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 5,
  "versionNonce": 1604849351,
  "width": 10,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] number of renders 1`] = `6`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Duplicate' in context menu duplicates element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -20,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 5,
            "width": 10,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 4,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
]
`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id9": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "id9",
  ],
  "height": 20,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 81784553,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "id9",
  ],
  "height": 20,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 238820263,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 747212839,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] number of renders 1`] = `10`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Group selection' in context menu groups selected elements > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id9": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id9",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id9",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id11",
  },
]
`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "#a5d8ff",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "cross-hatch",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 60,
  "currentItemRoughness": 2,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#e03131",
  "currentItemStrokeStyle": "dotted",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": {
    "message": "Copied styles.",
  },
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "#a5d8ff",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "cross-hatch",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 60,
  "roughness": 2,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#e03131",
  "strokeStyle": "dotted",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 1359939303,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "#a5d8ff",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "cross-hatch",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 60,
  "roughness": 2,
  "roundness": null,
  "seed": 640725609,
  "strokeColor": "#e03131",
  "strokeStyle": "dotted",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 9,
  "versionNonce": 908564423,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] number of renders 1`] = `16`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Paste styles' in context menu pastes styles > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "strokeColor": "#e03131",
            "version": 4,
          },
          "inserted": {
            "strokeColor": "#1e1e1e",
            "version": 3,
          },
        },
      },
    },
    "id": "id7",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "backgroundColor": "#a5d8ff",
            "version": 5,
          },
          "inserted": {
            "backgroundColor": "transparent",
            "version": 4,
          },
        },
      },
    },
    "id": "id9",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "fillStyle": "cross-hatch",
            "version": 6,
          },
          "inserted": {
            "fillStyle": "solid",
            "version": 5,
          },
        },
      },
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "strokeStyle": "dotted",
            "version": 7,
          },
          "inserted": {
            "strokeStyle": "solid",
            "version": 6,
          },
        },
      },
    },
    "id": "id13",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "roughness": 2,
            "version": 8,
          },
          "inserted": {
            "roughness": 1,
            "version": 7,
          },
        },
      },
    },
    "id": "id15",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "opacity": 60,
            "version": 9,
          },
          "inserted": {
            "opacity": 100,
            "version": 8,
          },
        },
      },
    },
    "id": "id17",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "backgroundColor": "#a5d8ff",
            "fillStyle": "cross-hatch",
            "opacity": 60,
            "roughness": 2,
            "strokeColor": "#e03131",
            "strokeStyle": "dotted",
            "version": 4,
          },
          "inserted": {
            "backgroundColor": "transparent",
            "fillStyle": "solid",
            "opacity": 100,
            "roughness": 1,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "version": 3,
          },
        },
      },
    },
    "id": "id19",
  },
]
`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id3",
  "index": "Zz",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1116226695,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 23633383,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 401146281,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] number of renders 1`] = `9`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Send backward' in context menu sends element backward > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "index": "Zz",
            "version": 4,
          },
          "inserted": {
            "index": "a1",
            "version": 3,
          },
        },
      },
    },
    "id": "id7",
  },
]
`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id3",
  "index": "Zz",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 238820263,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 915032327,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 1150084233,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] number of renders 1`] = `9`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Send to back' in context menu sends element to back > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {},
        "inserted": {},
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id3": {
          "deleted": {
            "index": "Zz",
            "version": 4,
          },
          "inserted": {
            "index": "a1",
            "version": 3,
          },
        },
      },
    },
    "id": "id7",
  },
]
`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id3": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 5,
  "versionNonce": 1006504105,
  "width": 20,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 20,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 400692809,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 5,
  "versionNonce": 289600103,
  "width": 20,
  "x": 20,
  "y": 30,
}
`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] number of renders 1`] = `11`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > selecting 'Ungroup selection' in context menu ungroups selected group > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 20,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 20,
            "x": 20,
            "y": 30,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id9": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id9",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id9",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {},
        },
        "inserted": {
          "selectedGroupIds": {
            "id9": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id9",
            ],
            "version": 4,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [],
            "version": 5,
          },
          "inserted": {
            "groupIds": [
              "id9",
            ],
            "version": 4,
          },
        },
      },
    },
    "id": "id13",
  },
]
`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": {
    "items": [
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M9.15 14.85l8.85 -10.85"
            />
            <path
              d="M6 4l8.85 10.85"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.cut",
        "name": "cut",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": undefined,
        "label": "labels.copy",
        "name": "copy",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keyTest": undefined,
        "label": "labels.paste",
        "name": "paste",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.selectAllElementsInFrame",
        "name": "selectAllElementsInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
        },
      },
      {
        "label": "labels.removeAllElementsFromFrame",
        "name": "removeAllElementsFromFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "history",
        },
      },
      {
        "label": "labels.wrapSelectionInFrame",
        "name": "wrapSelectionInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 5v10a1 1 0 0 0 1 1h10"
            />
            <path
              d="M5 8h10a1 1 0 0 1 1 1v10"
            />
          </g>
        </svg>,
        "keywords": [
          "image",
          "crop",
        ],
        "label": "helpDialog.cropStart",
        "name": "cropEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "menu",
        },
        "viewMode": true,
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
            <path
              d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6"
            />
            <path
              d="M11 21v-6l3 6v-6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "png",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsPng",
        "name": "copyAsPng",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M4 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75"
            />
            <path
              d="M10 15l2 6l2 -6"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
          </g>
        </svg>,
        "keywords": [
          "svg",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsSvg",
        "name": "copyAsSvg",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keywords": [
          "text",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyText",
        "name": "copyText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.copyStyles",
        "name": "copyStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.pasteStyles",
        "name": "pasteStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.group",
        "name": "group",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": null,
        "label": "labels.autoResize",
        "name": "autoResize",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.unbindText",
        "name": "unbindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.bindText",
        "name": "bindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.createContainerFromText",
        "name": "wrapTextInContainer",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.ungroup",
        "name": "ungroup",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.addToLibrary",
        "name": "addToLibrary",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendBackward",
        "name": "sendBackward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringForward",
        "name": "bringForward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendToBack",
        "name": "sendToBack",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringToFront",
        "name": "bringToFront",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 3l0 18"
            />
            <path
              d="M16 7l0 10l5 0l-5 -10"
            />
            <path
              d="M8 7l0 10l-5 0l5 -10"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipHorizontal",
        "name": "flipHorizontal",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 12l18 0"
            />
            <path
              d="M7 16l10 0l-10 5l0 -5"
            />
            <path
              d="M7 8l10 0l-10 -5l0 5"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipVertical",
        "name": "flipVertical",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "category": "Elements",
        "keywords": [
          "line",
        ],
        "label": [Function],
        "name": "toggleLinearEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M8.333 11.667a2.917 2.917 0 0 0 4.167 0l3.333-3.334a2.946 2.946 0 1 0-4.166-4.166l-.417.416"
            />
            <path
              d="M11.667 8.333a2.917 2.917 0 0 0-4.167 0l-3.333 3.334a2.946 2.946 0 0 0 4.166 4.166l.417-.416"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": [Function],
        "name": "hyperlink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "action": "click",
          "category": "hyperlink",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <React.Fragment>
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"
            />
          </React.Fragment>
        </svg>,
        "label": "labels.copyElementLink",
        "name": "copyElementLink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.duplicateSelection",
        "name": "duplicateSelection",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": [Function],
        "keyTest": [Function],
        "label": [Function],
        "name": "toggleElementLock",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 5.833h13.334M8.333 9.167v5M11.667 9.167v5M4.167 5.833l.833 10c0 .92.746 1.667 1.667 1.667h6.666c.92 0 1.667-.746 1.667-1.667l.833-10M7.5 5.833v-2.5c0-.46.373-.833.833-.833h3.334c.46 0 .833.373.833.833v2.5"
            strokeWidth="1.25"
          />
        </svg>,
        "keyTest": [Function],
        "label": "labels.delete",
        "name": "deleteSelectedElements",
        "perform": [Function],
        "trackEvent": {
          "action": "delete",
          "category": "element",
        },
      },
    ],
    "left": -17,
    "top": -7,
  },
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 453191,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 1014066025,
  "width": 10,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1505387817,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 915032327,
  "width": 10,
  "x": 12,
  "y": 0,
}
`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] number of renders 1`] = `10`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > shows 'Group selection' in context menu for multiple selected elements > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 12,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
]
`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": {
    "items": [
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M9.15 14.85l8.85 -10.85"
            />
            <path
              d="M6 4l8.85 10.85"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.cut",
        "name": "cut",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": undefined,
        "label": "labels.copy",
        "name": "copy",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keyTest": undefined,
        "label": "labels.paste",
        "name": "paste",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.selectAllElementsInFrame",
        "name": "selectAllElementsInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
        },
      },
      {
        "label": "labels.removeAllElementsFromFrame",
        "name": "removeAllElementsFromFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "history",
        },
      },
      {
        "label": "labels.wrapSelectionInFrame",
        "name": "wrapSelectionInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 5v10a1 1 0 0 0 1 1h10"
            />
            <path
              d="M5 8h10a1 1 0 0 1 1 1v10"
            />
          </g>
        </svg>,
        "keywords": [
          "image",
          "crop",
        ],
        "label": "helpDialog.cropStart",
        "name": "cropEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "menu",
        },
        "viewMode": true,
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
            <path
              d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6"
            />
            <path
              d="M11 21v-6l3 6v-6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "png",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsPng",
        "name": "copyAsPng",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M4 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75"
            />
            <path
              d="M10 15l2 6l2 -6"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
          </g>
        </svg>,
        "keywords": [
          "svg",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsSvg",
        "name": "copyAsSvg",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keywords": [
          "text",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyText",
        "name": "copyText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.copyStyles",
        "name": "copyStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.pasteStyles",
        "name": "pasteStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.group",
        "name": "group",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": null,
        "label": "labels.autoResize",
        "name": "autoResize",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.unbindText",
        "name": "unbindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.bindText",
        "name": "bindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.createContainerFromText",
        "name": "wrapTextInContainer",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.ungroup",
        "name": "ungroup",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.addToLibrary",
        "name": "addToLibrary",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendBackward",
        "name": "sendBackward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringForward",
        "name": "bringForward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendToBack",
        "name": "sendToBack",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringToFront",
        "name": "bringToFront",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 3l0 18"
            />
            <path
              d="M16 7l0 10l5 0l-5 -10"
            />
            <path
              d="M8 7l0 10l-5 0l5 -10"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipHorizontal",
        "name": "flipHorizontal",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 12l18 0"
            />
            <path
              d="M7 16l10 0l-10 5l0 -5"
            />
            <path
              d="M7 8l10 0l-10 -5l0 5"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipVertical",
        "name": "flipVertical",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "category": "Elements",
        "keywords": [
          "line",
        ],
        "label": [Function],
        "name": "toggleLinearEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M8.333 11.667a2.917 2.917 0 0 0 4.167 0l3.333-3.334a2.946 2.946 0 1 0-4.166-4.166l-.417.416"
            />
            <path
              d="M11.667 8.333a2.917 2.917 0 0 0-4.167 0l-3.333 3.334a2.946 2.946 0 0 0 4.166 4.166l.417-.416"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": [Function],
        "name": "hyperlink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "action": "click",
          "category": "hyperlink",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <React.Fragment>
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"
            />
          </React.Fragment>
        </svg>,
        "label": "labels.copyElementLink",
        "name": "copyElementLink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.duplicateSelection",
        "name": "duplicateSelection",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": [Function],
        "keyTest": [Function],
        "label": [Function],
        "name": "toggleElementLock",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 5.833h13.334M8.333 9.167v5M11.667 9.167v5M4.167 5.833l.833 10c0 .92.746 1.667 1.667 1.667h6.666c.92 0 1.667-.746 1.667-1.667l.833-10M7.5 5.833v-2.5c0-.46.373-.833.833-.833h3.334c.46 0 .833.373.833.833v2.5"
            strokeWidth="1.25"
          />
        </svg>,
        "keyTest": [Function],
        "label": "labels.delete",
        "name": "deleteSelectedElements",
        "perform": [Function],
        "trackEvent": {
          "action": "delete",
          "category": "element",
        },
      },
    ],
    "left": -17,
    "top": -7,
  },
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {
    "id0": true,
  },
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
    "id3": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {
    "id12": true,
  },
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "id12",
  ],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 449462985,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 1723083209,
  "width": 10,
  "x": -10,
  "y": 0,
}
`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [
    "id12",
  ],
  "height": 10,
  "id": "id3",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 400692809,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 4,
  "versionNonce": 760410951,
  "width": 10,
  "x": 12,
  "y": 0,
}
`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] number of elements 1`] = `2`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] number of renders 1`] = `11`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > shows 'Ungroup selection' in context menu for group inside selected elements > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -10,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id3": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a1",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": 12,
            "y": 0,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id5",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id8",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id3": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {},
    },
    "id": "id11",
  },
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedGroupIds": {
            "id12": true,
          },
        },
        "inserted": {
          "selectedGroupIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {},
      "updated": {
        "id0": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
        "id3": {
          "deleted": {
            "groupIds": [
              "id12",
            ],
            "version": 4,
          },
          "inserted": {
            "groupIds": [],
            "version": 3,
          },
        },
      },
    },
    "id": "id14",
  },
]
`;

exports[`contextMenu element > shows context menu for canvas > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": {
    "items": [
      {
        "keyTest": undefined,
        "label": "labels.paste",
        "name": "paste",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
            <path
              d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6"
            />
            <path
              d="M11 21v-6l3 6v-6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "png",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsPng",
        "name": "copyAsPng",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M4 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75"
            />
            <path
              d="M10 15l2 6l2 -6"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
          </g>
        </svg>,
        "keywords": [
          "svg",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsSvg",
        "name": "copyAsSvg",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keywords": [
          "text",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyText",
        "name": "copyText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g>
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 8m0 1a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-6a1 1 0 0 1 -1 -1z"
            />
            <path
              d="M12 20v.01"
            />
            <path
              d="M16 20v.01"
            />
            <path
              d="M8 20v.01"
            />
            <path
              d="M4 20v.01"
            />
            <path
              d="M4 16v.01"
            />
            <path
              d="M4 12v.01"
            />
            <path
              d="M4 8v.01"
            />
            <path
              d="M4 4v.01"
            />
            <path
              d="M8 4v.01"
            />
            <path
              d="M12 4v.01"
            />
            <path
              d="M16 4v.01"
            />
            <path
              d="M20 4v.01"
            />
            <path
              d="M20 8v.01"
            />
            <path
              d="M20 12v.01"
            />
            <path
              d="M20 16v.01"
            />
            <path
              d="M20 20v.01"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.selectAll",
        "name": "selectAll",
        "perform": [Function],
        "trackEvent": {
          "category": "canvas",
        },
        "viewMode": false,
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g>
            <path
              d="M13.542 8.542H6.458a2.5 2.5 0 0 0-2.5 2.5v3.75a2.5 2.5 0 0 0 2.5 2.5h7.084a2.5 2.5 0 0 0 2.5-2.5v-3.75a2.5 2.5 0 0 0-2.5-2.5Z"
              stroke="currentColor"
              strokeWidth="1.25"
            />
            <path
              d="M10 13.958a1.042 1.042 0 1 0 0-2.083 1.042 1.042 0 0 0 0 2.083Z"
              stroke="currentColor"
              strokeWidth="1.25"
            />
            <mask
              height={9}
              id="UnlockedIcon"
              maskUnits="userSpaceOnUse"
              style={
                {
                  "maskType": "alpha",
                }
              }
              width={9}
              x={6}
              y={1}
            >
              <path
                d="M6.399 9.561V5.175c0-.93.401-1.823 1.116-2.48a3.981 3.981 0 0 1 2.693-1.028c1.01 0 1.98.37 2.694 1.027.715.658 1.116 1.55 1.116 2.481"
                fill="#fff"
                stroke="none"
              />
            </mask>
            <g
              mask="url(#UnlockedIcon)"
            >
              <path
                d="M5.149 9.561v1.25h2.5v-1.25h-2.5Zm5.06-7.894V.417v1.25Zm2.559 3.508v1.25h2.5v-1.25h-2.5ZM7.648 8.51V5.175h-2.5V8.51h2.5Zm0-3.334c0-.564.243-1.128.713-1.561L6.668 1.775c-.959.883-1.52 2.104-1.52 3.4h2.5Zm.713-1.561a2.732 2.732 0 0 1 1.847-.697v-2.5c-1.31 0-2.585.478-3.54 1.358L8.36 3.614Zm1.847-.697c.71 0 1.374.26 1.847.697l1.694-1.839a5.231 5.231 0 0 0-3.54-1.358v2.5Zm1.847.697c.47.433.713.997.713 1.561h2.5c0-1.296-.56-2.517-1.52-3.4l-1.693 1.839Z"
                fill="currentColor"
                stroke="none"
              />
            </g>
          </g>
        </svg>,
        "label": "labels.elementLock.unlockAll",
        "name": "unlockAllElements",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
        },
        "viewMode": false,
      },
      "separator",
      {
        "checked": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 6h18"
            />
            <path
              d="M3 12h18"
            />
            <path
              d="M3 18h18"
            />
            <path
              d="M6 3v18"
            />
            <path
              d="M12 3v18"
            />
            <path
              d="M18 3v18"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "snap",
        ],
        "label": "labels.toggleGrid",
        "name": "gridMode",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
          "predicate": [Function],
        },
        "viewMode": true,
      },
      {
        "checked": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M4 13v-8a2 2 0 0 1 2 -2h1a2 2 0 0 1 2 2v8a2 2 0 0 0 6 0v-8a2 2 0 0 1 2 -2h1a2 2 0 0 1 2 2v8a8 8 0 0 1 -16 0"
            />
            <path
              d="M4 8l5 0"
            />
            <path
              d="M15 8l4 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "buttons.objectsSnapMode",
        "name": "objectsSnapMode",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
          "predicate": [Function],
        },
        "viewMode": false,
      },
      {
        "checked": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 14c.83 .642 2.077 1.017 3.5 1c1.423 .017 2.67 -.358 3.5 -1c.83 -.642 2.077 -1.017 3.5 -1c1.423 -.017 2.67 .358 3.5 1"
            />
            <path
              d="M8 3a2.4 2.4 0 0 0 -1 2a2.4 2.4 0 0 0 1 2"
            />
            <path
              d="M12 3a2.4 2.4 0 0 0 -1 2a2.4 2.4 0 0 0 1 2"
            />
            <path
              d="M3 10h14v5a6 6 0 0 1 -6 6h-2a6 6 0 0 1 -6 -6v-5z"
            />
            <path
              d="M16.746 16.726a3 3 0 1 0 .252 -5.555"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "buttons.zenMode",
        "name": "zenMode",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
          "predicate": [Function],
        },
        "viewMode": true,
      },
      {
        "checked": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            fill="none"
            stroke="currentColor"
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"
            />
            <path
              d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.viewMode",
        "name": "viewMode",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
          "predicate": [Function],
        },
        "viewMode": true,
      },
      {
        "checked": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3v18"
            />
            <path
              d="M19 21v-18"
            />
            <path
              d="M5 7h14"
            />
            <path
              d="M5 15h14"
            />
            <path
              d="M8 13v4"
            />
            <path
              d="M11 13v4"
            />
            <path
              d="M16 13v4"
            />
            <path
              d="M14 5v4"
            />
            <path
              d="M11 5v4"
            />
            <path
              d="M8 5v4"
            />
            <path
              d="M3 21h18"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "edit",
          "attributes",
          "customize",
        ],
        "label": "stats.fullTitle",
        "name": "stats",
        "perform": [Function],
        "trackEvent": {
          "category": "menu",
        },
        "viewMode": true,
      },
    ],
    "left": -19,
    "top": -9,
  },
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > shows context menu for canvas > [end of test] number of elements 1`] = `0`;

exports[`contextMenu element > shows context menu for canvas > [end of test] number of renders 1`] = `3`;

exports[`contextMenu element > shows context menu for canvas > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > shows context menu for canvas > [end of test] undo stack 1`] = `[]`;

exports[`contextMenu element > shows context menu for element > [end of test] appState 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": {
    "items": [
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M9.15 14.85l8.85 -10.85"
            />
            <path
              d="M6 4l8.85 10.85"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.cut",
        "name": "cut",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": undefined,
        "label": "labels.copy",
        "name": "copy",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keyTest": undefined,
        "label": "labels.paste",
        "name": "paste",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.selectAllElementsInFrame",
        "name": "selectAllElementsInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
        },
      },
      {
        "label": "labels.removeAllElementsFromFrame",
        "name": "removeAllElementsFromFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "history",
        },
      },
      {
        "label": "labels.wrapSelectionInFrame",
        "name": "wrapSelectionInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 5v10a1 1 0 0 0 1 1h10"
            />
            <path
              d="M5 8h10a1 1 0 0 1 1 1v10"
            />
          </g>
        </svg>,
        "keywords": [
          "image",
          "crop",
        ],
        "label": "helpDialog.cropStart",
        "name": "cropEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "menu",
        },
        "viewMode": true,
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
            <path
              d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6"
            />
            <path
              d="M11 21v-6l3 6v-6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "png",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsPng",
        "name": "copyAsPng",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M4 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75"
            />
            <path
              d="M10 15l2 6l2 -6"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
          </g>
        </svg>,
        "keywords": [
          "svg",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsSvg",
        "name": "copyAsSvg",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keywords": [
          "text",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyText",
        "name": "copyText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.copyStyles",
        "name": "copyStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.pasteStyles",
        "name": "pasteStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.group",
        "name": "group",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": null,
        "label": "labels.autoResize",
        "name": "autoResize",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.unbindText",
        "name": "unbindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.bindText",
        "name": "bindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.createContainerFromText",
        "name": "wrapTextInContainer",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.ungroup",
        "name": "ungroup",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.addToLibrary",
        "name": "addToLibrary",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendBackward",
        "name": "sendBackward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringForward",
        "name": "bringForward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendToBack",
        "name": "sendToBack",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringToFront",
        "name": "bringToFront",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 3l0 18"
            />
            <path
              d="M16 7l0 10l5 0l-5 -10"
            />
            <path
              d="M8 7l0 10l-5 0l5 -10"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipHorizontal",
        "name": "flipHorizontal",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 12l18 0"
            />
            <path
              d="M7 16l10 0l-10 5l0 -5"
            />
            <path
              d="M7 8l10 0l-10 -5l0 5"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipVertical",
        "name": "flipVertical",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "category": "Elements",
        "keywords": [
          "line",
        ],
        "label": [Function],
        "name": "toggleLinearEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M8.333 11.667a2.917 2.917 0 0 0 4.167 0l3.333-3.334a2.946 2.946 0 1 0-4.166-4.166l-.417.416"
            />
            <path
              d="M11.667 8.333a2.917 2.917 0 0 0-4.167 0l-3.333 3.334a2.946 2.946 0 0 0 4.166 4.166l.417-.416"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": [Function],
        "name": "hyperlink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "action": "click",
          "category": "hyperlink",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <React.Fragment>
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"
            />
          </React.Fragment>
        </svg>,
        "label": "labels.copyElementLink",
        "name": "copyElementLink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.duplicateSelection",
        "name": "duplicateSelection",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": [Function],
        "keyTest": [Function],
        "label": [Function],
        "name": "toggleElementLock",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 5.833h13.334M8.333 9.167v5M11.667 9.167v5M4.167 5.833l.833 10c0 .92.746 1.667 1.667 1.667h6.666c.92 0 1.667-.746 1.667-1.667l.833-10M7.5 5.833v-2.5c0-.46.373-.833.833-.833h3.334c.46 0 .833.373.833.833v2.5"
            strokeWidth="1.25"
          />
        </svg>,
        "keyTest": [Function],
        "label": "labels.delete",
        "name": "deleteSelectedElements",
        "perform": [Function],
        "trackEvent": {
          "action": "delete",
          "category": "element",
        },
      },
    ],
    "left": -17,
    "top": -7,
  },
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": null,
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": true,
  "searchMatches": null,
  "selectedElementIds": {
    "id0": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > shows context menu for element > [end of test] appState 2`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": {
    "items": [
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"
            />
            <path
              d="M9.15 14.85l8.85 -10.85"
            />
            <path
              d="M6 4l8.85 10.85"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.cut",
        "name": "cut",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": undefined,
        "label": "labels.copy",
        "name": "copy",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keyTest": undefined,
        "label": "labels.paste",
        "name": "paste",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.selectAllElementsInFrame",
        "name": "selectAllElementsInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "canvas",
        },
      },
      {
        "label": "labels.removeAllElementsFromFrame",
        "name": "removeAllElementsFromFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "history",
        },
      },
      {
        "label": "labels.wrapSelectionInFrame",
        "name": "wrapSelectionInFrame",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 5v10a1 1 0 0 0 1 1h10"
            />
            <path
              d="M5 8h10a1 1 0 0 1 1 1v10"
            />
          </g>
        </svg>,
        "keywords": [
          "image",
          "crop",
        ],
        "label": "helpDialog.cropStart",
        "name": "cropEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "menu",
        },
        "viewMode": true,
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
            <path
              d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6"
            />
            <path
              d="M11 21v-6l3 6v-6"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "png",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsPng",
        "name": "copyAsPng",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M14 3v4a1 1 0 0 0 1 1h4"
            />
            <path
              d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4"
            />
            <path
              d="M4 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75"
            />
            <path
              d="M10 15l2 6l2 -6"
            />
            <path
              d="M20 15h-1a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h1v-3"
            />
          </g>
        </svg>,
        "keywords": [
          "svg",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyAsSvg",
        "name": "copyAsSvg",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "keywords": [
          "text",
          "clipboard",
          "copy",
        ],
        "label": "labels.copyText",
        "name": "copyText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.copyStyles",
        "name": "copyStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M19 6h1a2 2 0 0 1 2 2a5 5 0 0 1 -5 5l-5 0v2"
            />
            <path
              d="M10 15m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.pasteStyles",
        "name": "pasteStyles",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.group",
        "name": "group",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": null,
        "label": "labels.autoResize",
        "name": "autoResize",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.unbindText",
        "name": "unbindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.bindText",
        "name": "bindText",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "label": "labels.createContainerFromText",
        "name": "wrapTextInContainer",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": [Function],
        "keyTest": [Function],
        "label": "labels.ungroup",
        "name": "ungroup",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "label": "labels.addToLibrary",
        "name": "addToLibrary",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendBackward",
        "name": "sendBackward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 5l0 14"
            />
            <path
              d="M16 9l-4 -4"
            />
            <path
              d="M8 9l4 -4"
            />
          </g>
        </svg>,
        "keyPriority": 40,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringForward",
        "name": "bringForward",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          style={
            {
              "transform": "rotate(180deg)",
            }
          }
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move down",
          "zindex",
          "layer",
        ],
        "label": "labels.sendToBack",
        "name": "sendToBack",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.50000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 10l0 10"
            />
            <path
              d="M12 10l4 4"
            />
            <path
              d="M12 10l-4 4"
            />
            <path
              d="M4 4l16 0"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "keywords": [
          "move up",
          "zindex",
          "layer",
        ],
        "label": "labels.bringToFront",
        "name": "bringToFront",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M12 3l0 18"
            />
            <path
              d="M16 7l0 10l5 0l-5 -10"
            />
            <path
              d="M8 7l0 10l-5 0l5 -10"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipHorizontal",
        "name": "flipHorizontal",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <g
            strokeWidth={"1.25000"}
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M3 12l18 0"
            />
            <path
              d="M7 16l10 0l-10 5l0 -5"
            />
            <path
              d="M7 8l10 0l-10 -5l0 5"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.flipVertical",
        "name": "flipVertical",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "category": "Elements",
        "keywords": [
          "line",
        ],
        "label": [Function],
        "name": "toggleLinearEditor",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M8.333 11.667a2.917 2.917 0 0 0 4.167 0l3.333-3.334a2.946 2.946 0 1 0-4.166-4.166l-.417.416"
            />
            <path
              d="M11.667 8.333a2.917 2.917 0 0 0-4.167 0l-3.333 3.334a2.946 2.946 0 0 0 4.166 4.166l.417-.416"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": [Function],
        "name": "hyperlink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "action": "click",
          "category": "hyperlink",
        },
      },
      {
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          viewBox="0 0 24 24"
        >
          <React.Fragment>
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"
            />
            <path
              d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"
            />
          </React.Fragment>
        </svg>,
        "label": "labels.copyElementLink",
        "name": "copyElementLink",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <g
            strokeWidth="1.25"
          >
            <path
              d="M14.375 6.458H8.958a2.5 2.5 0 0 0-2.5 2.5v5.417a2.5 2.5 0 0 0 2.5 2.5h5.417a2.5 2.5 0 0 0 2.5-2.5V8.958a2.5 2.5 0 0 0-2.5-2.5Z"
            />
            <path
              clipRule="evenodd"
              d="M11.667 3.125c.517 0 .986.21 1.325.55.34.338.55.807.55 1.325v1.458H8.333c-.485 0-.927.185-1.26.487-.343.312-.57.75-.609 1.24l-.005 5.357H5a1.87 1.87 0 0 1-1.326-.55 1.87 1.87 0 0 1-.549-1.325V5c0-.518.21-.987.55-1.326.338-.34.807-.549 1.325-.549h6.667Z"
            />
          </g>
        </svg>,
        "keyTest": [Function],
        "label": "labels.duplicateSelection",
        "name": "duplicateSelection",
        "perform": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      {
        "icon": [Function],
        "keyTest": [Function],
        "label": [Function],
        "name": "toggleElementLock",
        "perform": [Function],
        "predicate": [Function],
        "trackEvent": {
          "category": "element",
        },
      },
      "separator",
      {
        "PanelComponent": [Function],
        "icon": <svg
          aria-hidden="true"
          className=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 5.833h13.334M8.333 9.167v5M11.667 9.167v5M4.167 5.833l.833 10c0 .92.746 1.667 1.667 1.667h6.666c.92 0 1.667-.746 1.667-1.667l.833-10M7.5 5.833v-2.5c0-.46.373-.833.833-.833h3.334c.46 0 .833.373.833.833v2.5"
            strokeWidth="1.25"
          />
        </svg>,
        "keyTest": [Function],
        "label": "labels.delete",
        "name": "deleteSelectedElements",
        "perform": [Function],
        "trackEvent": {
          "action": "delete",
          "category": "element",
        },
      },
    ],
    "left": 80,
    "top": 90,
  },
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "height": 100,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "Untitled-201933152653",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "offsetLeft": 20,
  "offsetTop": 10,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {
    "id1": true,
  },
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": true,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "width": 200,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;

exports[`contextMenu element > shows context menu for element > [end of test] element 0 1`] = `
{
  "angle": 0,
  "backgroundColor": "transparent",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 10,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1278240551,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 3,
  "versionNonce": 2019559783,
  "width": 10,
  "x": -20,
  "y": -10,
}
`;

exports[`contextMenu element > shows context menu for element > [end of test] element 0 2`] = `
{
  "angle": 0,
  "backgroundColor": "red",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 200,
  "id": "id0",
  "index": "a0",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": 1278240551,
  "width": 200,
  "x": 0,
  "y": 0,
}
`;

exports[`contextMenu element > shows context menu for element > [end of test] element 1 1`] = `
{
  "angle": 0,
  "backgroundColor": "red",
  "boundElements": null,
  "customData": undefined,
  "fillStyle": "solid",
  "frameId": null,
  "groupIds": [],
  "height": 200,
  "id": "id1",
  "index": "a1",
  "isDeleted": false,
  "link": null,
  "locked": false,
  "opacity": 100,
  "roughness": 1,
  "roundness": null,
  "seed": 1,
  "strokeColor": "#1e1e1e",
  "strokeStyle": "solid",
  "strokeWidth": 2,
  "type": "rectangle",
  "updated": 1,
  "version": 2,
  "versionNonce": 449462985,
  "width": 200,
  "x": 0,
  "y": 0,
}
`;

exports[`contextMenu element > shows context menu for element > [end of test] number of elements 1`] = `1`;

exports[`contextMenu element > shows context menu for element > [end of test] number of elements 2`] = `2`;

exports[`contextMenu element > shows context menu for element > [end of test] number of renders 1`] = `5`;

exports[`contextMenu element > shows context menu for element > [end of test] number of renders 2`] = `6`;

exports[`contextMenu element > shows context menu for element > [end of test] redo stack 1`] = `[]`;

exports[`contextMenu element > shows context menu for element > [end of test] redo stack 2`] = `[]`;

exports[`contextMenu element > shows context menu for element > [end of test] undo stack 1`] = `
[
  {
    "appState": AppStateDelta {
      "delta": Delta {
        "deleted": {
          "selectedElementIds": {
            "id0": true,
          },
        },
        "inserted": {
          "selectedElementIds": {},
        },
      },
    },
    "elements": {
      "added": {},
      "removed": {
        "id0": {
          "deleted": {
            "angle": 0,
            "backgroundColor": "transparent",
            "boundElements": null,
            "customData": undefined,
            "fillStyle": "solid",
            "frameId": null,
            "groupIds": [],
            "height": 10,
            "index": "a0",
            "isDeleted": false,
            "link": null,
            "locked": false,
            "opacity": 100,
            "roughness": 1,
            "roundness": null,
            "strokeColor": "#1e1e1e",
            "strokeStyle": "solid",
            "strokeWidth": 2,
            "type": "rectangle",
            "version": 3,
            "width": 10,
            "x": -20,
            "y": -10,
          },
          "inserted": {
            "isDeleted": true,
            "version": 2,
          },
        },
      },
      "updated": {},
    },
    "id": "id2",
  },
]
`;

exports[`contextMenu element > shows context menu for element > [end of test] undo stack 2`] = `[]`;
