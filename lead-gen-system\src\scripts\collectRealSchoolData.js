#!/usr/bin/env node

import { apolloWorkingService } from '../services/apolloWorkingService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

class RealSchoolDataCollector {
  constructor() {
    this.outputDir = './data';
    this.webDir = './web';
    this.collectedSchools = [];
    this.rateLimitHit = false;
    this.totalApiCalls = 0;
    this.maxApiCalls = 100; // Conservative limit to avoid hitting rate limits
  }

  async collectAllSchoolData() {
    logger.info('🏫 Starting comprehensive UK school data collection...');
    
    try {
      // Ensure directories exist
      await this.ensureDirectories();
      
      // Test connection first
      const connectionTest = await apolloWorkingService.testConnection();
      if (!connectionTest.success) {
        throw new Error('Apollo API connection failed');
      }
      
      logger.info('✅ Apollo API connected successfully');
      
      // Step 1: Collect known UK school domains
      const knownSchools = await this.getKnownUKSchools();
      logger.info(`📋 Found ${knownSchools.length} known UK schools to process`);
      
      // Step 2: Enrich school data with Apollo
      const enrichedSchools = await this.enrichSchoolsWithApollo(knownSchools);
      
      // Step 3: Search Apollo CRM for education-related accounts
      const crmAccounts = await this.searchEducationAccounts();
      
      // Step 4: Search Apollo CRM for education-related contacts
      const crmContacts = await this.searchEducationContacts();
      
      // Step 5: Generate comprehensive email suggestions
      const emailSuggestions = await this.generateAllEmailSuggestions(enrichedSchools);
      
      // Step 6: Compile final dataset
      const finalDataset = await this.compileFinalDataset(enrichedSchools, crmAccounts, crmContacts, emailSuggestions);
      
      // Step 7: Save data for web interface
      await this.saveWebData(finalDataset);
      
      logger.info('✅ School data collection completed successfully!');
      return finalDataset;
      
    } catch (error) {
      logger.error('❌ School data collection failed:', error);
      throw error;
    }
  }

  async ensureDirectories() {
    const dirs = [this.outputDir, this.webDir, `${this.webDir}/data`];
    for (const dir of dirs) {
      await fs.mkdir(dir, { recursive: true });
    }
  }

  async getKnownUKSchools() {
    // Real UK Multi Academy Trusts and major schools with known domains
    return [
      // Major MATs
      { name: 'Harris Federation', domain: 'harrisacademies.org.uk', type: 'MAT', region: 'London' },
      { name: 'Ark Schools', domain: 'arkschools.org', type: 'MAT', region: 'National' },
      { name: 'United Learning', domain: 'unitedlearning.org.uk', type: 'MAT', region: 'National' },
      { name: 'Oasis Community Learning', domain: 'oasisacademies.org', type: 'MAT', region: 'National' },
      { name: 'Star Academies', domain: 'staracademies.org', type: 'MAT', region: 'National' },
      { name: 'Academies Enterprise Trust', domain: 'academiesenterprisetrust.org', type: 'MAT', region: 'National' },
      { name: 'E-ACT', domain: 'e-act.org.uk', type: 'MAT', region: 'National' },
      { name: 'Outwood Grange Academies Trust', domain: 'outwood.com', type: 'MAT', region: 'Yorkshire' },
      { name: 'Ormiston Academies Trust', domain: 'ormistonacademies.co.uk', type: 'MAT', region: 'National' },
      { name: 'Delta Academies Trust', domain: 'deltatrust.org.uk', type: 'MAT', region: 'Yorkshire' },
      
      // Major Independent Schools
      { name: 'Eton College', domain: 'etoncollege.org.uk', type: 'Independent', region: 'Berkshire' },
      { name: 'Harrow School', domain: 'harrowschool.org.uk', type: 'Independent', region: 'London' },
      { name: 'Westminster School', domain: 'westminster.org.uk', type: 'Independent', region: 'London' },
      { name: 'Winchester College', domain: 'winchestercollege.org', type: 'Independent', region: 'Hampshire' },
      { name: 'Charterhouse School', domain: 'charterhouse.org.uk', type: 'Independent', region: 'Surrey' },

      // Additional MATs and Schools
      { name: 'Cognita Schools', domain: 'cognitaschools.co.uk', type: 'Independent Group', region: 'National' },
      { name: 'GEMS Education', domain: 'gemseducation.com', type: 'Independent Group', region: 'National' },
      { name: 'Dukes Education', domain: 'dukeseducation.com', type: 'Independent Group', region: 'National' },
      { name: 'Bellevue Education', domain: 'bellevueeducation.com', type: 'Independent Group', region: 'National' },
      { name: 'Bright Futures Educational Trust', domain: 'bfet.co.uk', type: 'MAT', region: 'Manchester' },
      { name: 'Cabot Learning Federation', domain: 'cabotlearningfederation.org.uk', type: 'MAT', region: 'Bristol' },
      { name: 'Creative Education Trust', domain: 'creativeeducationtrust.org.uk', type: 'MAT', region: 'National' },
      { name: 'Greenwood Academies Trust', domain: 'greenwoodacademies.org', type: 'MAT', region: 'Midlands' },
      { name: 'Inspiration Trust', domain: 'inspirationtrust.org', type: 'MAT', region: 'Norfolk' },
      { name: 'Kemnal Academies Trust', domain: 'kemnal.co.uk', type: 'MAT', region: 'London' },
      
      // Major State Schools
      { name: 'King Edward VI School Birmingham', domain: 'kes.org.uk', type: 'Grammar', region: 'Birmingham' },
      { name: 'Reading School', domain: 'reading-school.co.uk', type: 'Grammar', region: 'Berkshire' },
      { name: 'Tiffin School', domain: 'tiffinschool.co.uk', type: 'Grammar', region: 'Kingston' },
      
      // University Technical Colleges
      { name: 'JCB Academy', domain: 'jcbacademy.com', type: 'UTC', region: 'Staffordshire' },
      { name: 'UTC Sheffield', domain: 'utcsheffield.org.uk', type: 'UTC', region: 'Sheffield' },
      
      // Sixth Form Colleges
      { name: 'Peter Symonds College', domain: 'psc.ac.uk', type: 'Sixth Form', region: 'Winchester' },
      { name: 'Farnborough Sixth Form College', domain: 'farnborough.ac.uk', type: 'Sixth Form', region: 'Hampshire' },
      
      // Special Schools
      { name: 'Treloar School', domain: 'treloar.org.uk', type: 'Special', region: 'Hampshire' },
      { name: 'National Star College', domain: 'nationalstar.org', type: 'Special', region: 'Gloucestershire' },
      
      // International Schools in UK
      { name: 'American School in London', domain: 'asl.org', type: 'International', region: 'London' },
      { name: 'International School of London', domain: 'islondon.org', type: 'International', region: 'London' }
    ];
  }

  async enrichSchoolsWithApollo(schools) {
    logger.info(`🔍 Enriching ${schools.length} schools with Apollo data...`);
    
    const enrichedSchools = [];
    let successCount = 0;
    let errorCount = 0;
    
    for (const school of schools) {
      if (this.totalApiCalls >= this.maxApiCalls) {
        logger.warn('⚠️ Approaching API rate limit, stopping enrichment');
        this.rateLimitHit = true;
        break;
      }
      
      try {
        logger.info(`   Processing: ${school.name} (${school.domain})`);
        
        const enrichment = await apolloWorkingService.enrichOrganization(school.domain);
        this.totalApiCalls++;
        
        const enrichedSchool = {
          ...school,
          apollo_data: enrichment.success ? enrichment.organization : null,
          apollo_error: enrichment.success ? null : enrichment.error,
          enriched_at: new Date().toISOString(),
          api_call_count: this.totalApiCalls
        };
        
        if (enrichment.success) {
          successCount++;
          logger.info(`   ✅ Success: ${school.name}`);
        } else {
          errorCount++;
          logger.info(`   ❌ Failed: ${school.name} - ${enrichment.error}`);
        }
        
        enrichedSchools.push(enrichedSchool);
        
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        errorCount++;
        logger.error(`   ❌ Error processing ${school.name}:`, error.message);
        
        enrichedSchools.push({
          ...school,
          apollo_data: null,
          apollo_error: error.message,
          enriched_at: new Date().toISOString(),
          api_call_count: this.totalApiCalls
        });
      }
    }
    
    logger.info(`📊 Enrichment complete: ${successCount} success, ${errorCount} errors`);
    return enrichedSchools;
  }

  async searchEducationAccounts() {
    if (this.totalApiCalls >= this.maxApiCalls) {
      logger.warn('⚠️ Skipping account search due to rate limit');
      return [];
    }
    
    logger.info('🏢 Searching Apollo CRM for education accounts...');
    
    const searchTerms = [
      'academy trust',
      'school',
      'education',
      'college',
      'university'
    ];
    
    const allAccounts = [];
    
    for (const term of searchTerms) {
      if (this.totalApiCalls >= this.maxApiCalls) break;
      
      try {
        const result = await apolloWorkingService.searchAccounts({
          q_keywords: term,
          per_page: 10
        });
        this.totalApiCalls++;
        
        if (result.success) {
          allAccounts.push(...result.accounts);
          logger.info(`   Found ${result.accounts.length} accounts for "${term}"`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        logger.error(`Error searching accounts for "${term}":`, error.message);
      }
    }
    
    // Remove duplicates
    const uniqueAccounts = allAccounts.filter((account, index, self) => 
      index === self.findIndex(a => a.id === account.id)
    );
    
    logger.info(`📊 Found ${uniqueAccounts.length} unique education accounts`);
    return uniqueAccounts;
  }

  async searchEducationContacts() {
    if (this.totalApiCalls >= this.maxApiCalls) {
      logger.warn('⚠️ Skipping contact search due to rate limit');
      return [];
    }
    
    logger.info('👥 Searching Apollo CRM for education contacts...');
    
    const searchTerms = [
      'headteacher',
      'principal',
      'education',
      'school administrator'
    ];
    
    const allContacts = [];
    
    for (const term of searchTerms) {
      if (this.totalApiCalls >= this.maxApiCalls) break;
      
      try {
        const result = await apolloWorkingService.searchContacts({
          q_keywords: term,
          per_page: 10
        });
        this.totalApiCalls++;
        
        if (result.success) {
          allContacts.push(...result.contacts);
          logger.info(`   Found ${result.contacts.length} contacts for "${term}"`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        logger.error(`Error searching contacts for "${term}":`, error.message);
      }
    }
    
    // Remove duplicates
    const uniqueContacts = allContacts.filter((contact, index, self) => 
      index === self.findIndex(c => c.id === contact.id)
    );
    
    logger.info(`📊 Found ${uniqueContacts.length} unique education contacts`);
    return uniqueContacts;
  }

  async generateAllEmailSuggestions(schools) {
    logger.info('📧 Generating email suggestions for all schools...');
    
    const allSuggestions = [];
    
    for (const school of schools) {
      const suggestions = apolloWorkingService.generateEmailSuggestions(school, [
        'headteacher', 'admin', 'it', 'business', 'deputy'
      ]);
      
      allSuggestions.push({
        school: school.name,
        domain: school.domain,
        type: school.type,
        region: school.region,
        suggestions: suggestions,
        total_emails: suggestions.length
      });
    }
    
    const totalEmails = allSuggestions.reduce((sum, school) => sum + school.total_emails, 0);
    logger.info(`📧 Generated ${totalEmails} email suggestions across ${allSuggestions.length} schools`);
    
    return allSuggestions;
  }

  async compileFinalDataset(enrichedSchools, crmAccounts, crmContacts, emailSuggestions) {
    const dataset = {
      collection_info: {
        collected_at: new Date().toISOString(),
        total_api_calls: this.totalApiCalls,
        rate_limit_hit: this.rateLimitHit,
        collection_status: this.rateLimitHit ? 'Stopped due to rate limit' : 'Completed successfully'
      },
      schools: {
        total: enrichedSchools.length,
        enriched: enrichedSchools.filter(s => s.apollo_data).length,
        failed: enrichedSchools.filter(s => s.apollo_error).length,
        data: enrichedSchools
      },
      crm_accounts: {
        total: crmAccounts.length,
        data: crmAccounts
      },
      crm_contacts: {
        total: crmContacts.length,
        data: crmContacts
      },
      email_suggestions: {
        total_schools: emailSuggestions.length,
        total_emails: emailSuggestions.reduce((sum, s) => sum + s.total_emails, 0),
        data: emailSuggestions
      },
      summary: {
        total_schools_processed: enrichedSchools.length,
        total_contacts_found: crmContacts.length,
        total_accounts_found: crmAccounts.length,
        total_email_suggestions: emailSuggestions.reduce((sum, s) => sum + s.total_emails, 0),
        api_calls_used: this.totalApiCalls,
        success_rate: Math.round((enrichedSchools.filter(s => s.apollo_data).length / enrichedSchools.length) * 100)
      }
    };
    
    // Save complete dataset
    await fs.writeFile(
      path.join(this.outputDir, 'complete_school_dataset.json'),
      JSON.stringify(dataset, null, 2)
    );
    
    logger.info('💾 Complete dataset saved to complete_school_dataset.json');
    return dataset;
  }

  async saveWebData(dataset) {
    // Save data in format optimized for web interface
    const webData = {
      lastUpdated: dataset.collection_info.collected_at,
      stats: dataset.summary,
      schools: dataset.schools.data,
      contacts: dataset.crm_contacts.data,
      accounts: dataset.crm_accounts.data,
      emailSuggestions: dataset.email_suggestions.data
    };
    
    await fs.writeFile(
      path.join(this.webDir, 'data', 'school_data.json'),
      JSON.stringify(webData, null, 2)
    );
    
    logger.info('🌐 Web data saved for frontend interface');
  }
}

// Main execution
async function main() {
  const collector = new RealSchoolDataCollector();
  
  try {
    const dataset = await collector.collectAllSchoolData();
    
    logger.info('\n🎉 COLLECTION COMPLETE!');
    logger.info(`📊 Summary:`);
    logger.info(`   Schools processed: ${dataset.summary.total_schools_processed}`);
    logger.info(`   Contacts found: ${dataset.summary.total_contacts_found}`);
    logger.info(`   Accounts found: ${dataset.summary.total_accounts_found}`);
    logger.info(`   Email suggestions: ${dataset.summary.total_email_suggestions}`);
    logger.info(`   API calls used: ${dataset.summary.api_calls_used}`);
    logger.info(`   Success rate: ${dataset.summary.success_rate}%`);
    
  } catch (error) {
    logger.error('❌ Collection failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
