// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<Excalidraw/> > <MainMenu/> > should render main menu with host menu items if passed from host 1`] = `
<div
  class="dropdown-menu"
  data-testid="dropdown-menu"
>
  <div
    class="Island dropdown-menu-container"
    style="--padding: 2; z-index: 2;"
  >
    <button
      class="dropdown-menu-item dropdown-menu-item-base"
    >
      <div
        class="dropdown-menu-item__text"
      >
        Click me
      </div>
    </button>
    <a
      class="dropdown-menu-item dropdown-menu-item-base"
      href="blog.excalidaw.com"
      rel="noopener"
      target="_blank"
    >
      <div
        class="dropdown-menu-item__text"
      >
        Excalidraw blog
      </div>
    </a>
    <div
      class="dropdown-menu-item-base dropdown-menu-item-custom"
    >
      <button
        style="height: 2rem;"
      >
        custom menu item
      </button>
    </div>
    <button
      aria-label="Help"
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="help-menu-item"
      title="Help"
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <g
            stroke-width="1.5"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <circle
              cx="12"
              cy="12"
              r="9"
            />
            <line
              x1="12"
              x2="12"
              y1="17"
              y2="17.01"
            />
            <path
              d="M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4"
            />
          </g>
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Help
      </div>
      <div
        class="dropdown-menu-item__shortcut"
      >
        ?
      </div>
    </button>
  </div>
</div>
`;

exports[`<Excalidraw/> > Test UIOptions prop > Test canvasActions > should render menu with default items when "UIOPtions" is "undefined" 1`] = `
<div
  class="dropdown-menu"
  data-testid="dropdown-menu"
>
  <div
    class="Island dropdown-menu-container"
    style="--padding: 2; z-index: 2;"
  >
    <button
      aria-label="Open"
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="load-button"
      title="Open"
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="m9.257 6.351.183.183H15.819c.34 0 .727.182 1.051.506.323.323.505.708.505 1.05v5.819c0 .316-.183.7-.52 1.035-.337.338-.723.522-1.037.522H4.182c-.352 0-.74-.181-1.058-.5-.318-.318-.499-.705-.499-1.057V5.182c0-.351.181-.736.5-1.054.32-.321.71-.503 1.057-.503H6.53l2.726 2.726Z"
            stroke-width="1.25"
          />
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Open
      </div>
      <div
        class="dropdown-menu-item__shortcut"
      >
        Ctrl+O
      </div>
    </button>
    <button
      aria-label="Save to..."
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="json-export-button"
      title="Save to..."
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 14.167v1.666c0 .92.747 1.667 1.667 1.667h10c.92 0 1.667-.746 1.667-1.667v-1.666M5.833 9.167 10 13.333l4.167-4.166M10 3.333v10"
            stroke-width="1.25"
          />
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Save to...
      </div>
    </button>
    <button
      aria-label="Export image..."
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="image-export-button"
      title="Export image..."
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <g
            stroke-width="1.25"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M15 8h.01"
            />
            <path
              d="M12 20h-5a3 3 0 0 1 -3 -3v-10a3 3 0 0 1 3 -3h10a3 3 0 0 1 3 3v5"
            />
            <path
              d="M4 15l4 -4c.928 -.893 2.072 -.893 3 0l4 4"
            />
            <path
              d="M14 14l1 -1c.617 -.593 1.328 -.793 2.009 -.598"
            />
            <path
              d="M19 16v6"
            />
            <path
              d="M22 19l-3 3l-3 -3"
            />
          </g>
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Export image...
      </div>
      <div
        class="dropdown-menu-item__shortcut"
      >
        Ctrl+Shift+E
      </div>
    </button>
    <button
      aria-label="Find on canvas"
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="search-menu-button"
      title="Find on canvas"
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <g
            stroke-width="1.5"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <path
              d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"
            />
            <path
              d="M21 21l-6 -6"
            />
          </g>
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Find on canvas
      </div>
      <div
        class="dropdown-menu-item__shortcut"
      >
        Ctrl+F
      </div>
    </button>
    <button
      aria-label="Help"
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="help-menu-item"
      title="Help"
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <g
            stroke-width="1.5"
          >
            <path
              d="M0 0h24v24H0z"
              fill="none"
              stroke="none"
            />
            <circle
              cx="12"
              cy="12"
              r="9"
            />
            <line
              x1="12"
              x2="12"
              y1="17"
              y2="17.01"
            />
            <path
              d="M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4"
            />
          </g>
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Help
      </div>
      <div
        class="dropdown-menu-item__shortcut"
      >
        ?
      </div>
    </button>
    <button
      aria-label="Reset the canvas"
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="clear-canvas-button"
      title="Reset the canvas"
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            d="M3.333 5.833h13.334M8.333 9.167v5M11.667 9.167v5M4.167 5.833l.833 10c0 .92.746 1.667 1.667 1.667h6.666c.92 0 1.667-.746 1.667-1.667l.833-10M7.5 5.833v-2.5c0-.46.373-.833.833-.833h3.334c.46 0 .833.373.833.833v2.5"
            stroke-width="1.25"
          />
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Reset the canvas
      </div>
    </button>
    <div
      style="height: 1px; margin: .5rem 0px;"
    />
    <div
      class="dropdown-menu-group "
    >
      <p
        class="dropdown-menu-group-title"
      >
        Excalidraw links
      </p>
      <a
        aria-label="GitHub"
        class="dropdown-menu-item dropdown-menu-item-base"
        href="https://github.com/excalidraw/excalidraw"
        rel="noopener"
        target="_blank"
        title="GitHub"
      >
        <div
          class="dropdown-menu-item__icon"
        >
          <svg
            aria-hidden="true"
            class=""
            fill="none"
            focusable="false"
            role="img"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            viewBox="0 0 20 20"
          >
            <path
              d="M7.5 15.833c-3.583 1.167-3.583-2.083-5-2.5m10 4.167v-2.917c0-.833.083-1.166-.417-1.666 2.334-.25 4.584-1.167 4.584-5a3.833 3.833 0 0 0-1.084-2.667 3.5 3.5 0 0 0-.083-2.667s-.917-.25-2.917 1.084a10.25 10.25 0 0 0-5.166 0C5.417 2.333 4.5 2.583 4.5 2.583a3.5 3.5 0 0 0-.083 2.667 3.833 3.833 0 0 0-1.084 2.667c0 3.833 2.25 4.75 4.584 5-.5.5-.5 1-.417 1.666V17.5"
              stroke-width="1.25"
            />
          </svg>
        </div>
        <div
          class="dropdown-menu-item__text"
        >
          GitHub
        </div>
      </a>
      <a
        aria-label="X"
        class="dropdown-menu-item dropdown-menu-item-base"
        href="https://x.com/excalidraw"
        rel="noopener"
        target="_blank"
        title="X"
      >
        <div
          class="dropdown-menu-item__icon"
        >
          <svg
            aria-hidden="true"
            class=""
            fill="none"
            focusable="false"
            role="img"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <g
              stroke-width="1.25"
            >
              <path
                d="M0 0h24v24H0z"
                fill="none"
                stroke="none"
              />
              <path
                d="M4 4l11.733 16h4.267l-11.733 -16z"
              />
              <path
                d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772"
              />
            </g>
          </svg>
        </div>
        <div
          class="dropdown-menu-item__text"
        >
          Follow us
        </div>
      </a>
      <a
        aria-label="Discord"
        class="dropdown-menu-item dropdown-menu-item-base"
        href="https://discord.gg/UexuTaE"
        rel="noopener"
        target="_blank"
        title="Discord"
      >
        <div
          class="dropdown-menu-item__icon"
        >
          <svg
            aria-hidden="true"
            class=""
            fill="none"
            focusable="false"
            role="img"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            viewBox="0 0 20 20"
          >
            <g
              stroke-width="1.25"
            >
              <path
                d="M7.5 10.833a.833.833 0 1 0 0-1.666.833.833 0 0 0 0 1.666ZM12.5 10.833a.833.833 0 1 0 0-1.666.833.833 0 0 0 0 1.666ZM6.25 6.25c2.917-.833 4.583-.833 7.5 0M5.833 13.75c2.917.833 5.417.833 8.334 0"
              />
              <path
                d="M12.917 14.167c0 .833 1.25 2.5 1.666 2.5 1.25 0 2.361-1.39 2.917-2.5.556-1.39.417-4.861-1.25-9.584-1.214-.846-2.5-1.116-3.75-1.25l-.833 2.084M7.083 14.167c0 .833-1.13 2.5-1.526 2.5-1.191 0-2.249-1.39-2.778-2.5-.529-1.39-.397-4.861 1.19-9.584 1.157-.846 2.318-1.116 3.531-1.25l.833 2.084"
              />
            </g>
          </svg>
        </div>
        <div
          class="dropdown-menu-item__text"
        >
          Discord chat
        </div>
      </a>
    </div>
    <div
      style="height: 1px; margin: .5rem 0px;"
    />
    <button
      aria-label="Dark mode"
      class="dropdown-menu-item dropdown-menu-item-base"
      data-testid="toggle-dark-mode"
      title="Dark mode"
    >
      <div
        class="dropdown-menu-item__icon"
      >
        <svg
          aria-hidden="true"
          class=""
          fill="none"
          focusable="false"
          role="img"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          viewBox="0 0 20 20"
        >
          <path
            clip-rule="evenodd"
            d="M10 2.5h.328a6.25 6.25 0 0 0 6.6 10.372A7.5 7.5 0 1 1 10 2.493V2.5Z"
            stroke="currentColor"
          />
        </svg>
      </div>
      <div
        class="dropdown-menu-item__text"
      >
        Dark mode
      </div>
      <div
        class="dropdown-menu-item__shortcut"
      >
        Shift+Alt+D
      </div>
    </button>
    <div
      style="margin-top: 0.5rem;"
    >
      <div
        data-testid="canvas-background-label"
        style="font-size: .75rem; margin-bottom: .5rem;"
      >
        Canvas background
      </div>
      <div
        style="padding: 0px 0.625rem;"
      >
        <div>
          <div
            aria-modal="true"
            class="color-picker-container"
            role="dialog"
          >
            <div
              class="color-picker__top-picks"
            >
              <button
                class="color-picker__button active has-outline"
                data-testid="color-top-pick-#ffffff"
                style="--swatch-color: #ffffff;"
                title="#ffffff"
                type="button"
              >
                <div
                  class="color-picker__button-outline"
                />
              </button>
              <button
                class="color-picker__button has-outline"
                data-testid="color-top-pick-#f8f9fa"
                style="--swatch-color: #f8f9fa;"
                title="#f8f9fa"
                type="button"
              >
                <div
                  class="color-picker__button-outline"
                />
              </button>
              <button
                class="color-picker__button has-outline"
                data-testid="color-top-pick-#f5faff"
                style="--swatch-color: #f5faff;"
                title="#f5faff"
                type="button"
              >
                <div
                  class="color-picker__button-outline"
                />
              </button>
              <button
                class="color-picker__button has-outline"
                data-testid="color-top-pick-#fffce8"
                style="--swatch-color: #fffce8;"
                title="#fffce8"
                type="button"
              >
                <div
                  class="color-picker__button-outline"
                />
              </button>
              <button
                class="color-picker__button has-outline"
                data-testid="color-top-pick-#fdf8f6"
                style="--swatch-color: #fdf8f6;"
                title="#fdf8f6"
                type="button"
              >
                <div
                  class="color-picker__button-outline"
                />
              </button>
            </div>
            <div
              style="width: 1px; height: 1rem; margin: 0px auto;"
            />
            <button
              aria-controls="radix-:r0:"
              aria-expanded="false"
              aria-haspopup="dialog"
              aria-label="Canvas background"
              class="color-picker__button active-color properties-trigger has-outline"
              data-state="closed"
              style="--swatch-color: #ffffff;"
              title="Show background color picker"
              type="button"
            >
              <div
                class="color-picker__button-outline"
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
