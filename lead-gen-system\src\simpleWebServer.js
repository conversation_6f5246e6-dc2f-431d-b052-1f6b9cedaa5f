#!/usr/bin/env node

import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = 3001;
const webDir = path.join(__dirname, '../web');

console.log('🚀 Starting UK Schools Lead Generation Web Server...');
console.log('📁 Web directory:', webDir);

// Serve static files
app.use(express.static(webDir));

// Parse JSON
app.use(express.json());

// CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  next();
});

// Logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Main route
app.get('/', (req, res) => {
  console.log('📊 Serving dashboard...');
  res.sendFile(path.join(webDir, 'index.html'));
});

// API route for school data
app.get('/api/school-data', async (req, res) => {
  try {
    console.log('📡 API request for school data...');
    const dataPath = path.join(webDir, 'data', 'school_data.json');
    
    try {
      const data = await fs.readFile(dataPath, 'utf8');
      console.log('✅ School data loaded successfully');
      res.json(JSON.parse(data));
    } catch (error) {
      console.log('⚠️ No data file found, returning empty data');
      res.json({
        lastUpdated: null,
        stats: {
          total_schools_processed: 0,
          total_contacts_found: 0,
          total_accounts_found: 0,
          total_email_suggestions: 0,
          api_calls_used: 0,
          success_rate: 0
        },
        schools: [],
        contacts: [],
        accounts: [],
        emailSuggestions: []
      });
    }
  } catch (error) {
    console.error('❌ Error serving school data:', error);
    res.status(500).json({ error: 'Failed to load school data' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'UK Schools Lead Generation Web Server'
  });
});

// 404 handler
app.use('*', (req, res) => {
  console.log(`❌ 404: ${req.originalUrl}`);
  res.status(404).json({ error: 'Endpoint not found' });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('❌ Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(port, () => {
  console.log('🎉 UK Schools Lead Generation Dashboard is ready!');
  console.log(`🌐 Server running on: http://localhost:${port}`);
  console.log(`📊 Dashboard: http://localhost:${port}`);
  console.log(`🔍 Health Check: http://localhost:${port}/health`);
  console.log(`📡 API: http://localhost:${port}/api/school-data`);
  console.log('\n📋 Features available:');
  console.log('   • Real-time school data from Apollo.io');
  console.log('   • Interactive dashboard with filtering');
  console.log('   • Contact and email suggestions');
  console.log('   • Data export functionality');
  console.log('   • Analytics and performance metrics');
  console.log('\n✅ Server is running and ready to serve requests!');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down web server gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Shutting down web server gracefully...');
  process.exit(0);
});

console.log('📝 Web server script loaded, starting Express server...');
