{"private": true, "name": "excalidraw-monorepo", "packageManager": "yarn@1.22.22", "workspaces": ["excalidraw-app", "packages/*", "examples/*"], "devDependencies": {"@babel/preset-env": "7.26.9", "@excalidraw/eslint-config": "1.0.3", "@excalidraw/prettier-config": "1.0.2", "@types/chai": "4.3.0", "@types/jest": "27.4.0", "@types/lodash.throttle": "4.1.7", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/socket.io-client": "3.0.0", "@vitejs/plugin-react": "3.1.0", "@vitest/coverage-v8": "3.0.7", "@vitest/ui": "2.0.5", "chai": "4.3.6", "dotenv": "16.0.1", "eslint-config-prettier": "8.5.0", "eslint-config-react-app": "7.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "3.3.1", "http-server": "14.1.1", "husky": "7.0.4", "jsdom": "22.1.0", "lint-staged": "12.3.7", "pepjs": "0.5.3", "prettier": "2.6.2", "rewire": "6.0.0", "rimraf": "^5.0.0", "typescript": "4.9.4", "vite": "5.0.12", "vite-plugin-checker": "0.7.2", "vite-plugin-ejs": "1.7.0", "vite-plugin-pwa": "0.21.1", "vite-plugin-svgr": "4.2.0", "vitest": "3.0.6", "vitest-canvas-mock": "0.3.3"}, "engines": {"node": "18.0.0 - 22.x.x"}, "homepage": ".", "prettier": "@excalidraw/prettier-config", "scripts": {"build-node": "node ./scripts/build-node.js", "build:app:docker": "yarn --cwd ./excalidraw-app build:app:docker", "build:app": "yarn --cwd ./excalidraw-app build:app", "build:package": "yarn --cwd ./packages/excalidraw build:esm", "build:version": "yarn --cwd ./excalidraw-app build:version", "build": "yarn --cwd ./excalidraw-app build", "build:preview": "yarn --cwd ./excalidraw-app build:preview", "start": "yarn --cwd ./excalidraw-app start", "start:production": "yarn --cwd ./excalidraw-app start:production", "start:example": "yarn build:package && yarn --cwd ./examples/with-script-in-browser start", "test:all": "yarn test:typecheck && yarn test:code && yarn test:other && yarn test:app --watch=false", "test:app": "vitest", "test:code": "eslint --max-warnings=0 --ext .js,.ts,.tsx .", "test:other": "yarn prettier --list-different", "test:typecheck": "tsc", "test:update": "yarn test:app --update --watch=false", "test": "yarn test:app", "test:coverage": "vitest --coverage", "test:coverage:watch": "vitest --coverage --watch", "test:ui": "yarn test --ui --coverage.enabled=true", "fix:code": "yarn test:code --fix", "fix:other": "yarn prettier --write", "fix": "yarn fix:other && yarn fix:code", "locales-coverage": "node scripts/build-locales-coverage.js", "locales-coverage:description": "node scripts/locales-coverage-description.js", "prepare": "husky install", "prettier": "prettier \"**/*.{css,scss,json,md,html,yml}\" --ignore-path=.eslint<PERSON>ore", "autorelease": "node scripts/autorelease.js", "prerelease:excalidraw": "node scripts/prerelease.js", "release:excalidraw": "node scripts/release.js", "rm:build": "rimraf --glob excalidraw-app/build excalidraw-app/dist excalidraw-app/dev-dist packages/*/dist packages/*/build examples/*/build examples/*/dist", "rm:node_modules": "rimraf --glob node_modules excalidraw-app/node_modules packages/*/node_modules", "clean-install": "yarn rm:node_modules && yarn install"}, "resolutions": {"strip-ansi": "6.0.1"}}