// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`exportToSvg > with default arguments 1`] = `
{
  "activeEmbeddable": null,
  "activeLockedId": null,
  "activeTool": {
    "customType": null,
    "fromSelection": false,
    "lastActiveTool": null,
    "locked": false,
    "type": "selection",
  },
  "collaborators": Map {},
  "contextMenu": null,
  "croppingElementId": null,
  "currentChartType": "bar",
  "currentHoveredFontFamily": null,
  "currentItemArrowType": "round",
  "currentItemBackgroundColor": "transparent",
  "currentItemEndArrowhead": "arrow",
  "currentItemFillStyle": "solid",
  "currentItemFontFamily": 5,
  "currentItemFontSize": 20,
  "currentItemOpacity": 100,
  "currentItemRoughness": 1,
  "currentItemRoundness": "sharp",
  "currentItemStartArrowhead": null,
  "currentItemStrokeColor": "#1e1e1e",
  "currentItemStrokeStyle": "solid",
  "currentItemStrokeWidth": 2,
  "currentItemTextAlign": "left",
  "cursorButton": "up",
  "defaultSidebarDockedPreference": false,
  "editingFrame": null,
  "editingGroupId": null,
  "editingLinearElement": null,
  "editingTextElement": null,
  "elementsToHighlight": null,
  "errorMessage": null,
  "exportBackground": true,
  "exportEmbedScene": false,
  "exportPadding": undefined,
  "exportScale": 1,
  "exportWithDarkMode": false,
  "fileHandle": null,
  "followedBy": Set {},
  "frameRendering": {
    "clip": true,
    "enabled": true,
    "name": true,
    "outline": true,
  },
  "frameToHighlight": null,
  "gridModeEnabled": false,
  "gridSize": 20,
  "gridStep": 5,
  "hoveredElementIds": {},
  "isBindingEnabled": true,
  "isCropping": false,
  "isLoading": false,
  "isResizing": false,
  "isRotating": false,
  "lastPointerDownWith": "mouse",
  "lockedMultiSelections": {},
  "multiElement": null,
  "name": "name",
  "newElement": null,
  "objectsSnapModeEnabled": false,
  "openDialog": null,
  "openMenu": null,
  "openPopup": null,
  "openSidebar": null,
  "originSnapOffset": {
    "x": 0,
    "y": 0,
  },
  "pasteDialog": {
    "data": null,
    "shown": false,
  },
  "penDetected": false,
  "penMode": false,
  "previousSelectedElementIds": {},
  "resizingElement": null,
  "scrollX": 0,
  "scrollY": 0,
  "scrolledOutside": false,
  "searchMatches": null,
  "selectedElementIds": {},
  "selectedElementsAreBeingDragged": false,
  "selectedGroupIds": {},
  "selectedLinearElement": null,
  "selectionElement": null,
  "shouldCacheIgnoreZoom": false,
  "showHyperlinkPopup": false,
  "showWelcomeScreen": false,
  "snapLines": [],
  "startBoundElement": null,
  "stats": {
    "open": false,
    "panels": 3,
  },
  "suggestedBindings": [],
  "theme": "light",
  "toast": null,
  "userToFollow": null,
  "viewBackgroundColor": "#ffffff",
  "viewModeEnabled": false,
  "zenModeEnabled": false,
  "zoom": {
    "value": 1,
  },
}
`;
