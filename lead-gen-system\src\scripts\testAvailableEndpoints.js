#!/usr/bin/env node

import { config } from '../config/index.js';
import axios from 'axios';
import fs from 'fs/promises';

async function testAvailableEndpoints() {
  console.log('🧪 Testing Available Apollo.io Endpoints...\n');
  
  const client = axios.create({
    baseURL: 'https://api.apollo.io/v1',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'x-api-key': config.apollo.apiKey
    }
  });

  const results = {
    api_key: config.apollo.apiKey,
    test_date: new Date().toISOString(),
    available_endpoints: [],
    unavailable_endpoints: [],
    working_features: []
  };

  // Test various endpoints
  const endpointsToTest = [
    // Basic endpoints
    { name: 'Health Check', method: 'GET', path: '/auth/health', data: null },
    
    // Search endpoints
    { name: 'People Search', method: 'POST', path: '/mixed_people/search', data: { q_keywords: 'test', per_page: 1 } },
    { name: 'People Search Alt', method: 'POST', path: '/people/search', data: { q_keywords: 'test', per_page: 1 } },
    { name: 'Organization Search', method: 'POST', path: '/organizations/search', data: { q_organization_name: 'test', per_page: 1 } },
    
    // Enrichment endpoints
    { name: 'People Enrichment', method: 'POST', path: '/people/enrich', data: { email: '<EMAIL>' } },
    { name: 'Organization Enrichment', method: 'GET', path: '/organizations/enrich', data: { domain: 'example.com' } },
    
    // Email endpoints
    { name: 'Email Finder', method: 'GET', path: '/email_finder', data: { domain: 'example.com' } },
    { name: 'Email Verifier', method: 'GET', path: '/email_verifier', data: { email: '<EMAIL>' } },
    
    // Account management
    { name: 'Usage Stats', method: 'POST', path: '/usage_stats', data: {} },
    { name: 'Users List', method: 'GET', path: '/users', data: null },
    
    // CRM endpoints
    { name: 'Contacts Search', method: 'POST', path: '/contacts/search', data: { q_keywords: 'test' } },
    { name: 'Accounts Search', method: 'POST', path: '/accounts/search', data: { q_keywords: 'test' } }
  ];

  for (const endpoint of endpointsToTest) {
    try {
      console.log(`📡 Testing: ${endpoint.name} (${endpoint.method} ${endpoint.path})`);
      
      let response;
      if (endpoint.method === 'GET') {
        response = await client.get(endpoint.path, endpoint.data ? { params: endpoint.data } : {});
      } else {
        response = await client.post(endpoint.path, endpoint.data || {});
      }
      
      console.log(`✅ SUCCESS: ${endpoint.name}`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Credits: ${response.headers['x-daily-requests-left'] || 'Unknown'}`);
      console.log(`   Rate Limit: ${response.headers['x-hourly-requests-left'] || 'Unknown'}`);
      
      if (response.data) {
        const keys = Object.keys(response.data);
        console.log(`   Response Keys: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
        
        // Check for specific data types
        if (response.data.people && response.data.people.length > 0) {
          console.log(`   📊 Found ${response.data.people.length} people`);
          results.working_features.push(`${endpoint.name} - People Data Available`);
        }
        if (response.data.organizations && response.data.organizations.length > 0) {
          console.log(`   🏢 Found ${response.data.organizations.length} organizations`);
          results.working_features.push(`${endpoint.name} - Organization Data Available`);
        }
        if (response.data.pagination) {
          console.log(`   📄 Pagination: ${response.data.pagination.total_entries || 0} total entries`);
        }
      }
      
      results.available_endpoints.push({
        name: endpoint.name,
        method: endpoint.method,
        path: endpoint.path,
        status: response.status,
        credits_remaining: response.headers['x-daily-requests-left'],
        rate_limit: response.headers['x-hourly-requests-left']
      });
      
    } catch (error) {
      const status = error.response?.status || 'Network Error';
      const message = error.response?.data?.error || error.response?.data?.message || error.message;
      
      console.log(`❌ FAILED: ${endpoint.name} - ${status}: ${message}`);
      
      results.unavailable_endpoints.push({
        name: endpoint.name,
        method: endpoint.method,
        path: endpoint.path,
        error_status: status,
        error_message: message
      });
    }
    
    console.log('');
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Test specific UK education searches if any search endpoint works
  const workingSearchEndpoint = results.available_endpoints.find(ep => 
    ep.name.includes('Search') && (ep.name.includes('People') || ep.name.includes('Organization'))
  );

  if (workingSearchEndpoint) {
    console.log(`🎓 Testing UK Education Search with working endpoint: ${workingSearchEndpoint.name}`);
    
    try {
      let searchData;
      if (workingSearchEndpoint.name.includes('People')) {
        searchData = {
          q_keywords: 'headteacher OR principal',
          person_locations: ['United Kingdom'],
          organization_industry_tag_ids: ['5567cd4e73696439b10b0000'], // Education
          per_page: 5
        };
      } else {
        searchData = {
          q_organization_name: 'Academy Trust',
          organization_locations: ['United Kingdom'],
          organization_industry_tag_ids: ['5567cd4e73696439b10b0000'], // Education
          per_page: 5
        };
      }
      
      const response = await client.post(workingSearchEndpoint.path, searchData);
      
      console.log(`✅ UK Education search successful!`);
      console.log(`   Results: ${response.data.pagination?.total_entries || response.data.people?.length || response.data.organizations?.length || 0}`);
      
      if (response.data.people && response.data.people.length > 0) {
        const sample = response.data.people[0];
        console.log(`   Sample Person: ${sample.name || 'N/A'} - ${sample.title || 'N/A'} at ${sample.organization?.name || 'N/A'}`);
        console.log(`   Contact Data: Email ${sample.email ? '✓' : '✗'} | Phone ${sample.sanitized_phone ? '✓' : '✗'} | LinkedIn ${sample.linkedin_url ? '✓' : '✗'}`);
      }
      
      if (response.data.organizations && response.data.organizations.length > 0) {
        const sample = response.data.organizations[0];
        console.log(`   Sample Organization: ${sample.name || 'N/A'} - ${sample.domain || 'N/A'}`);
      }
      
      results.working_features.push('UK Education Search - Working');
      
      // Save sample data
      await fs.writeFile('./data/apollo_uk_education_sample.json', JSON.stringify(response.data, null, 2));
      console.log('   💾 Sample data saved to ./data/apollo_uk_education_sample.json');
      
    } catch (error) {
      console.log(`❌ UK Education search failed: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
    }
  }

  // Save complete results
  await fs.writeFile('./data/apollo_endpoint_test_results.json', JSON.stringify(results, null, 2));
  
  console.log('\n📊 SUMMARY:');
  console.log(`✅ Available Endpoints: ${results.available_endpoints.length}`);
  console.log(`❌ Unavailable Endpoints: ${results.unavailable_endpoints.length}`);
  console.log(`🎯 Working Features: ${results.working_features.length}`);
  
  console.log('\n✅ Available Endpoints:');
  results.available_endpoints.forEach(ep => {
    console.log(`   • ${ep.name} (${ep.method} ${ep.path})`);
  });
  
  if (results.working_features.length > 0) {
    console.log('\n🎯 Working Features:');
    results.working_features.forEach(feature => {
      console.log(`   • ${feature}`);
    });
  }
  
  console.log('\n💾 Complete results saved to ./data/apollo_endpoint_test_results.json');
  
  return results;
}

// Run the test
testAvailableEndpoints().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
