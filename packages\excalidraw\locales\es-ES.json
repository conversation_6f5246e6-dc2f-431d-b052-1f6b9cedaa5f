{"labels": {"paste": "<PERSON><PERSON><PERSON>", "pasteAsPlaintext": "Pegar como texto sin formato", "pasteCharts": "<PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "multiSelect": "Añadir elemento a la selección", "moveCanvas": "Mover el lienzo", "cut": "Cortar", "copy": "Copiar", "copyAsPng": "Copiar al portapapeles como PNG", "copyAsSvg": "Copiar al portapapeles como SVG", "copyText": "Copiar al portapapeles como texto", "copySource": "Copiar fuente al portapapeles", "convertToCode": "Convertir a código", "bringForward": "<PERSON><PERSON><PERSON> hacia delante", "sendToBack": "Enviar al fondo", "bringToFront": "Traer al frente", "sendBackward": "Enviar atrás", "delete": "Bo<PERSON>r", "copyStyles": "<PERSON><PERSON><PERSON> estilo<PERSON>", "pasteStyles": "<PERSON><PERSON><PERSON> estilo<PERSON>", "stroke": "Trazo", "background": "Fondo", "fill": "<PERSON><PERSON><PERSON>", "strokeWidth": "Grosor del trazo", "strokeStyle": "Estilo del trazo", "strokeStyle_solid": "<PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dashed": "Discontinua", "strokeStyle_dotted": "Punteado", "sloppiness": "Estilo de t<PERSON>o", "opacity": "Opacidad", "textAlign": "Alineado de texto", "edges": "<PERSON><PERSON>", "sharp": "<PERSON><PERSON><PERSON><PERSON>", "round": "Redondo", "arrowheads": "Puntas de flecha", "arrowhead_none": "Ninguna", "arrowhead_arrow": "Fle<PERSON>", "arrowhead_bar": "Barr<PERSON>", "arrowhead_circle": "<PERSON><PERSON><PERSON><PERSON>", "arrowhead_circle_outline": "<PERSON><PERSON><PERSON><PERSON> (contorno)", "arrowhead_triangle": "<PERSON><PERSON><PERSON><PERSON>", "arrowhead_triangle_outline": "Trián<PERSON><PERSON> (contorno)", "arrowhead_diamond": "Diamante", "arrowhead_diamond_outline": "<PERSON><PERSON><PERSON> (contorno)", "fontSize": "Tamaño de la fuente", "fontFamily": "Tipo de fuente", "addWatermark": "Agregar \"<PERSON><PERSON> con Excalidraw\"", "handDrawn": "<PERSON><PERSON><PERSON><PERSON> a mano", "normal": "Normal", "code": "Código", "small": "Pequeña", "medium": "Mediana", "large": "Grande", "veryLarge": "<PERSON><PERSON> grande", "solid": "<PERSON><PERSON><PERSON><PERSON>", "hachure": "<PERSON><PERSON><PERSON>", "zigzag": "Zigzag", "crossHatch": "Rayado transversal", "thin": "Fino", "bold": "<PERSON><PERSON><PERSON>", "left": "Iz<PERSON>erda", "center": "Centrado", "right": "Derecha", "extraBold": "Extra negrita", "architect": "Arquitecto", "artist": "Artista", "cartoonist": "Caricatura", "fileTitle": "Nombre del archivo", "colorPicker": "Selector de color", "canvasColors": "Usado en lienzo", "canvasBackground": "Fondo del lienzo", "drawingCanvas": "<PERSON><PERSON><PERSON> di<PERSON>", "layers": "Capas", "actions": "Acciones", "language": "Idioma", "liveCollaboration": "Colaboración en directo...", "duplicateSelection": "Duplicar", "untitled": "Sin título", "name": "Nombre", "yourName": "Tu nombre", "madeWithExcalidraw": "<PERSON><PERSON> con <PERSON>", "group": "Agrupar se<PERSON>", "ungroup": "Desagrupar <PERSON>", "collaborators": "Colaboradores", "showGrid": "Mostrar cuadr<PERSON>cula", "addToLibrary": "Añadir a la biblioteca", "removeFromLibrary": "Eliminar de la biblioteca", "libraryLoadingMessage": "Cargando biblioteca…", "libraries": "Explorar bibliotecas", "loadingScene": "Cargando escena…", "align": "Alinear", "alignTop": "Alineación superior", "alignBottom": "Alineación inferior", "alignLeft": "Alinear a la izquierda", "alignRight": "Alinear a la derecha", "centerVertically": "Centrar verticalmente", "centerHorizontally": "Centrar horizontalmente", "distributeHorizontally": "Distribuir horizontalmente", "distributeVertically": "Distribuir verticalmente", "flipHorizontal": "Girar horizontalmente", "flipVertical": "Girar verticalmente", "viewMode": "Modo presentación", "share": "Compartir", "showStroke": "Mostrar selector de color de trazo", "showBackground": "Mostrar el selector de color de fondo", "toggleTheme": "Cambiar tema", "personalLib": "Biblioteca personal", "excalidrawLib": "Biblioteca Excalidraw", "decreaseFontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "increaseFontSize": "Aumentar el tamaño de letra", "unbindText": "Desvincular texto", "bindText": "Vincular texto al contenedor", "createContainerFromText": "Envolver el texto en un contenedor", "link": {"edit": "<PERSON><PERSON> enlace", "editEmbed": "Editar enlace e incrustar", "create": "<PERSON><PERSON><PERSON> enlace", "createEmbed": "<PERSON><PERSON><PERSON> enlace e incrustar", "label": "Enlace", "labelEmbed": "Enlazar e incrustar", "empty": "No se ha establecido un enlace"}, "lineEditor": {"edit": "<PERSON><PERSON>", "exit": "Salir del editor en línea"}, "elementLock": {"lock": "Bloquear", "unlock": "Desb<PERSON>que<PERSON>", "lockAll": "Bloquear todo", "unlockAll": "Desb<PERSON><PERSON><PERSON> todo"}, "statusPublished": "Publicado", "sidebarLock": "Mantener barra lateral abierta", "selectAllElementsInFrame": "Seleccionar todos los elementos en el marco", "removeAllElementsFromFrame": "Eliminar todos los elementos del marco", "eyeDropper": "Seleccionar un color del lienzo", "textToDiagram": "Texto a diagrama", "prompt": "Sugerencia"}, "library": {"noItems": "No hay elementos añadidos todavía...", "hint_emptyLibrary": "Seleccione un elemento en el lienzo para añadirlo aquí, o instale una biblioteca del repositorio público, a continuación.", "hint_emptyPrivateLibrary": "Seleccione un elemento del lienzo para añadirlo aquí."}, "buttons": {"clearReset": "Limpiar lienzo y reiniciar el color de fondo", "exportJSON": "Exportar a archivo", "exportImage": "Exportar imagen...", "export": "Guardar en...", "copyToClipboard": "Copiar al portapapeles", "save": "Guardar en archivo actual", "saveAs": "Guardar como", "load": "Abrir", "getShareableLink": "Obtener enlace para compartir", "close": "<PERSON><PERSON><PERSON>", "selectLanguage": "Elegir idioma", "scrollBackToContent": "Volver al contenido", "zoomIn": "Acercarse", "zoomOut": "Alejarse", "resetZoom": "Restablecer zoom", "menu": "Menú", "done": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "resetLibrary": "Reiniciar biblioteca", "createNewRoom": "Crear nueva sala", "fullScreen": "Pantalla completa", "darkMode": "<PERSON><PERSON> oscuro", "lightMode": "<PERSON>do claro", "zenMode": "Modo Zen", "objectsSnapMode": "Ajustar a los objetos", "exitZenMode": "Salir del modo Zen", "cancel": "<PERSON><PERSON><PERSON>", "clear": "Bo<PERSON>r", "remove": "Eliminar", "embed": "", "publishLibrary": "Publicar", "submit": "Enviar", "confirm": "Confirmar", "embeddableInteractionButton": "<PERSON><PERSON>sa para <PERSON>uar"}, "alerts": {"clearReset": "Esto limpiará todo el lienzo. Estás seguro?", "couldNotCreateShareableLink": "No se pudo crear un enlace para compartir.", "couldNotCreateShareableLinkTooBig": "No se pudo crear el enlace para compartir: la escena es demasiado grande", "couldNotLoadInvalidFile": "No se pudo cargar el archivo no válido", "importBackendFailed": "La importación falló.", "cannotExportEmptyCanvas": "No se puede exportar un lienzo vació", "couldNotCopyToClipboard": "No se pudo copiar al portapapeles.", "decryptFailed": "No se pudieron descifrar los datos.", "uploadedSecurly": "La carga ha sido asegurada con cifrado de principio a fin, lo que significa que el servidor de Excalidraw y terceros no pueden leer el contenido.", "loadSceneOverridePrompt": "Si carga este dibujo externo, reemplazar<PERSON> el que tiene. ¿Desea continuar?", "collabStopOverridePrompt": "Detener la sesión sobrescribirá su dibujo anterior almacenado localmente. ¿Está seguro?\n\n(Si desea mantener su dibujo local, simplemente cierre la pestaña del navegador.)", "errorAddingToLibrary": "No se pudo agregar el elemento a la biblioteca", "errorRemovingFromLibrary": "No se pudo quitar el elemento de la biblioteca", "confirmAddLibrary": "Esto añadirá {{numShapes}} forma(s) a tu biblioteca. ¿Estás seguro?", "imageDoesNotContainScene": "Esta imagen no parece contener datos de escena. ¿Ha habilitado la inserción de la escena durante la exportación?", "cannotRestoreFromImage": "No se pudo restaurar la escena desde este archivo de imagen", "invalidSceneUrl": "No se ha podido importar la escena desde la URL proporcionada. Está mal formada, o no contiene datos de Excalidraw JSON válidos.", "resetLibrary": "Esto borrará tu biblioteca. ¿Estás seguro?", "removeItemsFromsLibrary": "¿Eliminar {{count}} elemento(s) de la biblioteca?", "invalidEncryptionKey": "La clave de cifrado debe tener 22 caracteres. La colaboración en vivo está deshabilitada.", "collabOfflineWarning": "No hay conexión a internet disponible.\n¡No se guardarán los cambios!"}, "errors": {"unsupportedFileType": "Tipo de archivo no admitido.", "imageInsertError": "No se pudo insertar la imagen. Inténtelo de nuevo más tarde...", "fileTooBig": "Archivo demasiado grande. El tamaño máximo permitido es {{maxSize}}.", "svgImageInsertError": "No se pudo insertar la imagen SVG. El código SVG parece inválido.", "failedToFetchImage": "Error al obtener la imagen.", "invalidSVGString": "SVG no válido.", "cannotResolveCollabServer": "No se pudo conectar al servidor colaborador. Por favor, vuelva a cargar la página y vuelva a intentarlo.", "importLibraryError": "No se pudo cargar la librería", "collabSaveFailed": "No se pudo guardar en la base de datos del backend. Si los problemas persisten, debería guardar su archivo localmente para asegurarse de que no pierde su trabajo.", "collabSaveFailed_sizeExceeded": "No se pudo guardar en la base de datos del backend, el lienzo parece ser demasiado grande. Debería guardar el archivo localmente para asegurarse de que no pierde su trabajo.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "Parece que estás usando el navegador Brave con el ajuste <bold><PERSON>zar el bloqueo de huellas digitales</bold> habilitado.", "line2": "Esto podría resultar en errores en los <bold>Elementos de Texto</bold> en tus dibujos.", "line3": "Recomendamos fuertemente deshabilitar esta configuración. P<PERSON>es seguir <link>estos pasos</link> sobre cómo hacerlo.", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "Los elementos IFrame no se pueden agregar a la biblioteca.", "image": ""}, "asyncPasteFailedOnRead": "No se pudo pegar (no se pudo leer desde el portapapeles del sistema).", "asyncPasteFailedOnParse": "No se pudo pegar.", "copyToSystemClipboardFailed": "No se pudo copiar al portapapeles."}, "toolBar": {"selection": "Selección", "image": "Insertar imagen", "rectangle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diamond": "Diamante", "ellipse": "Elipse", "arrow": "Fle<PERSON>", "line": "Lín<PERSON>", "freedraw": "<PERSON><PERSON><PERSON>", "text": "Texto", "library": "Biblioteca", "lock": "Mantener la herramienta seleccionada activa después de dibujar", "penMode": "Modo <PERSON> - previene toque", "link": "<PERSON><PERSON><PERSON>/Actualizar enlace para una forma seleccionada", "eraser": "Bo<PERSON>r", "frame": "", "magicframe": "Esquema a código", "embeddable": "Incrustar Web", "laser": "<PERSON><PERSON><PERSON>", "hand": "Man<PERSON> (herramienta de panoramización)", "extraTools": "Más <PERSON>", "mermaidToExcalidraw": "Mermaid a Excalidraw", "magicSettings": "Ajustes AI"}, "headings": {"canvasActions": "Acciones del lienzo", "selectedShapeActions": "Acciones de la forma seleccionada", "shapes": "Formas"}, "hints": {"canvasPanning": "Para mover el lienzo, mantenga la rueda del ratón o la barra espaciadora mientras arrastra o utilice la herramienta de mano", "linearElement": "<PERSON>z clic para dibujar múl<PERSON><PERSON> puntos, arrastrar para solo una línea", "freeDraw": "Haz clic y arrastra, suelta al terminar", "text": "Consejo: tamb<PERSON><PERSON> puedes añadir texto haciendo doble clic en cualquier lugar con la herramienta de selección", "embeddable": "Haga clic y arrastre para crear un sitio web incrustado", "text_selected": "Doble clic o pulse ENTER para editar el texto", "text_editing": "Pulse Escape o Ctrl/Cmd + ENTER para terminar de editar", "linearElementMulti": "Haz clic en el último punto o presiona Escape o Enter para finalizar", "lockAngle": "<PERSON>uedes restringir el ángulo manteniendo presionado el botón SHIFT", "resize": "Para mantener las proporciones mantén SHIFT presionado mientras modificas el tamaño, \nmantén presionado ALT para modificar el tamaño desde el centro", "resizeImage": "Puede redimensionar libremente pulsando SHIFT,\npulse ALT para redimensionar desde el centro", "rotate": "Puedes restringir los ángulos manteniendo presionado SHIFT mientras giras", "lineEditor_info": "Mantenga pulsado CtrlOrCmd y haga doble click o presione CtrlOrCmd + Enter para editar puntos", "lineEditor_pointSelected": "Presione Suprimir para eliminar el/los punto(s), CtrlOrCmd+D para duplicarlo, o arrástrelo para moverlo", "lineEditor_nothingSelected": "Seleccione un punto a editar (mantenga MAYÚSCULAS para seleccionar múltiples),\no mantenga pulsado Alt y haga click para añadir nuevos puntos", "placeImage": "Haga clic para colocar la imagen o haga click y arrastre para establecer su tamaño manualmente", "publishLibrary": "Publica tu propia biblioteca", "bindTextToElement": "Presione Entrar para agregar", "deepBoxSelect": "Mantén CtrlOrCmd para seleccionar en profundidad, y para evitar arrastrar", "eraserRevert": "Mantenga pulsado Alt para revertir los elementos marcados para su eliminación", "firefox_clipboard_write": "Esta característica puede ser habilitada estableciendo la bandera \"dom.events.asyncClipboard.clipboardItem\" a \"true\". Para cambiar las banderas del navegador en Firefox, visite la página \"about:config\".", "disableSnapping": "Mantén pulsado CtrlOrCmd para desactivar el ajuste"}, "canvasError": {"cannotShowPreview": "No se puede mostrar la vista previa", "canvasTooBig": "El lienzo podría ser demasiado grande.", "canvasTooBigTip": "Sugerencia: intenta acercar un poco más los elementos más lejanos."}, "errorSplash": {"headingMain": "Se encontró un error. Intente <button>recargando la página.</button>", "clearCanvasMessage": "Si la recarga no funciona, intente <button>limpiando el lienzo.</button>", "clearCanvasCaveat": " Esto provocará la pérdida de su trabajo ", "trackedToSentry": "El error con el identificador {{eventId}} fue rastreado en nuestro sistema.", "openIssueMessage": "Fuimos muy cautelosos de no incluir la información de tu escena en el error. Si tu escena no es privada, por favor considera seguir nuestro <button>rastreador de errores.</button> Por favor, incluya la siguiente información copiándola y pegándola en el issue de GitHub.", "sceneContent": "Contenido de la escena:"}, "roomDialog": {"desc_intro": "<PERSON><PERSON>e invitar a otras personas a tu actual escena para que colaboren contigo.", "desc_privacy": "No te preocupes, la sesión usa encriptación de punta a punta, por lo que todo lo que se dibuje se mantendrá privadamente. Ni siquiera nuestro servidor podrá ver lo que haces.", "button_startSession": "In<PERSON><PERSON>", "button_stopSession": "Detener sesión", "desc_inProgressIntro": "La sesión de colaboración en vivo está ahora en progreso.", "desc_shareLink": "Comparte este enlace con cualquier persona con quien quieras colaborar:", "desc_exitSession": "Detener la sesión te desconectará de la sala, pero podrás seguir trabajando con la escena en su computadora, esto es de modo local. Ten en cuenta que esto no afectará a otras personas, y que las mismas seguirán siendo capaces de colaborar en tu escena.", "shareTitle": "Únase a una sesión colaborativa en vivo en Excalidraw"}, "errorDialog": {"title": "Error"}, "exportDialog": {"disk_title": "Guardar en disco", "disk_details": "Exportar los datos de la escena a un archivo desde el cual pueda importar más tarde.", "disk_button": "Guardar en archivo", "link_title": "Enlace para compartir", "link_details": "Exportar como enlace de sólo lectura.", "link_button": "Exportar a Link", "excalidrawplus_description": "Guarde la escena en su espacio de trabajo de Excalidraw+.", "excalidrawplus_button": "Exportar", "excalidrawplus_exportError": "No se pudo exportar a Excalidraw+ en este momento..."}, "helpDialog": {"blog": "Lea nuestro blog", "click": "click", "deepSelect": "Selección profunda", "deepBoxSelect": "Seleccione en profundidad dentro de la caja, y evite arrastrar", "curvedArrow": "Flecha curva", "curvedLine": "Línea curva", "documentation": "Documentación", "doubleClick": "doble clic", "drag": "arra<PERSON><PERSON>", "editor": "Editor", "editLineArrowPoints": "Editar puntos de línea/flecha", "editText": "Editar texto / añadir etiqueta", "github": "¿Ha encontrado un problema? Envíelo", "howto": "Siga nuestras guías", "or": "o", "preventBinding": "<PERSON><PERSON><PERSON> enlace de flechas", "tools": "Herramientas", "shortcuts": "Atajos del teclado", "textFinish": "<PERSON><PERSON>r edición (editor de texto)", "textNewLine": "<PERSON><PERSON><PERSON> nueva <PERSON> (editor de texto)", "title": "<PERSON><PERSON><PERSON>", "view": "Vista", "zoomToFit": "Ajustar la vista para mostrar todos los elementos", "zoomToSelection": "<PERSON>p<PERSON><PERSON>", "toggleElementLock": "Bloquear/desbloquear selección", "movePageUpDown": "Mover página hacia arriba/abajo", "movePageLeftRight": "Mover página hacia la izquierda/derecha"}, "clearCanvasDialog": {"title": "<PERSON><PERSON><PERSON> lienzo"}, "publishDialog": {"title": "Publicar biblioteca", "itemName": "Nombre del artículo", "authorName": "Nombre del autor", "githubUsername": "Nombre de usuario de GitHub", "twitterUsername": "Nombre de usuario de Twitter", "libraryName": "Nombre de la biblioteca", "libraryDesc": "Descripción de la biblioteca", "website": "Sitio Web", "placeholder": {"authorName": "Nombre o nombre de usuario", "libraryName": "Nombre de tu biblioteca", "libraryDesc": "Descripción de su biblioteca para ayudar a la gente a entender su uso", "githubHandle": "Nombre de usuario de GitHub (opcional), así podrá editar la biblioteca una vez enviada para su revisión", "twitterHandle": "Nombre de usuario de Twitter (opcional), así sabemos a quién acreditar cuando se promociona en Twitter", "website": "Enlace a su sitio web personal o en cualquier otro lugar (opcional)"}, "errors": {"required": "Requerido", "website": "Introduce una URL válida"}, "noteDescription": "Envía tu biblioteca para ser incluida en el <link>repositorio de librería pública</link>para que otras personas utilicen en sus dibujos.", "noteGuidelines": "La biblioteca debe ser aprobada manualmente primero. Por favor, lea la <link>pautas</link> antes de enviar. Necesitará una cuenta de GitHub para comunicarse y hacer cambios si se solicita, pero no es estrictamente necesario.", "noteLicense": "Al enviar, usted acepta que la biblioteca se publicará bajo el <link>Licencia MIT </link>que en breve significa que cualquiera puede utilizarlos sin restricciones.", "noteItems": "Cada elemento de la biblioteca debe tener su propio nombre para que sea filtrable. Los siguientes elementos de la biblioteca serán incluidos:", "atleastOneLibItem": "Por favor, seleccione al menos un elemento de la biblioteca para empezar", "republishWarning": "Nota: algunos de los elementos seleccionados están marcados como ya publicados/enviados. Sólo debería volver a enviar elementos cuando se actualice una biblioteca o envío."}, "publishSuccessDialog": {"title": "Biblioteca enviada", "content": "<PERSON><PERSON><PERSON> {{authorName}}. Su biblioteca ha sido enviada para ser revisada. Puede seguir el estado<link>aquí</link>"}, "confirmDialog": {"resetLibrary": "Reiniciar biblioteca", "removeItemsFromLib": "Eliminar elementos seleccionados de la biblioteca"}, "imageExportDialog": {"header": "Exportar imagen", "label": {"withBackground": "Fondo", "onlySelected": "<PERSON><PERSON><PERSON>", "darkMode": "<PERSON><PERSON> oscuro", "embedScene": "Incrustar escena", "scale": "Escalar", "padding": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "Exportar a PNG", "exportToSvg": "Exportar a SVG", "copyPngToClipboard": "Copiar PNG al portapapeles"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Copiar al portapapeles"}}, "encrypted": {"tooltip": "Tus dibujos están cifrados de punto a punto, por lo que los servidores de Excalidraw nunca los verán.", "link": "Entrada en el blog sobre cifrado de extremo a extremo"}, "stats": {"angle": "<PERSON><PERSON><PERSON>", "element": "Elemento", "elements": "Elementos", "height": "Alto", "scene": "Escena", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storage": "Almacenamiento", "title": "Estadísticas para nerds", "total": "Total", "version": "Versión", "versionCopy": "Click para copiar", "versionNotAvailable": "Versión no disponible", "width": "<PERSON><PERSON>"}, "toast": {"addedToLibrary": "Añadido a la biblioteca", "copyStyles": "Estilos copiados.", "copyToClipboard": "Copiado en el portapapeles.", "copyToClipboardAsPng": "Copiado {{exportSelection}} al portapapeles como PNG\n({{exportColorScheme}})", "fileSaved": "Archivo guardado.", "fileSavedToFilename": "Guardado en {filename}", "canvas": "lienzo", "selection": "selección", "pasteAsSingleElement": "Usa {{shortcut}} para pegar como un solo elemento,\no pegar en un editor de texto existente", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Transparente", "black": "Negro", "white": "<PERSON>", "red": "<PERSON><PERSON><PERSON>", "pink": "<PERSON>", "grape": "<PERSON><PERSON>", "violet": "<PERSON><PERSON>", "gray": "<PERSON><PERSON>", "blue": "Azul", "cyan": "<PERSON><PERSON>", "teal": "Turquesa", "green": "Verde", "yellow": "Amarillo", "orange": "<PERSON><PERSON><PERSON>", "bronze": "Bronce"}, "welcomeScreen": {"app": {"center_heading": "Toda su información es guardada localmente en su navegador.", "center_heading_plus": "¿Quieres ir a Excalidraw+?", "menuHint": "Exportar, preferencias, idiomas, ..."}, "defaults": {"menuHint": "Exportar, preferencias y más...", "center_heading": "Diagramas. Hecho. Simplemente.", "toolbarHint": "¡Elige una herramienta y empieza a dibujar!", "helpHint": "Atajos y ayuda"}}, "colorPicker": {"mostUsedCustomColors": "Colores personalizados más utilizados", "colors": "Colores", "shades": "", "hexCode": "Código Hexadecimal", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Exportar como imagen", "button": "Exportar como imagen", "description": ""}, "saveToDisk": {"title": "Guardar en el disco", "button": "Guardar en el disco", "description": "Exporta los datos de la escena a un archivo desde el cual podrás importar más tarde."}, "excalidrawPlus": {"title": "", "button": "Exportar a Excalidraw+", "description": ""}}, "modal": {"loadFromFile": {"title": "Cargar desde un archivo", "button": "Cargar desde un archivo", "description": ""}, "shareableLink": {"title": "<PERSON><PERSON> desde un enlace", "button": "Reemplazar mi contenido", "description": "Cargar un dibujo externo <bold>reemplazará tu contenido existente</bold>.<br></br><PERSON><PERSON><PERSON> primero hacer una copia de seguridad de tu dibujo usando una de las opciones de abajo."}}}, "mermaid": {"title": "Mermaid a Excalidraw", "button": "Insertar", "description": "Actualmente sólo <flowchartLink>Flowchart</flowchartLink>,<sequenceLink> Secuencia, </sequenceLink> y <classLink>Class </classLink>Diagramas son soportados. Los otros tipos se renderizarán como imagen en Excalidraw.", "syntax": "Sintaxis Me<PERSON>", "preview": "Vista previa"}}