{"name": "uk-schools-lead-generation", "version": "1.0.0", "description": "Automated lead generation system for UK schools and Multi Academy Trusts", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "build": "npm run clean && npm run compile", "clean": "rm -rf dist", "compile": "tsc", "setup": "node setup.js", "data:collect": "node src/scripts/collectData.js", "data:enrich": "node src/scripts/enrichData.js", "email:send": "node src/scripts/sendEmails.js", "setup:db": "node src/scripts/setupDatabase.js", "test:apollo": "node src/scripts/testApollo.js"}, "keywords": ["lead-generation", "uk-schools", "multi-academy-trusts", "email-automation", "crm-integration"], "author": "Lead Generation System", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "node-cron": "^3.0.3", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "sqlite3": "^5.1.6", "nodemailer": "^6.9.7", "joi": "^17.11.0", "winston": "^3.11.0", "rate-limiter-flexible": "^2.4.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "@types/node": "^20.8.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}