{"labels": {"paste": "Lim inn", "pasteAsPlaintext": "", "pasteCharts": "Lim inn diagram", "selectAll": "Vel alt", "multiSelect": "Legg til element i utval", "moveCanvas": "<PERSON><PERSON> lerre<PERSON>t", "cut": "<PERSON><PERSON><PERSON> ut", "copy": "<PERSON><PERSON><PERSON>", "copyAsPng": "<PERSON><PERSON><PERSON> til utklippstavla som PNG", "copyAsSvg": "Ko<PERSON>r til utklippstavla som SVG", "copyText": "", "copySource": "", "convertToCode": "", "bringForward": "Flytt framover", "sendToBack": "Send heilt bak", "bringToFront": "Flytt heilt fram", "sendBackward": "Send bakover", "delete": "<PERSON><PERSON>", "copyStyles": "<PERSON><PERSON><PERSON> s<PERSON>", "pasteStyles": "Lim inn stilar", "stroke": "Strek", "background": "Bakgrunn", "fill": "<PERSON><PERSON><PERSON>", "strokeWidth": "Strekbreidd", "strokeStyle": "Strekstil", "strokeStyle_solid": "Solid", "strokeStyle_dashed": "St<PERSON><PERSON>", "strokeStyle_dotted": "<PERSON><PERSON><PERSON>", "sloppiness": "Ujamnheit", "opacity": "Synlegheit", "textAlign": "Tekstjustering", "edges": "<PERSON><PERSON>", "sharp": "<PERSON><PERSON><PERSON>", "round": "Rund", "arrowheads": "<PERSON><PERSON><PERSON><PERSON>", "arrowhead_none": "Ingen", "arrowhead_arrow": "<PERSON>l", "arrowhead_bar": "<PERSON><PERSON><PERSON>", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Trekant", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Skriftstorleik", "fontFamily": "Skrifttype", "addWatermark": "<PERSON><PERSON> til «Laga med Excalidraw»", "handDrawn": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normal", "code": "<PERSON><PERSON>", "small": "<PERSON>ten", "medium": "Medium", "large": "St<PERSON>", "veryLarge": "<PERSON><PERSON><PERSON><PERSON> stor", "solid": "Solid", "hachure": "<PERSON><PERSON><PERSON>", "zigzag": "", "crossHatch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thin": "<PERSON><PERSON>", "bold": "Tjukk", "left": "<PERSON><PERSON><PERSON>", "center": "Midstill", "right": "<PERSON><PERSON><PERSON><PERSON>", "extraBold": "Ekstra tjukk", "architect": "Arkitekt", "artist": "<PERSON><PERSON><PERSON>", "cartoonist": "<PERSON><PERSON><PERSON>", "fileTitle": "Filnamn", "colorPicker": "Far<PERSON>vel<PERSON>", "canvasColors": "Brukt på lerretet", "canvasBackground": "Lerretsbakgrunn", "drawingCanvas": "<PERSON><PERSON><PERSON>", "layers": "Lag", "actions": "Handlingar", "language": "Språk", "liveCollaboration": "", "duplicateSelection": "<PERSON><PERSON><PERSON><PERSON>", "untitled": "Utan namn", "name": "<PERSON><PERSON>", "yourName": "<PERSON><PERSON> ditt", "madeWithExcalidraw": "Laga med Excalidraw", "group": "Grupper utval", "ungroup": "Avgrupper utval", "collaborators": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showGrid": "<PERSON><PERSON> rute<PERSON>", "addToLibrary": "Legg til i bibliotek", "removeFromLibrary": "Fjern frå bibliotek", "libraryLoadingMessage": "Laster bibliotek…", "libraries": "Blad gjennom bibliotek", "loadingScene": "Laster scene…", "align": "<PERSON><PERSON>", "alignTop": "Juster til topp", "alignBottom": "Juster til botn", "alignLeft": "<PERSON>er til venstre", "alignRight": "<PERSON><PERSON> til høgre", "centerVertically": "Midtstill vertikalt", "centerHorizontally": "Midtstill horisontalt", "distributeHorizontally": "Sprei horisontalt", "distributeVertically": "Sprei vertikalt", "flipHorizontal": "<PERSON><PERSON><PERSON>", "flipVertical": "<PERSON><PERSON><PERSON> lo<PERSON>", "viewMode": "Visningsmodus", "share": "Del", "showStroke": "<PERSON><PERSON> for linjer", "showBackground": "<PERSON><PERSON> for bakgrunn", "toggleTheme": "<PERSON><PERSON><PERSON> tema", "personalLib": "Personleg bibliotek", "excalidrawLib": "Excalidraw-bibliotek", "decreaseFontSize": "<PERSON><PERSON> skriftstorleik mindre", "increaseFontSize": "<PERSON><PERSON> skriftstorleik større", "unbindText": "Avbind tekst", "bindText": "", "createContainerFromText": "", "link": {"edit": "<PERSON><PERSON> lenke", "editEmbed": "", "create": "Lag lenke", "createEmbed": "", "label": "<PERSON><PERSON>", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "", "exit": ""}, "elementLock": {"lock": "", "unlock": "", "lockAll": "", "unlockAll": ""}, "statusPublished": "", "sidebarLock": "", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "", "hint_emptyLibrary": "", "hint_emptyPrivateLibrary": ""}, "buttons": {"clearReset": "Tilbakestill lerretet", "exportJSON": "Eksporter til fil", "exportImage": "", "export": "", "copyToClipboard": "Ko<PERSON>r til utklippstavla", "save": "Lagre til noverande fil", "saveAs": "Lagre som", "load": "", "getShareableLink": "<PERSON><PERSON>", "close": "Lukk", "selectLanguage": "<PERSON><PERSON>", "scrollBackToContent": "<PERSON><PERSON><PERSON> tilbake til innhald", "zoomIn": "Zoom inn", "zoomOut": "Zoom ut", "resetZoom": "Nullstill zoom", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "undo": "<PERSON><PERSON>", "redo": "<PERSON><PERSON> om", "resetLibrary": "Nullstill bibliotek", "createNewRoom": "Lag nytt rom", "fullScreen": "Fullskjerm", "darkMode": "<PERSON><PERSON><PERSON> modus", "lightMode": "Lys modus", "zenMode": "Zen-modus", "objectsSnapMode": "", "exitZenMode": "Avslutt zen-modus", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "embed": "", "publishLibrary": "<PERSON><PERSON><PERSON>", "submit": "Send inn", "confirm": "Stadfest", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "Dette vil tømme lerretet. Er du sikker?", "couldNotCreateShareableLink": "Kunne ikkje lage delingslenke.", "couldNotCreateShareableLinkTooBig": "<PERSON><PERSON> ikkje opprette deleleg lenke: scena er for stor", "couldNotLoadInvalidFile": "Kunne ikkje laste inn ugyldig fil", "importBackendFailed": "Importering av backend feila.", "cannotExportEmptyCanvas": "Kan ikkje eksportere eit tomt lerret.", "couldNotCopyToClipboard": "", "decryptFailed": "Kunne ikkje dekryptere data.", "uploadedSecurly": "Opplastinga er kryptert og er ikkje mogleg å lese av Excalidraw-serveren eller tredjepartar.", "loadSceneOverridePrompt": "Innlasting av ekstern teikning erstattar ditt eksisterande innhald. Ynskjer du å fortsette?", "collabStopOverridePrompt": "Viss du avsluttar økta overskriv du den førre, lokalt lagra teikninga di. Er du sikker?\n\n(Ønsker du å halde fram med denne? Då er det berre å lukke denne fana.)", "errorAddingToLibrary": "Kunne ikkje legge elementet i biblioteket", "errorRemovingFromLibrary": "Kunne ikkje fjerne elementet frå biblioteket", "confirmAddLibrary": "Dette vil legge til {{numShapes}} form(er) i biblioteket ditt. <PERSON>r du sikker?", "imageDoesNotContainScene": "Dette biletet ser ikkje ut til å ha noko scenedata. Har du skrutt på innbygging av scene medan eksporteringa heldt på?", "cannotRestoreFromImage": "Scena kunne ikkje gjenopprettast frå denne bilet<PERSON>la", "invalidSceneUrl": "Kunne ikkje hente noko scene frå den URL-en. Ho er anten øydelagd eller inneheld ikkje gyldig Excalidraw JSON-data.", "resetLibrary": "Dette vil fjerne alt innhald frå biblioteket. Er du sikker?", "removeItemsFromsLibrary": "Slette {{count}} element frå biblioteket?", "invalidEncryptionKey": "Krypteringsnøkkelen må ha 22 teikn. Sanntidssamarbeid er deaktivert.", "collabOfflineWarning": ""}, "errors": {"unsupportedFileType": "Filtypen er ikkje støtta.", "imageInsertError": "Kunne ikkje sette inn biletet. Prøv igjen seinare...", "fileTooBig": "Fila er for stor. <PERSON><PERSON><PERSON> tillate storleik er {{maxSize}}.", "svgImageInsertError": "Kunne ikkje sette inn SVG-biletet. SVG-koden ser ugyldig ut.", "failedToFetchImage": "", "invalidSVGString": "Ugyldig SVG.", "cannotResolveCollabServer": "Kunne ikkje kople til samarbeidsserveren. Ver vennleg å oppdatere inn sida og prøv på nytt.", "importLibraryError": "", "collabSaveFailed": "", "collabSaveFailed_sizeExceeded": "", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "Vel", "image": "Sett in bilete", "rectangle": "<PERSON><PERSON><PERSON><PERSON>", "diamond": "<PERSON><PERSON><PERSON>", "ellipse": "Ellipse", "arrow": "<PERSON>l", "line": "<PERSON><PERSON>", "freedraw": "<PERSON><PERSON><PERSON>", "text": "Tekst", "library": "Bibliotek", "lock": "Hald fram med valt verktøy", "penMode": "", "link": "Legg til/ oppdater lenke til valt figur", "eraser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "Handlingar: lerret", "selectedShapeActions": "Handlingar: valt objekt", "shapes": "Formar"}, "hints": {"canvasPanning": "", "linearElement": "<PERSON><PERSON><PERSON> for å starte linje med fleire punkt, el<PERSON> drag for ei enkel linje", "freeDraw": "Klikk og drag, slepp når du er ferdig", "text": "Tips: du kan òg leggje til tekst ved å dobbeltklikke kor som helst med utvalgsverktyet", "embeddable": "", "text_selected": "Dobbelklikk eller trykk ENTER for å redigere teksta", "text_editing": "Trykk Escape eller CtrlOrCmd+ENTER for å fullføre redigeringa", "linearElementMulti": "Klikk på siste punkt eller trykk Escape eller Enter for å fullføre", "lockAngle": "Du kan begrense vinkelen ved å holde nede SKIFT", "resize": "Du kan halde fram med forholdet ved å trykke SHIFT medan du endrar storleik,\ntrykk ALT for å endre storleiken frå midten", "resizeImage": "Du kan endre storleiken fritt ved å halde inne SHIFT,\nhald ALT for å endre storleik frå sentrum", "rotate": "Du kan låse vinklane ved å halde SHIFT medan du roterer", "lineEditor_info": "", "lineEditor_pointSelected": "Trykk på Slett for å fjerne punkt(a),\nCtrl / Cmd+D for å duplisere, eller drag for å flytte", "lineEditor_nothingSelected": "<PERSON>el eit punkt å redigere (hald inne SHIFT for å velje fleire),\neller hald inne Alt og klikk for å legge til nye punkt", "placeImage": "Klikk for å plassere biletet, eller klikk og drag for å velje storleik manuelt", "publishLibrary": "Publiser ditt eige bibliotek", "bindTextToElement": "Trykk på enter for å legge til tekst", "deepBoxSelect": "Hald inne Ctrl / Cmd for å velje djupt, og forhindre flytting", "eraserRevert": "Hald inne Alt for å reversere markering av element for sletting", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Kan ikkje vise førehandsvising", "canvasTooBig": "Lerretet er mogleg for stort.", "canvasTooBigTip": "Tips: prøv <PERSON> flytte elementa som er lengst frå kvarandre, litt nærare kvarandre."}, "errorSplash": {"headingMain": "Ein feil oppstod. Prøv <button>å laste sida på nytt.</button>", "clearCanvasMessage": "Om ny sidelasting ik<PERSON><PERSON> fun<PERSON>, prøv <button>å tømme lerretet.</button>", "clearCanvasCaveat": " Dette vil føre til tap av arbeid ", "trackedToSentry": "Feilen med identifikator {{eventId}} vart logga i systemet vårt.", "openIssueMessage": "Vi er veldig nøye med å ikkje inkludere scene-opplysingane dine i feilmeldinga. Viss scena di ikkje er privat kan du vurdere å følge opp i <button>feilrapporteringssystemet vårt.</button> Ta med opplysingane nedanfor ved å kopiere og lime inn i GitHub-saka.", "sceneContent": "Scene-innhald:"}, "roomDialog": {"desc_intro": "Du kan invitere personar til scena di for å samarbeide med deg.", "desc_privacy": "<PERSON> det med ro; ø<PERSON>a brukar ende-til-ende-krypt<PERSON>, så alt du teiknar held fram med å vere privat. Ikkje ein gong serveren vår kan sjå kva du lagar.", "button_startSession": "Start økt", "button_stopSession": "Stopp økt", "desc_inProgressIntro": "Sanntids-samarbeidsøkt er no i gang.", "desc_shareLink": "<PERSON> denne lenka med dei du vil samarbeide med:", "desc_exitSession": "Dersom du avsluttar økta blir du kopla frå rommet, men du kan halde fram med å arbeide med scena lokalt. Ver merksam på at dette ikkje vil påverke andre personar, og desse vil framleis ha moglegheit til å samarbeide på deira eigen versjon.", "shareTitle": "Bli med på eit sanntidssamarbeid på Excalidraw"}, "errorDialog": {"title": "<PERSON><PERSON>"}, "exportDialog": {"disk_title": "Lagre til disk", "disk_details": "Eksporter scenedataa til ei fil du kan importere seinare.", "disk_button": "Lagre til fil", "link_title": "<PERSON><PERSON><PERSON> lenke", "link_details": "Eksporter som skrivebeskytta lenke.", "link_button": "Eksporter til lenke", "excalidrawplus_description": "Lagre scena til Excalidraw+-arbeidsområdet ditt.", "excalidrawplus_button": "Eksporter", "excalidrawplus_exportError": "Kunne ikkje eksportere til Excalidraw+ akkurat no..."}, "helpDialog": {"blog": "Les bloggen vår", "click": "klikk", "deepSelect": "<PERSON><PERSON> djupt", "deepBoxSelect": "<PERSON><PERSON> djupt inni boksen og forhindr flytting", "curvedArrow": "Boga pil", "curvedLine": "Boga linje", "documentation": "Dokumentasjon", "doubleClick": "dobbelklikk", "drag": "drag", "editor": "Redigering", "editLineArrowPoints": "", "editText": "", "github": "Funne eit problem? Send inn", "howto": "F<PERSON><PERSON>g vegleiinga vår", "or": "eller", "preventBinding": "<PERSON><PERSON><PERSON> p<PERSON>", "tools": "", "shortcuts": "Tastatursnarvegar", "textFinish": "<PERSON><PERSON><PERSON><PERSON> rediger<PERSON> (teksthandsamar)", "textNewLine": "<PERSON>gg til ny linje (teksthandsamar)", "title": "<PERSON><PERSON><PERSON><PERSON>", "view": "Vising", "zoomToFit": "Zoom for å sjå alle elementa", "zoomToSelection": "Zoom til utval", "toggleElementLock": "", "movePageUpDown": "", "movePageLeftRight": ""}, "clearCanvasDialog": {"title": "<PERSON><PERSON><PERSON> le<PERSON>"}, "publishDialog": {"title": "Publiser bibliotek", "itemName": "Elementnamn", "authorName": "Eigaren sitt namn", "githubUsername": "GitHub-brukarnamn", "twitterUsername": "Twitter-brukarnamn", "libraryName": "Biblioteknamn", "libraryDesc": "Bibliotekskildring", "website": "Nettstad", "placeholder": {"authorName": "<PERSON><PERSON> eller brukarnamnet ditt", "libraryName": "Namnet på biblioteket ditt", "libraryDesc": "Skildring av biblioteket ditt sånn at andre forstår bruken av det", "githubHandle": "GitHub-<PERSON><PERSON><PERSON><PERSON><PERSON> (valfritt), slik at du kan redigere bibiloteket når det er sendt inn til vurdering", "twitterHandle": "Twitter-brukarnamn (valfritt), så vi veit kven vi skal kreditere på Twitter", "website": "<PERSON><PERSON> til den personlege nettstaden din eller ein anna stad (valfritt)"}, "errors": {"required": "Kravt", "website": "Fyll inn ein gyldig URL"}, "noteDescription": "Send inn biblioteket ditt til inkludering i <link>den offentlege bibliotek-kjeldekoda</link>slik at andre kan bruke det i teikningane deira.", "noteGuidelines": "Biblioteket må godkjennast manuelt fyrst. Ver vennleg å lese <link>retningslinjene</link> før du sender inn. Du kjem til å trenge ein GitHub-konto for å kommunisere og gjere endringar dersom kravt, men det er ikkje strengt naudsynt.", "noteLicense": "Ved å sende inn godkjenner du at biblioteket vert publisert under <link>MIT-lisensen, </link>som kort sagt betyr at kven som helst kan bruke det utan avgrensingar.", "noteItems": "Kvart bibliotekselement må ha eit eige namn, slik at det er mogleg å filtrere. Dei følgande bibliotekselementa blir inkludert:", "atleastOneLibItem": "Ver vennleg å markere minst eitt bibliotekselement for å starte", "republishWarning": ""}, "publishSuccessDialog": {"title": "Bibliotek innsendt", "content": "<PERSON>sen takk {{authorName}}! Biblioteket ditt har blitt sendt inn til gjennomgang. Du kan halde styr på status<link>her</link>"}, "confirmDialog": {"resetLibrary": "Tilbakestill bibliotek", "removeItemsFromLib": "Fjern valde element frå biblioteket"}, "imageExportDialog": {"header": "", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "", "scale": "", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "<PERSON><PERSON>ning<PERSON> dine er ende-til-ende-krypterte slik at Excalidraw sine serverar aldri får sjå dei.", "link": "Blogginnlegg om ende-til-ende-kryptering i Excalidraw"}, "stats": {"angle": "<PERSON><PERSON>", "element": "Element", "elements": "Element", "height": "<PERSON><PERSON><PERSON><PERSON>", "scene": "Scene", "selected": "Valde", "storage": "<PERSON><PERSON><PERSON>", "title": "Statistikk for nerdar", "total": "Totalt", "version": "Versjon", "versionCopy": "Klikk for å kopiere", "versionNotAvailable": "Versjonen er ikkje tilgjengeleg", "width": "Breidde"}, "toast": {"addedToLibrary": "Lagt til i bibliotek", "copyStyles": "<PERSON><PERSON><PERSON> stil<PERSON>.", "copyToClipboard": "Kopiert til utklippstavla.", "copyToClipboardAsPng": "Kopierte {{exportSelection}} til utklippstavla som PNG\n({{exportColorScheme}})", "fileSaved": "Fila er lagra.", "fileSavedToFilename": "Lagra som {filename}", "canvas": "lerret", "selection": "val", "pasteAsSingleElement": "", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Gjennomsiktig", "black": "", "white": "", "red": "", "pink": "", "grape": "", "violet": "", "gray": "", "blue": "", "cyan": "", "teal": "", "green": "", "yellow": "", "orange": "", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "", "center_heading_plus": "", "menuHint": ""}, "defaults": {"menuHint": "", "center_heading": "", "toolbarHint": "", "helpHint": ""}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}