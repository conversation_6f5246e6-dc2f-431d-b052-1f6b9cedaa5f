{"labels": {"paste": "Lipire", "pasteAsPlaintext": "Inserare ca text simplu", "pasteCharts": "Lipire diagrame", "selectAll": "Selectare totală", "multiSelect": "Adaugă element la selecție", "moveCanvas": "<PERSON><PERSON><PERSON>", "cut": "<PERSON>up<PERSON>", "copy": "<PERSON><PERSON><PERSON>", "copyAsPng": "Copiere în memoria temporară ca PNG", "copyAsSvg": "Copiere în memoria temporară ca SVG", "copyText": "Copiere în memoria temporară ca text", "copySource": "Copiere sursă în memoria temporară", "convertToCode": "Convertire în cod", "bringForward": "Aducere în plan apropiat", "sendToBack": "Trimitere în ultimul plan", "bringToFront": "Aducere în prim plan", "sendBackward": "Trimitere în plan secundar", "delete": "<PERSON><PERSON><PERSON><PERSON>", "copyStyles": "<PERSON><PERSON><PERSON> s<PERSON>", "pasteStyles": "<PERSON><PERSON> stiluri", "stroke": "<PERSON><PERSON>", "background": "Fundal", "fill": "Umplere", "strokeWidth": "Lățimea conturului", "strokeStyle": "Stilul conturului", "strokeStyle_solid": "Neîntrerupt", "strokeStyle_dashed": "Liniuțe", "strokeStyle_dotted": "Punctat", "sloppiness": "Aspectul trasării", "opacity": "Opacitate", "textAlign": "Alinierea textului", "edges": "<PERSON><PERSON><PERSON>", "sharp": "Ascuțite", "round": "Rotunde", "arrowheads": "Vârfuri de săgeată", "arrowhead_none": "Niciunul", "arrowhead_arrow": "Săgeată", "arrowhead_bar": "Bară", "arrowhead_circle": "Cerc", "arrowhead_circle_outline": "Cerc (contur)", "arrowhead_triangle": "Triunghi", "arrowhead_triangle_outline": "<PERSON><PERSON><PERSON> (contur)", "arrowhead_diamond": "Romb", "arrowhead_diamond_outline": "<PERSON><PERSON> (contur)", "fontSize": "Dimensiune font", "fontFamily": "Familia de fonturi", "addWatermark": "Adaugă „Realizat cu Excalidraw”", "handDrawn": "<PERSON><PERSON>", "normal": "Normal", "code": "Cod", "small": "<PERSON><PERSON><PERSON>", "medium": "Me<PERSON>", "large": "Mare", "veryLarge": "Foarte mare", "solid": "Plină", "hachure": "<PERSON><PERSON><PERSON><PERSON>", "zigzag": "Zigzag", "crossHatch": "Hașură transversală", "thin": "Subțire", "bold": "Îngroșat<PERSON>", "left": "Stânga", "center": "Centru", "right": "<PERSON><PERSON><PERSON>", "extraBold": "Extra îngroșată", "architect": "Arhitect", "artist": "Artist", "cartoonist": "Caricaturist", "fileTitle": "<PERSON><PERSON>", "colorPicker": "Selector de culoare", "canvasColors": "Folosite pe pânză", "canvasBackground": "Fundalul pânzei", "drawingCanvas": "Pânz<PERSON> pentru desenat", "layers": "<PERSON><PERSON><PERSON>", "actions": "Acțiuni", "language": "Limbă", "liveCollaboration": "Colaborare în direct...", "duplicateSelection": "<PERSON><PERSON><PERSON><PERSON>", "untitled": "Nedenumit", "name": "Nume", "yourName": "<PERSON><PERSON><PERSON> t<PERSON>u", "madeWithExcalidraw": "Realizat cu Excalidraw", "group": "Grupare selecție", "ungroup": "Degrupare selecție", "collaborators": "Colaboratori", "showGrid": "Afișare grilă", "addToLibrary": "Adăugare la bibliotecă", "removeFromLibrary": "Eliminare din bibliotecă", "libraryLoadingMessage": "Se încarcă biblioteca…", "libraries": "Răsfoiește bibliotecile", "loadingScene": "Se încarcă scena…", "align": "Aliniere", "alignTop": "Aliniere sus", "alignBottom": "Aliniere jos", "alignLeft": "Aliniere la stânga", "alignRight": "Aliniere la dreapta", "centerVertically": "Centrare verticală", "centerHorizontally": "Centrare orizontală", "distributeHorizontally": "Distribuie orizontal", "distributeVertically": "Distribuie vertical", "flipHorizontal": "Răsturnare orizontală", "flipVertical": "Răsturnare verticală", "viewMode": "Mod de vizualizare", "share": "Distribuie", "showStroke": "Afișare selector culoare contur", "showBackground": "Afișare selector culoare fundal", "toggleTheme": "Comutare te<PERSON>ă", "personalLib": "Biblioteca personală", "excalidrawLib": "Biblioteca Excalidraw", "decreaseFontSize": "Micșorează dimensiunea fontului", "increaseFontSize": "Mărește dimensiunea fontului", "unbindText": "Deconectare text", "bindText": "Legare text de container", "createContainerFromText": "Încadrare text într-un container", "link": {"edit": "Editare URL", "editEmbed": "Editare URL și încorporare", "create": "Creare URL", "createEmbed": "Creare URL și încorporare", "label": "URL", "labelEmbed": "URL și încorporare", "empty": "Nu este setat niciun URL"}, "lineEditor": {"edit": "Editare linie", "exit": "Părăsire editor de linii"}, "elementLock": {"lock": "Blocare", "unlock": "Deblocare", "lockAll": "<PERSON><PERSON> toate", "unlockAll": "Deblocare toate"}, "statusPublished": "Publicat", "sidebarLock": "Păstrează deschisă bara laterală", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "Alegere culoare din pânză", "textToDiagram": "Text la diagramă", "prompt": "Solicitare"}, "library": {"noItems": "Niciun element adăugat încă...", "hint_emptyLibrary": "Selectează un element de pe pânză pentru a-l adăuga aici sau instalează o bibliotecă din depozitul public, de mai jos.", "hint_emptyPrivateLibrary": "Selectează un element de pe pânză pentru a-l adăuga aici."}, "buttons": {"clearReset": "Reset<PERSON>", "exportJSON": "Exportare la fișiere", "exportImage": "Exportare imagine...", "export": "<PERSON><PERSON><PERSON> în...", "copyToClipboard": "Co<PERSON>re în memoria temporară", "save": "<PERSON><PERSON><PERSON> în fișierul curent", "saveAs": "<PERSON><PERSON><PERSON> ca", "load": "Deschidere", "getShareableLink": "Obține URL partajabil", "close": "<PERSON><PERSON><PERSON><PERSON>", "selectLanguage": "Selectare limbă", "scrollBackToContent": "Derulare înapoi la conținut", "zoomIn": "Apropiere", "zoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetZoom": "Resetare transfocare", "menu": "<PERSON><PERSON>", "done": "Efectuat", "edit": "Edit", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "resetLibrary": "Resetare bibliotecă", "createNewRoom": "<PERSON><PERSON><PERSON> camer<PERSON> nouă", "fullScreen": "<PERSON><PERSON>ran complet", "darkMode": "<PERSON><PERSON>", "lightMode": "Mod luminos", "zenMode": "Mod zen", "objectsSnapMode": "Ancorare la obiecte", "exitZenMode": "Ieșire din modul zen", "cancel": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Eliminare", "embed": "Comutar<PERSON>", "publishLibrary": "Publicare", "submit": "Trimitere", "confirm": "Confirmare", "embeddableInteractionButton": "Clic pentru interacționare"}, "alerts": {"clearReset": "Această opțiune va șterge întreaga pânză. Confirmi?", "couldNotCreateShareableLink": "Nu s-a putut crea un URL partajabil.", "couldNotCreateShareableLinkTooBig": "Nu s-a putut crea un URL partajabil: scena este prea mare", "couldNotLoadInvalidFile": "Fișierul invalid nu a putut fi încărcat", "importBackendFailed": "Importarea de la nivel de server a eșuat.", "cannotExportEmptyCanvas": "Nu se poate exporta pânza goală.", "couldNotCopyToClipboard": "Nu s-a putut copia în memoria temporară.", "decryptFailed": "Datele nu au putut fi decriptate.", "uploadedSecurly": "Încărcarea a fost securizată prin criptare integrală, însemnând că serverul Excalidraw și terții nu pot citi conținutul.", "loadSceneOverridePrompt": "Încărcarea desenului extern va înlocui conținutul existent. Dorești să continui?", "collabStopOverridePrompt": "Oprirea sesiunii va suprascrie desenul anterior stocat local. Confirmi alegerea?\n\n(Dacă vrei să păstrezi desenul local, pur și simplu închide fila navigatorului în schimb.)", "errorAddingToLibrary": "Elementul nu a putut fi adăugat în bibliotecă", "errorRemovingFromLibrary": "Elementul nu a putut fi eliminat din bibliotecă", "confirmAddLibrary": "Această acțiune va adăuga {{numShapes}} formă(e) la biblioteca ta. Confirmi?", "imageDoesNotContainScene": "Această imagine nu pare să conțină date de scenă. Ai activat încorporarea scenei în timpul exportului?", "cannotRestoreFromImage": "Scena nu a putut fi restaurată din acest fișier de imagine", "invalidSceneUrl": "Scena nu a putut fi importată din URL-ul furnizat. Este fie incorect formată, fie nu conține date JSON Excalidraw valide.", "resetLibrary": "Această opțiune va elimina conținutul din bibliotecă. Confirmi?", "removeItemsFromsLibrary": "<PERSON><PERSON><PERSON> {{count}} element(e) din bibliotecă?", "invalidEncryptionKey": "Cheia de criptare trebuie să aibă 22 de caractere. Colaborarea în direct este dezactivată.", "collabOfflineWarning": "Nu este disponibilă nicio conexiune la internet.\nModificările nu vor fi salvate!"}, "errors": {"unsupportedFileType": "<PERSON><PERSON> <PERSON> ne<PERSON>.", "imageInsertError": "Imaginea nu a putut fi introdusă. Reîncearcă mai târziu...", "fileTooBig": "Fișierul este prea mare. Dimensiunea maximă permisă este de {{maxSize}}.", "svgImageInsertError": "Imaginea SVG nu a putut fi introdus. Marcajul SVG pare invalid.", "failedToFetchImage": "Preluarea imaginii a eșuat.", "invalidSVGString": "SVG invalid.", "cannotResolveCollabServer": "Nu a putut fi realizată conexiunea la serverul de colaborare. Reîncarcă pagina și încearcă din nou.", "importLibraryError": "Biblioteca nu a putut fi încărcată", "collabSaveFailed": "Nu s-a putut salva în baza de date la nivel de server. Dacă problemele persistă, ar trebui să salvezi fișierul la nivel local pentru a te asigura că nu îți pierzi munca.", "collabSaveFailed_sizeExceeded": "Nu s-a putut salva în baza de date la nivel de server, întrucât se pare că pânza este prea mare. Ar trebui să salvezi fișierul la nivel local pentru a te asigura că nu îți pierzi munca.", "imageToolNotSupported": "Imaginile sunt dezactivate.", "brave_measure_text_error": {"line1": "Se pare că folosești navigatorul Brave cu opțiunea <bold>strictă pentru blocarea amprentării</bold>.", "line2": "Acest lucru poate duce la întreruperea <bold>elementelor text</bold> din desene.", "line3": "Îți recomandăm ferm să dezactivezi această setare. Poți urma <link>acești pași</link> pentru a face acest lucru.", "line4": "Dacă dezactivarea acestei setări nu duce la remedierea afișării elementelor text, deschide un tichet de <issueLink>problemă</issueLink> pe pagina noastră de GitHub sau scrie-ne pe <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "Elementele încorporabile nu pot fi adăugate la bibliotecă.", "iframe": "Elementele iFrame nu pot fi adăugate la bibliotecă.", "image": "În curând vor putea fi adăugate imagini în bibliotecă!"}, "asyncPasteFailedOnRead": "Lipirea nu a putut fi efectuată (nu s-a putut citit din memoria temporară a sistemului).", "asyncPasteFailedOnParse": "Lipirea nu a putut fi efectuată.", "copyToSystemClipboardFailed": "Nu s-a putut copia în memoria temporară."}, "toolBar": {"selection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": "Introducere imagine", "rectangle": "Dreptunghi", "diamond": "Romb", "ellipse": "Elipsă", "arrow": "Săgeată", "line": "<PERSON><PERSON>", "freedraw": "Desenare", "text": "Text", "library": "Bibliotecă", "lock": "Menține activ instrumentul selectat după desenare", "penMode": "<PERSON><PERSON> – împiedic<PERSON> atingerea", "link": "Adăugare/actualizare URL pentru forma selectată", "eraser": "<PERSON><PERSON><PERSON>", "frame": "", "magicframe": "Structură-de-fire la cod", "embeddable": "Încorporare web", "laser": "Indicator laser", "hand": "<PERSON><PERSON><PERSON> (instrument de panoramare)", "extraTools": "", "mermaidToExcalidraw": "Mermaid la Excalidraw", "magicSettings": "Setări IA"}, "headings": {"canvasActions": "Acțiuni pentru pânză", "selectedShapeActions": "Acțiuni pentru forma selectată", "shapes": "Forme"}, "hints": {"canvasPanning": "Pentru a muta pânză, ține apăsată rotița mausului sau bara de spațiu sau folosește instrumentul în formă de mână", "linearElement": "<PERSON>ă clic pentru a crea mai multe puncte, glisea<PERSON><PERSON> pentru a forma o singură linie", "freeDraw": "Dă clic pe pânză și glisează cursorul, apoi eliberează-l când ai terminat", "text": "Sfat: poți adăuga text și dând dublu clic oriunde cu instrumentul de selecție", "embeddable": "Dă clic și trage pentru a crea un cod de încorporare de pagină web", "text_selected": "Dă dublu clic sau apasă tasta Enter pentru a edita textul", "text_editing": "Apasă tasta Escape sau Ctrl sau Cmd + Enter pentru a finaliza editarea", "linearElementMulti": "Dă clic pe ultimul punct sau apasă tasta Escape sau tasta Enter pentru a termina", "lockAngle": "Poți constrânge unghiul prin ținerea apăsată a tastei SHIFT", "resize": "Poți constrân<PERSON> prop<PERSON>le, ținând apăsată tasta SHIFT în timp ce redimensionezi,\nține apăsată tasta ALT pentru a redimensiona de la centru", "resizeImage": "Poți redimensiona liber ținând apăsată tasta SHIFT,\nține apăsată tasta ALT pentru a redimensiona din centru", "rotate": "Poți constrânge un<PERSON>rile, ținând apăsată tasta SHIFT în timp ce rotești", "lineEditor_info": "Ține apăsată tasta Ctrl sau Cmd și dă dublu clic sau apasă tasta Ctrl sau Cmd + Enter pentru a edita puncte", "lineEditor_pointSelected": "Apasă tasta Delete pentru a elimina punctele,\ncombinația de taste Ctrl sau Cmd + D pentru a le duplica sau glisează-le pentru a le schimba poziția", "lineEditor_nothingSelected": "Selectează un punct pentru a-l edita (ține apăsată tasta SHIFT pentru a selecta mai multe),\nsau ține apăsată tasta Alt și dă clic pentru a adăuga puncte noi", "placeImage": "Dă clic pentru a poziționa imaginea sau dă clic și glisează pentru a seta manual dimensiunea imaginii", "publishLibrary": "Publică propria bibliotecă", "bindTextToElement": "<PERSON><PERSON><PERSON> tasta Enter pentru a adăuga text", "deepBoxSelect": "Ține apăsată tasta Ctrl sau Cmd pentru a efectua selectarea de adâncime și pentru a preveni glisarea", "eraserRevert": "Ține apăsată tasta Alt pentru a anula elementele marcate pentru ștergere", "firefox_clipboard_write": "Această caracteristică poate fi probabil activată prin setarea preferinței „dom.events.asyncClipboard.clipboardItem” ca „true”. Pentru a schimba preferințele navigatorului în Firefox, accesează pagina „about:config”.", "disableSnapping": "Ține apăsat CtrlOrCmd pentru a dezactiva ancorarea"}, "canvasError": {"cannotShowPreview": "Nu se poate afișa previzualizarea", "canvasTooBig": "Pânza poate fi prea mare.", "canvasTooBigTip": "Sfat: încearcă să apropii puțin mai mult elementele cele mai îndepărtate."}, "errorSplash": {"headingMain": "A apărut o eroare. Încearcă <button>să reîncarci pagina</button>.", "clearCanvasMessage": "Dacă reîncărcarea nu funcționează, încearcă <button>să <PERSON><PERSON><PERSON> pân<PERSON></button>.", "clearCanvasCaveat": " Acest lucru va duce la pierderea progresului ", "trackedToSentry": "Eroarea cu identificatorul {{eventId}} a fost urmărită în sistemul nostru.", "openIssueMessage": "Am luat măsuri de precauție pentru a nu include informații despre scenă în eroare. Dacă scena nu este privată, oferă-ne mai multe informații în <button>monitorul nostru pentru erori</button>. Include informațiile de mai jos copiindu-le și lipindu-le în tichetul cu problemă de pe GitHub.", "sceneContent": "Conținutul scenei:"}, "roomDialog": {"desc_intro": "Poți invita alte persoane pentru a colabora la scena actuală.", "desc_privacy": "Nu te îngrijora. Sesiunea utilizează criptarea integrală, astfel încât orice desenezi va rămâne privat. Nici măcar serverul nostru nu va putea vedea pe ce ai lucrat.", "button_startSession": "Pornire sesiune", "button_stopSession": "<PERSON><PERSON><PERSON> se<PERSON>", "desc_inProgressIntro": "Sesiunea de colaborare în direct este în curs de desfășurare.", "desc_shareLink": "Distribuie acest URL persoanelor cu care dorești să colaborezi:", "desc_exitSession": "Oprirea sesiunii te va deconecta de la sală, însă vei putea lucra în continuare, pe plan local, cu scena. Reține că această opțiune nu va afecta alte persoane, iar acestea vor putea să colaboreze în continuare pe versiunea lor.", "shareTitle": "Alătură-te unei sesiuni de colaborare în direct pe Excalidraw"}, "errorDialog": {"title": "Eroare"}, "exportDialog": {"disk_title": "<PERSON><PERSON>e pe disc", "disk_details": "Exportă datele scenei pe un fișier din care poți importa mai târziu.", "disk_button": "<PERSON><PERSON><PERSON>", "link_title": "URL partajabil", "link_details": "Exportă ca URL doar în citire.", "link_button": "Exportare în URL", "excalidrawplus_description": "Salvează scena în spațiul de lucru Excalidraw+.", "excalidrawplus_button": "Exportare", "excalidrawplus_exportError": "Excalidraw+ nu a putut fi exportat în acest moment..."}, "helpDialog": {"blog": "Citește blogul nostru", "click": "clic", "deepSelect": "Selectare de adâncime", "deepBoxSelect": "Selectare de adâncime în casetă și prevenire glisare", "curvedArrow": "Săgeată curbată", "curvedLine": "<PERSON><PERSON>", "documentation": "Documentație", "doubleClick": "dublu clic", "drag": "glisare", "editor": "Editor", "editLineArrowPoints": "Editare puncte de săgeată/rând", "editText": "Editare text/adăugare etichetă", "github": "Ai întâmpinat o problemă? Trimite un raport", "howto": "Urmărește ghidurile noastre", "or": "sau", "preventBinding": "Împiedică legarea săgeții", "tools": "Instrumente", "shortcuts": "Comenzi rapide de la tastatură", "textFinish": "Finalizează editarea (editor de text)", "textNewLine": "Adaugă o linie nouă (editor de text)", "title": "<PERSON><PERSON><PERSON>", "view": "Vizualizare", "zoomToFit": "Transfocare pentru a cuprinde totul", "zoomToSelection": "Transfocare la selecție", "toggleElementLock": "Blocare/deblocare selecție", "movePageUpDown": "Deplasare pagină sus/jos", "movePageLeftRight": "Deplasare pagină stânga/dreapta"}, "clearCanvasDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "publishDialog": {"title": "Publicare bibliotecă", "itemName": "Denumirea elementului", "authorName": "Numele autorului", "githubUsername": "Numele de utilizator GitHub", "twitterUsername": "Numele de utilizator Twitter", "libraryName": "Denumirea bibliotecii", "libraryDesc": "Descrierea biblio<PERSON>ii", "website": "Pagină de internet", "placeholder": {"authorName": "Numele sau numele tău de utilizator", "libraryName": "Numele bibliotecii tale", "libraryDesc": "Descrierea bibliotecii tale pentru a ajuta oamenii să înțeleagă utilizarea acesteia", "githubHandle": "Numele de utilizator GitHub (opțional), pentru a putea edita biblioteca odată ce este trimisă spre revizuire", "twitterHandle": "Numele de utilizator Twitter (opțional), pentru a indica sursa la promovarea pe Twitter", "website": "Trimitere către pagina ta personală de internet sau altundeva (opțional)"}, "errors": {"required": "Obligator<PERSON>", "website": "Introdu un URL valid"}, "noteDescription": "Trimite-ți biblioteca pentru a fi inclusă în <link>depozitul de biblioteci publice</link> în vederea utilizării de către alte persoane în desenele lor.", "noteGuidelines": "Biblioteca trebuie aprobată manual mai întâi. Citește <link>orientările</link> înainte de trimitere. Vei avea nevoie de un cont GitHub pentru a comunica și efectua modificări, dacă este cazul, însă nu este strict necesar.", "noteLicense": "Prin trimiterea bibliotecii, ești de acord că aceasta va fi publicată sub <link>Licența MIT, </link>care, pe scurt, înseamnă că oricine o poate folosi fără restricții.", "noteItems": "Fiecare element din bibliotecă trebuie să aibă propriul nume astfel încât să fie filtrabil. Următoarele elemente din bibliotecă vor fi incluse:", "atleastOneLibItem": "Selectează cel puțin un element din bibliotecă pentru a începe", "republishWarning": "Observație: unele dintre elementele selectate sunt marcate ca fiind deja publicate/trimise. Ar trebui să retrimiți elemente numai atunci când actualizezi o trimitere sau o bibliotecă existentă."}, "publishSuccessDialog": {"title": "Bibliotecă trimisă", "content": "<PERSON><PERSON><PERSON> m<PERSON>, {{authorName}}. Biblioteca a fost trimisă spre revizuire. Poți urmări starea <link>aici</link>"}, "confirmDialog": {"resetLibrary": "Resetare bibliotecă", "removeItemsFromLib": "Elimină elementele selectate din bibliotecă"}, "imageExportDialog": {"header": "Exportare imagine", "label": {"withBackground": "Fundal", "onlySelected": "<PERSON><PERSON>i <PERSON>ț<PERSON>", "darkMode": "<PERSON><PERSON>", "embedScene": "Încorporare scenă", "scale": "Sc<PERSON><PERSON>", "padding": "Spațiere"}, "tooltip": {"embedScene": "Datele scenei vor fi salvate în fișierul PNG/SVG exportat, astfel că scena va putea fi restaurată din acesta.\nVa crește dimensiunea fișierului exportat."}, "title": {"exportToPng": "Exportare ca PNG", "exportToSvg": "Exportare ca SVG", "copyPngToClipboard": "Copiere PNG în memoria temporară"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Co<PERSON>re în memoria temporară"}}, "encrypted": {"tooltip": "Desenele tale sunt criptate integral, astfel că serverele Excalidraw nu le vor vedea niciodată.", "link": "Articol de blog pe criptarea integrală din Excalidraw"}, "stats": {"angle": "<PERSON><PERSON><PERSON>", "element": "Element", "elements": "Elemente", "height": "Înălțime", "scene": "Scenă", "selected": "Selectate", "storage": "Stocare", "title": "Statistici pentru pasionați", "total": "Total", "version": "Versiune", "versionCopy": "Clic pentru copiere", "versionNotAvailable": "Versiune indisponibilă", "width": "Lățime"}, "toast": {"addedToLibrary": "Adăugat în bibliotecă", "copyStyles": "Stiluri copiate.", "copyToClipboard": "Copiat în memoria temporară.", "copyToClipboardAsPng": "S-a copiat {{exportSelection}} în memoria temporară sub formă de PNG\n({{exportColorScheme}})", "fileSaved": "<PERSON><PERSON><PERSON> salvat.", "fileSavedToFilename": "Salvat în {filename}", "canvas": "pânza", "selection": "selecția", "pasteAsSingleElement": "Folosește {{shortcut}} pentru a insera ca un singur element\nsau insera într-un editor de text existent", "unableToEmbed": "Încorporarea acestui URL nu este permisă momentan. Deschideți un tichet cu probleme pe GitHub pentru a solicita adăugarea acestui URL în lista albă", "unrecognizedLinkFormat": "URL-ul pe care l-ai încorporat nu coincide cu formatul așteptat. Încearcă să lipești șirul „de încorporat” furnizat de pagina sursă"}, "colors": {"transparent": "Transparent", "black": "Negru", "white": "Alb", "red": "<PERSON><PERSON><PERSON><PERSON>", "pink": "<PERSON><PERSON>", "grape": "Struguriu", "violet": "Violet", "gray": "<PERSON><PERSON>", "blue": "Albastru", "cyan": "<PERSON><PERSON>", "teal": "<PERSON>an-verde", "green": "Verde", "yellow": "<PERSON><PERSON><PERSON>", "orange": "Portocal<PERSON>", "bronze": "Bronz"}, "welcomeScreen": {"app": {"center_heading": "Toate datele tale sunt salvate local în navigatorul tău.", "center_heading_plus": "Ai vrut să mergi în schimb la Excalidraw+?", "menuHint": "<PERSON><PERSON><PERSON>, preferințe, limbi, ..."}, "defaults": {"menuHint": "<PERSON>rta<PERSON>, preferințe și mai multe...", "center_heading": "Diagrame. Făcute. Simple.", "toolbarHint": "Alege un instrument și începe să desenezi!", "helpHint": "Comenzi rapide și ajutor"}}, "colorPicker": {"mostUsedCustomColors": "Cele mai utilizate culori personalizate", "colors": "<PERSON><PERSON><PERSON>", "shades": "Nuanțe", "hexCode": "Cod hexa", "noShades": "Nu este disponibilă nicio nuanță pentru această culoare"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Exportare ca imagine", "button": "Exportare ca imagine", "description": "Exportă datele scenei ca fișier din care poți importa mai târziu."}, "saveToDisk": {"title": "<PERSON><PERSON>e pe disc", "button": "<PERSON><PERSON>e pe disc", "description": "Exportă datele scenei pe un fișier din care poți importa mai târziu."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Exportare în Excalidraw+", "description": "Salvează scena în spațiul de lucru Excalidraw+."}}, "modal": {"loadFromFile": {"title": "Încărcare din fișier", "button": "Încărcare din fișier", "description": "Încărcarea dintr-un fișier va <bold>înlocui conținutul existent</bold>.<br></br><PERSON><PERSON><PERSON> face mai înt<PERSON>i o copie de rezervă a desenului folosind una dintre opțiunile de mai jos."}, "shareableLink": {"title": "Încărcare din lnk", "button": "Înlocuiește conținutul meu", "description": "Încărcarea unui desen extern va <bold>înlocui conținutul existent</bold>.<br></br><PERSON><PERSON><PERSON> face mai întâi o copie de rezervă a desenului folosind una dintre opțiunile de mai jos."}}}, "mermaid": {"title": "Mermaid la Excalidraw", "button": "Introducere", "description": "În prezent, numai <flowchartLink>Organigramele</flowchartLink>, <sequenceLink>Diagramele de secvență</sequenceLink> și <classLink>Diagramele de clasă</classLink> sunt acceptate. Celelalte tipuri vor fi redate ca imagine în Excalidraw.", "syntax": "Sintaxă Mermaid", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}