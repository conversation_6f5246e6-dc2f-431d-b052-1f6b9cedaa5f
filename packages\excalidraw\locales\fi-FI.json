{"labels": {"paste": "Liit<PERSON>", "pasteAsPlaintext": "Liitä pelkkänä tekstinä", "pasteCharts": "<PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON> kaikki", "multiSelect": "Lisää kohde valintaan", "moveCanvas": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "cut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Ko<PERSON>i", "copyAsPng": "Ko<PERSON>i leikepöydälle PNG-tiedostona", "copyAsSvg": "Kopioi leikepöydälle SVG-tiedostona", "copyText": "<PERSON><PERSON><PERSON>", "copySource": "", "convertToCode": "", "bringForward": "<PERSON><PERSON>", "sendToBack": "<PERSON><PERSON>", "bringToFront": "<PERSON><PERSON> et<PERSON>", "sendBackward": "<PERSON><PERSON>", "delete": "Poista", "copyStyles": "Ko<PERSON>i tyyli", "pasteStyles": "<PERSON><PERSON><PERSON> t<PERSON>i", "stroke": "Piirto", "background": "Tausta", "fill": "Täyttö", "strokeWidth": "<PERSON><PERSON><PERSON> leveys", "strokeStyle": "Viivan tyyli", "strokeStyle_solid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dashed": "Katkov<PERSON>va", "strokeStyle_dotted": "Pisteviiva", "sloppiness": "Viivan tarkkuus", "opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textAlign": "<PERSON><PERSON><PERSON>", "edges": "<PERSON><PERSON><PERSON>", "sharp": "Terävä", "round": "Pyöristetty", "arrowheads": "Nuolenkärjet", "arrowhead_none": "<PERSON><PERSON> mit<PERSON>n", "arrowhead_arrow": "<PERSON><PERSON><PERSON>", "arrowhead_bar": "Tasapää", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "<PERSON><PERSON><PERSON>", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fontFamily": "Kirjasintyyppi", "addWatermark": "Lisää \"Tehty Excalidrawilla\"", "handDrawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "small": "<PERSON><PERSON>", "medium": "Keskikoko", "large": "<PERSON><PERSON>", "veryLarge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hachure": "Vinoviivoitus", "zigzag": "", "crossHatch": "Ristiviivoitus", "thin": "<PERSON><PERSON>", "bold": "Lihavoitu", "left": "<PERSON><PERSON><PERSON>", "center": "Keskitä", "right": "<PERSON><PERSON><PERSON>", "extraBold": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "architect": "Arkkitehti", "artist": "<PERSON><PERSON><PERSON><PERSON>", "cartoonist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorPicker": "<PERSON><PERSON><PERSON> vali<PERSON>", "canvasColors": "Käytössä p<PERSON>ella", "canvasBackground": "<PERSON><PERSON><PERSON><PERSON><PERSON> tausta", "drawingCanvas": "Piirtoalue", "layers": "Tasot", "actions": "<PERSON><PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON>", "liveCollaboration": "Live Yhteistyö...", "duplicateSelection": "<PERSON><PERSON>", "untitled": "Nimetön", "name": "<PERSON><PERSON>", "yourName": "<PERSON><PERSON><PERSON>", "madeWithExcalidraw": "Tehty Excalidrawilla", "group": "<PERSON><PERSON><PERSON><PERSON><PERSON> valinta", "ungroup": "<PERSON>ura valittu r<PERSON>", "collaborators": "Yhteistyökumppanit", "showGrid": "Näytä ruud<PERSON>ko", "addToLibrary": "Lisää kirjastoon", "removeFromLibrary": "Poista kirjastosta", "libraryLoadingMessage": "<PERSON><PERSON><PERSON> k<PERSON>…", "libraries": "<PERSON><PERSON><PERSON>", "loadingScene": "Ladataan työtä…", "align": "Ta<PERSON><PERSON>", "alignTop": "<PERSON><PERSON><PERSON>", "alignBottom": "<PERSON><PERSON><PERSON> alas", "alignLeft": "<PERSON><PERSON><PERSON> vasemmalle", "alignRight": "<PERSON><PERSON><PERSON>", "centerVertically": "Keskitä pystysuunnassa", "centerHorizontally": "Keskitä vaakasuunnassa", "distributeHorizontally": "Jaa vaakasu<PERSON>nassa", "distributeVertically": "<PERSON>aa pys<PERSON><PERSON><PERSON>nassa", "flipHorizontal": "Käännä vaakasuunnassa", "flipVertical": "Käännä pysty<PERSON>unnassa", "viewMode": "<PERSON><PERSON><PERSON><PERSON>", "share": "Jaa", "showStroke": "Näytä viivan värin valitsin", "showBackground": "Näytä taustav<PERSON><PERSON> valitsin", "toggleTheme": "<PERSON><PERSON><PERSON><PERSON> teema", "personalLib": "<PERSON><PERSON>", "excalidrawLib": "Excalidraw kirjasto", "decreaseFontSize": "Pienennä kirjasink<PERSON>a", "increaseFontSize": "<PERSON><PERSON><PERSON><PERSON>", "unbindText": "Irroita teksti", "bindText": "Kiinnitä teksti säiliöön", "createContainerFromText": "", "link": {"edit": "Muokkaa link<PERSON>ä", "editEmbed": "", "create": "<PERSON><PERSON>", "createEmbed": "", "label": "<PERSON><PERSON>", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "Muokkaa riviä", "exit": "<PERSON><PERSON><PERSON> rivied<PERSON>"}, "elementLock": {"lock": "Lukitse", "unlock": "Po<PERSON> lukitus", "lockAll": "Lukitse kaikki", "unlockAll": "Poista lukitus kaikista"}, "statusPublished": "Julkaistu", "sidebarLock": "<PERSON><PERSON><PERSON> sivu<PERSON>ki avoinna", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "Kirjastossa ei ole vielä yhtään kohdetta...", "hint_emptyLibrary": "Valitse lisättävä kohde pii<PERSON>, tai asenna alta julkinen kirjasto.", "hint_emptyPrivateLibrary": "Valitse lisättävä kohde piirtoalueelta."}, "buttons": {"clearReset": "Tyhjennä piirtoalue", "exportJSON": "<PERSON><PERSON>", "exportImage": "Vie kuva...", "export": "<PERSON><PERSON><PERSON>...", "copyToClipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "save": "<PERSON><PERSON><PERSON>", "saveAs": "<PERSON><PERSON><PERSON>", "load": "<PERSON><PERSON>", "getShareableLink": "<PERSON><PERSON> jae<PERSON><PERSON>", "close": "Sulje", "selectLanguage": "Valitse kieli", "scrollBackToContent": "Näytä si<PERSON>ältö", "zoomIn": "Lähennä", "zoomOut": "Loitonna", "resetZoom": "<PERSON><PERSON><PERSON> taso", "menu": "<PERSON><PERSON><PERSON>", "done": "Val<PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON>", "resetLibrary": "Tyhje<PERSON><PERSON> kir<PERSON>to", "createNewRoom": "<PERSON><PERSON> huone", "fullScreen": "<PERSON><PERSON>", "darkMode": "Tumma tila", "lightMode": "<PERSON><PERSON><PERSON> tila", "zenMode": "Zen-tila", "objectsSnapMode": "", "exitZenMode": "<PERSON><PERSON><PERSON> zen-tilasta", "cancel": "Peruuta", "clear": "Pyyhi", "remove": "Poista", "embed": "", "publishLibrary": "Julkai<PERSON>", "submit": "Lähetä", "confirm": "Vahvista", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "Tämä tyhjentää koko piirtoalueen. Jatketaanko?", "couldNotCreateShareableLink": "<PERSON><PERSON><PERSON><PERSON> linkin luominen epäon<PERSON>.", "couldNotCreateShareableLinkTooBig": "Jaettavaa link<PERSON>ä ei voitu luoda: teos on liian suuri", "couldNotLoadInvalidFile": "Virheellistä tiedostoa ei voitu avata", "importBackendFailed": "Palvelimelta tuonti ep<PERSON>.", "cannotExportEmptyCanvas": "Tyhjää piirtoaluetta ei voi viedä.", "couldNotCopyToClipboard": "Leikepöydälle vieminen epäonnistui.", "decryptFailed": "Salauksen purkaminen epäonnistui.", "uploadedSecurly": "Lähetys on turvattu päästä-päähän-salauksella. Excalidrawin palvelin ja kolmannet osapuolet eivät voi lukea sisältöä.", "loadSceneOverridePrompt": "Ulkopuolisen piirroksen lataaminen kor<PERSON>a nykyisen si<PERSON>ältösi. Jatketaanko?", "collabStopOverridePrompt": "Istunnon lopettaminen kor<PERSON> a<PERSON>, pai<PERSON>lisesti tallennetun piirust<PERSON>. Jatketaanko?\n\n(<PERSON><PERSON> halu<PERSON> säilyttää paikallisesti tallennetun piirust<PERSON>, sulje selaimen välilehti lopettamisen sijaan.)", "errorAddingToLibrary": "Kohdetta ei voitu lisätä kirjastoon", "errorRemovingFromLibrary": "Ko<PERSON><PERSON><PERSON> ei voitu poistaa kirjas<PERSON>ta", "confirmAddLibrary": "Tä<PERSON>ä lisää {{numShapes}} muoto<PERSON> kir<PERSON>. Jatketaanko?", "imageDoesNotContainScene": "Tämä kuva ei näytä sisältävän piirrostietoja. <PERSON><PERSON><PERSON> otta<PERSON> käyttöön piirroksen tallennuksen viennin aikana?", "cannotRestoreFromImage": "Teosta ei voitu palauttaa tästä kuvatiedostosta", "invalidSceneUrl": "Teosta ei voitu tuoda annetusta URL-osoitteesta. Tallenne on vioittunut, tai osoitteessa ei ole Excalidraw JSON-dataa.", "resetLibrary": "Tämä tyhjentää kirjastosi. Jatketaanko?", "removeItemsFromsLibrary": "Poista {{count}} k<PERSON><PERSON><PERSON> k<PERSON>?", "invalidEncryptionKey": "Salausavaimen on oltava 22 merkkiä pitkä. Live-yhteistyö ei ole käytössä.", "collabOfflineWarning": "Internet-yhteyttä ei ole saatavilla.\nMuutoksiasi ei tallenneta!"}, "errors": {"unsupportedFileType": "Tiedostotyyppiä ei tueta.", "imageInsertError": "<PERSON><PERSON> l<PERSON>n epäonnistui. Yritä <PERSON> uude<PERSON>...", "fileTooBig": "<PERSON><PERSON><PERSON><PERSON> on liian suuri. <PERSON><PERSON><PERSON> sallittu koko on {{maxSize}}.", "svgImageInsertError": "SVG- kuvaa ei voitu lisätä. Tiedoston SVG-sisältö näyttää virheelliseltä.", "failedToFetchImage": "", "invalidSVGString": "Virheellinen SVG.", "cannotResolveCollabServer": "Yht<PERSON>den muodostaminen collab-palvelimeen epäonnistui. Virkistä sivu ja yritä uudelleen.", "importLibraryError": "<PERSON><PERSON><PERSON><PERSON> ep<PERSON>", "collabSaveFailed": "<PERSON>i voitu tallentaan palvelimen tietokantaan. <PERSON><PERSON> es<PERSON>, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, että et menetä työtäsi.", "collabSaveFailed_sizeExceeded": "<PERSON>i voitu tallentaan palvelimen tietokantaan. <PERSON><PERSON> es<PERSON>, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, että et menetä työtäsi.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "<PERSON><PERSON><PERSON>", "image": "Lisää kuva", "rectangle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diamond": "Vinoneliö", "ellipse": "<PERSON><PERSON><PERSON>", "arrow": "<PERSON><PERSON><PERSON>", "line": "Viiva", "freedraw": "Piirrä", "text": "<PERSON><PERSON><PERSON>", "library": "<PERSON><PERSON><PERSON><PERSON>", "lock": "Pidä valittu työkalu aktiivisena piirron jälkeen", "penMode": "Kynätila - estä kosketus", "link": "Lisää/päivitä linkki valitulle muodolle", "eraser": "Poistotyökalu", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "Kä<PERSON> (panning-työkalu)", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "selectedShapeActions": "<PERSON><PERSON><PERSON> muodon toim<PERSON>ot", "shapes": "<PERSON><PERSON><PERSON>"}, "hints": {"canvasPanning": "Piirtoalueen liikuttamiseksi pidä hiiren pyörää tai välilyöntiä pohjassa tai käytä käsityökalua", "linearElement": "Klikkaa piirtääks<PERSON> useamp<PERSON> piste, raahaa piirtääksesi yksittäinen viiva", "freeDraw": "<PERSON>a ja raahaa, p<PERSON><PERSON>st<PERSON> irti kun olet valmis", "text": "Vinkki: voit myös lisätä tekstiä kaksoisnap<PERSON>utt<PERSON><PERSON> mihin tahansa valinta<PERSON>a", "embeddable": "", "text_selected": "Kaksoisnapsauta tai paina ENTER muokataksesi tekstiä", "text_editing": "Paina Escape tai CtrlOrCmd+ENTER lopetta<PERSON><PERSON> muokka<PERSON>sen", "linearElementMulti": "<PERSON><PERSON><PERSON> klik<PERSON>amalla viimeistä pistettä, painamalla Escape- tai Enter-näppäintä", "lockAngle": "Voit rajoittaa kulmaa pitämällä SHIFT-näppäintä alaspainettuna", "resize": "Voit rajoittaa mittasuhteet pitämällä SHIFT-näppäintä alaspainettuna kun muutat kokoa, pidä ALT-näppäintä alaspainettuna muuttaaksesi kokoa keskipisteen suhteen", "resizeImage": "Voit muuttaa kokoa vapaasti pitämällä SHIFTiä pohjassa, pidä ALT pohjassa muuttaaksesi kokoa keskipisteen ympäri", "rotate": "Voit rajoit<PERSON>a kulman pitämällä SHIFT pohjassa pyörittäessäsi", "lineEditor_info": "Pidä CtrlOrCmd pohjassa ja kaksoisnap<PERSON>uta tai paina CtrlOrCmd + Enter muokataksesi pisteitä", "lineEditor_pointSelected": "Poista piste(et) pain<PERSON><PERSON> delete, monista painamalla CtrlOrCmd+D, tai liikuta raaha<PERSON>lla", "lineEditor_nothingSelected": "Valitse muokattava piste (monivalinta pitämällä SHIFT pohjassa), tai paina Alt ja klikkaa lisätäksesi uusia pisteitä", "placeImage": "<PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON> kuvan, tai klikkaa ja raahaa as<PERSON><PERSON><PERSON> sen koon manua<PERSON>esti", "publishLibrary": "<PERSON><PERSON><PERSON> o<PERSON> k<PERSON>", "bindTextToElement": "Lisää teksti<PERSON> enter", "deepBoxSelect": "Käytä syvävalintaa ja estä raahaus painamalla CtrlOrCmd", "eraserRevert": "Pidä <PERSON>, kumotaksesi merkittyjen elementtien poistamisen", "firefox_clipboard_write": "Tämä ominaisuus voidaan todennäköisesti ottaa k<PERSON>töö<PERSON> asettamal<PERSON> \"dom.events.asyncClipboard.clipboardItem\" kohta \"true\":ksi. Vaihtaaksesi selaimen kohdan Firefoxissa, käy \"about:config\" sivulla.", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Esikatselua ei voitu n<PERSON>ää", "canvasTooBig": "Piirtoalue saattaa olla liian suuri.", "canvasTooBigTip": "Vinkki: yritä siirtää kaukaisimpia elementtejä hieman lähemmäs toisi<PERSON>."}, "errorSplash": {"headingMain": "Tapahtui virhe. Yritä <button>sivun lataamista uudelle<PERSON>.</button>", "clearCanvasMessage": "Mi<PERSON><PERSON><PERSON> sivun lataaminen uudelleen ei auta, yritä <button>tyhjent<PERSON>ä piirtoalue.</button>", "clearCanvasCaveat": " <PERSON><PERSON><PERSON><PERSON> johtaa työn men<PERSON> ", "trackedToSentry": "<PERSON><PERSON><PERSON> t<PERSON> {{eventId}} tall<PERSON><PERSON><PERSON> järjestelmäämme.", "openIssueMessage": "Olimme varovaisia emmekä sisällyttäneet tietoa piirroksestasi virheeseen. Mik<PERSON>li piirroksesi ei ole y<PERSON><PERSON>, harkitsethan kertovasi meille <button>virheenseurantajärjestelmässämme.</button> Sisällytä alla olevat tiedot kopioimalla ne GitHub-ongelmaan.", "sceneContent": "<PERSON><PERSON><PERSON><PERSON> tiedot:"}, "roomDialog": {"desc_intro": "Voit kutsua ihmisiä piirrokseesi tekemään yhteistyötä kanssasi.", "desc_privacy": "<PERSON><PERSON><PERSON>, istun<PERSON> k<PERSON> päästä-päähän-sa<PERSON>, joten mitä tahan<PERSON> p<PERSON>, se pysyy salassa. Edes palvelimemme eivät näe mitä keksit.", "button_startSession": "<PERSON><PERSON><PERSON> is<PERSON>", "button_stopSession": "<PERSON><PERSON><PERSON> istunto", "desc_inProgressIntro": "<PERSON><PERSON><PERSON><PERSON> is<PERSON>to on nyt käynnissä.", "desc_shareLink": "Jaa tämä linkki kenelle ta<PERSON>, jonka kanssa haluat tehdä yhteistyötä:", "desc_exitSession": "<PERSON><PERSON><PERSON> p<PERSON>yt<PERSON>äminen katkaisee yhteyden huo<PERSON>, mutta voit vielä jatkaa työskentelyä paikallisesti. <PERSON><PERSON><PERSON>, että tämä ei vaikuta muihin käyttäjiin ja he voivat jatkaa oman versionsa parissa työskentelyä.", "shareTitle": "Li<PERSON>idraw live-yhteistyöistuntoon"}, "errorDialog": {"title": "<PERSON><PERSON><PERSON>"}, "exportDialog": {"disk_title": "<PERSON><PERSON><PERSON> le<PERSON>", "disk_details": "<PERSON><PERSON> työn tiedot tied<PERSON><PERSON>, josta sen voi tuoda my<PERSON><PERSON>.", "disk_button": "<PERSON><PERSON><PERSON>", "link_title": "<PERSON><PERSON><PERSON><PERSON>", "link_details": "<PERSON><PERSON> <PERSON> luku -link<PERSON><PERSON>.", "link_button": "<PERSON><PERSON>", "excalidrawplus_description": "<PERSON><PERSON><PERSON> teos <PERSON>+ tilaan.", "excalidrawplus_button": "Vie", "excalidrawplus_exportError": "Ei voitu viedä Excalidraw+-palveluun tällä hetkellä..."}, "helpDialog": {"blog": "<PERSON><PERSON> blogiamme", "click": "klikkaa", "deepSelect": "Syvävalinta", "deepBoxSelect": "Käytä s<PERSON>ävalintaa ja estä raahaus", "curvedArrow": "<PERSON><PERSON><PERSON> nuoli", "curvedLine": "Kaareva viiva", "documentation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doubleClick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drag": "vedä", "editor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editLineArrowPoints": "", "editText": "", "github": "Löysitkö ongelman? <PERSON><PERSON>", "howto": "Seuraa oppaitamme", "or": "tai", "preventBinding": "Estä nuolten kiinnitys", "tools": "Työkalut", "shortcuts": "Pikanäppäimet", "textFinish": "<PERSON><PERSON><PERSON> muokka<PERSON> (tekstieditori)", "textNewLine": "Lisää uusi rivi (tekstieditori)", "title": "<PERSON><PERSON><PERSON>", "view": "Näkymä", "zoomToFit": "Näytä kaikki elementit", "zoomToSelection": "Näytä valinta", "toggleElementLock": "<PERSON><PERSON><PERSON><PERSON> / poista lukitus valinta", "movePageUpDown": "<PERSON><PERSON><PERSON><PERSON> sivua ylö<PERSON>/alas", "movePageLeftRight": "Siirrä sivua vasemmalle/oikealle"}, "clearCanvasDialog": {"title": "Pyyhi piirtoalue"}, "publishDialog": {"title": "<PERSON><PERSON><PERSON>", "itemName": "<PERSON><PERSON><PERSON> nimi", "authorName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "githubUsername": "GitHub-käyttäjätunnus", "twitterUsername": "Twitter-käyttäjätunnus", "libraryName": "<PERSON><PERSON><PERSON><PERSON>", "libraryDesc": "<PERSON><PERSON><PERSON><PERSON>", "website": "Verkkosivu", "placeholder": {"authorName": "Nimesi tai käyttäjänimesi", "libraryName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "libraryDesc": "<PERSON><PERSON><PERSON><PERSON>, joka auttaa ihmisi<PERSON> ymmärtämään sen käyttötarkoitukset", "githubHandle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>), jotta voit muokata kirjastoa sen jälkeen kun se on lähetetty tarkastettavaksi", "twitterHandle": "Twitter-tunnus (<PERSON><PERSON><PERSON><PERSON>), jotta tiedämme ketä kiittää kun viestimme Twitterissä", "website": "<PERSON><PERSON> henkilö<PERSON><PERSON><PERSON> verkkosivustollesi tai muualle (valinnainen)"}, "errors": {"required": "<PERSON><PERSON><PERSON>", "website": "Syötä oikeamuotoinen URL-osoite"}, "noteDescription": "Lä<PERSON><PERSON><PERSON> kir<PERSON>, jotta se <PERSON>aan sisällyttää <link>jul<PERSON><PERSON>a kirjastolistauksessa</link>muiden käyttöön omis<PERSON> p<PERSON>.", "noteGuidelines": "<PERSON><PERSON><PERSON><PERSON> on ensin hyväksyttävä manuaalisesti. Ole hyvä ja lue <link>oh<PERSON><PERSON></link> ennen lähettämistä. Tarvitset GitHub-tilin, jotta voit viestiä ja tehdä muutoksia pyydettäessä, mutta se ei ole ehdottoman välttämätöntä.", "noteLicense": "Lähettämällä hyväksyt että kirjas<PERSON> jul<PERSON> <link>MIT-lisenssin </link>alla, mikä lyhyesti antaa muiden käyttää sitä ilman rajoituksia.", "noteItems": "Jo<PERSON><PERSON><PERSON> kirjaston kohteella on oltava oma nimensä suodatusta varten. Se<PERSON><PERSON>t kirjaston kohteet sisältyvät:", "atleastOneLibItem": "Valitse vähintään yksi kir<PERSON>ton kohde al<PERSON>aksesi", "republishWarning": "Huom! Osa valituista kohteista on merkitty jo julkaistu/lähetetyiksi. Lähetä kohteita uudelleen vain päivitettäessä olemassa olevaa kirjastoa tai ehdotusta."}, "publishSuccessDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON> {{author<PERSON><PERSON>}}. <PERSON><PERSON><PERSON><PERSON><PERSON> on lähetetty tarkistettavaksi. Voit seurata sen tilaa<link>täällä</link>"}, "confirmDialog": {"resetLibrary": "Tyhje<PERSON><PERSON> kir<PERSON>to", "removeItemsFromLib": "Poista valitut kohteet kirjastosta"}, "imageExportDialog": {"header": "", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "", "scale": "", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "Piirroksesi ovat päästä-päähän-salattuja, joten Excalidrawin palvelimet eivät koskaan näe niitä.", "link": "Blogiartikkeli päästä päähän -salauksesta Excalidraw:ssa"}, "stats": {"angle": "<PERSON><PERSON>", "element": "<PERSON><PERSON><PERSON>", "elements": "Elementit", "height": "<PERSON><PERSON><PERSON>", "scene": "Te<PERSON>", "selected": "Valitut", "storage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Tilastoja nörteille", "total": "Yhteensä", "version": "Versio", "versionCopy": "Klikkaa kop<PERSON>idaksesi", "versionNotAvailable": "Versio ei saatavilla", "width": "<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Lisätty kirjastoon", "copyStyles": "Tyylit kopioitiin.", "copyToClipboard": "<PERSON><PERSON><PERSON><PERSON>.", "copyToClipboardAsPng": "Kopioitiin {{exportSelection}} leikepöydälle PNG:nä\n({{exportColorScheme}})", "fileSaved": "<PERSON><PERSON><PERSON><PERSON> tall<PERSON>.", "fileSavedToFilename": "<PERSON><PERSON><PERSON><PERSON> koh<PERSON>seen {filename}", "canvas": "piirt<PERSON><PERSON>", "selection": "valinta", "pasteAsSingleElement": "Käytä {{shortcut}} liittääksesi yhtenä elementtinä,\ntai liittääksesi olemassa olevaan tekstieditoriin", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Läpinäkyvä", "black": "", "white": "", "red": "", "pink": "", "grape": "", "violet": "", "gray": "", "blue": "", "cyan": "", "teal": "", "green": "", "yellow": "", "orange": "", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "<PERSON><PERSON><PERSON> on tallennettu paikallisesti se<PERSON>mellesi.", "center_heading_plus": "<PERSON><PERSON>t<PERSON> sen sijaan mennä Excalidraw+:aan?", "menuHint": "<PERSON><PERSON>, as<PERSON><PERSON><PERSON>, kielet, ..."}, "defaults": {"menuHint": "<PERSON><PERSON>, asetukset ja lisää...", "center_heading": "Kaaviot. Tehty. Yksinkertaiseksi.", "toolbarHint": "Valitse työkalu ja aloita piirt<PERSON>minen!", "helpHint": "Pikanäppäimet & ohje"}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}