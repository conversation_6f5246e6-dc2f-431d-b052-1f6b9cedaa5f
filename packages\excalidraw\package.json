{"name": "@excalidraw/excalidraw", "version": "0.18.0", "type": "module", "types": "./dist/types/excalidraw/index.d.ts", "main": "./dist/prod/index.js", "module": "./dist/prod/index.js", "exports": {"./common/*": {"types": "./dist/types/common/src/*.d.ts"}, "./element/*": {"types": "./dist/types/element/src/*.d.ts"}, "./math/*": {"types": "./dist/types/math/src/*.d.ts"}, "./utils/*": {"types": "./dist/types/utils/src/*.d.ts"}, "./*": {"types": "./dist/types/excalidraw/*.d.ts"}, "./index.css": {"development": "./dist/dev/index.css", "production": "./dist/prod/index.css"}, ".": {"types": "./dist/types/excalidraw/index.d.ts", "development": "./dist/dev/index.js", "production": "./dist/prod/index.js", "default": "./dist/prod/index.js"}}, "files": ["dist/*"], "publishConfig": {"access": "public"}, "description": "Excalidraw as a React component", "license": "MIT", "keywords": ["excalidraw", "excalidraw-embed", "react", "npm", "npm excalidraw"], "browserslist": {"production": [">0.2%", "not dead", "not ie <= 11", "not op_mini all", "not safari < 12", "not kaios <= 2.5", "not edge < 79", "not chrome < 70", "not and_uc < 13", "not samsung < 10"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "peerDependencies": {"react": "^17.0.2 || ^18.2.0 || ^19.0.0", "react-dom": "^17.0.2 || ^18.2.0 || ^19.0.0"}, "dependencies": {"@braintree/sanitize-url": "6.0.2", "@excalidraw/laser-pointer": "1.3.1", "@excalidraw/mermaid-to-excalidraw": "1.1.2", "@excalidraw/random-username": "1.1.0", "@radix-ui/react-popover": "1.1.6", "@radix-ui/react-tabs": "1.1.3", "browser-fs-access": "0.29.1", "canvas-roundrect-polyfill": "0.0.1", "clsx": "1.1.1", "cross-env": "7.0.3", "es6-promise-pool": "2.5.0", "fractional-indexing": "3.2.0", "fuzzy": "0.1.3", "image-blob-reduce": "3.0.1", "jotai": "2.11.0", "jotai-scope": "0.7.2", "lodash.throttle": "4.1.1", "lodash.debounce": "4.0.8", "nanoid": "3.3.3", "open-color": "1.9.1", "pako": "2.0.3", "perfect-freehand": "1.2.0", "pica": "7.1.1", "png-chunk-text": "1.0.0", "png-chunks-encode": "1.0.0", "png-chunks-extract": "1.0.0", "points-on-curve": "1.0.1", "pwacompat": "2.0.17", "roughjs": "4.6.4", "sass": "1.51.0", "tunnel-rat": "0.1.2"}, "devDependencies": {"@size-limit/preset-big-lib": "9.0.0", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@types/lodash.debounce": "4.0.8", "@types/pako": "2.0.3", "@types/pica": "5.1.3", "@types/resize-observer-browser": "0.1.7", "ansicolor": "2.0.3", "autoprefixer": "10.4.7", "cross-env": "7.0.3", "dotenv": "16.0.1", "esbuild": "0.19.10", "esbuild-sass-plugin": "2.16.0", "eslint-plugin-react": "7.32.2", "fake-indexeddb": "3.1.7", "fonteditor-core": "2.4.1", "harfbuzzjs": "0.3.6", "jest-diff": "29.7.0", "typescript": "4.9.4"}, "repository": "https://github.com/excalidraw/excalidraw", "bugs": "https://github.com/excalidraw/excalidraw/issues", "homepage": "https://github.com/excalidraw/excalidraw/tree/master/packages/excalidraw", "scripts": {"gen:types": "rimraf types && tsc", "build:esm": "rimraf dist && node ../../scripts/buildPackage.js && yarn gen:types"}}