{"labels": {"paste": "<PERSON><PERSON><PERSON><PERSON>", "pasteAsPlaintext": "", "pasteCharts": "<PERSON><PERSON><PERSON><PERSON> udlifen", "selectAll": "Fren akk", "multiSelect": "<PERSON><PERSON> a<PERSON> tefrayt", "moveCanvas": "<PERSON><PERSON>tti taɣzut n usuneɣ", "cut": "Gzem", "copy": "<PERSON><PERSON><PERSON>", "copyAsPng": "Nɣel ɣer tecfawit am PNG", "copyAsSvg": "Nɣel ɣer tecfawit am SVG", "copyText": "<PERSON><PERSON><PERSON>er tecfawit am uḍris", "copySource": "", "convertToCode": "", "bringForward": "<PERSON><PERSON> <PERSON><PERSON> sdat", "sendToBack": "<PERSON><PERSON> s agilal", "bringToFront": "<PERSON><PERSON> <PERSON><PERSON> deffir", "sendBackward": "<PERSON><PERSON> <PERSON><PERSON> deffir", "delete": "Kkes", "copyStyles": "<PERSON><PERSON><PERSON>", "pasteStyles": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "stroke": "Azizdew", "background": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON><PERSON>", "strokeWidth": "<PERSON><PERSON><PERSON> n yizirig", "strokeStyle": "Aɣanib n tizirig", "strokeStyle_solid": "Aččuran", "strokeStyle_dashed": "<PERSON> tjerriḍin", "strokeStyle_dotted": "S tenqiḍin", "sloppiness": "<PERSON><PERSON><PERSON><PERSON>", "opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textAlign": "Areyyec n uḍris", "edges": "<PERSON><PERSON><PERSON>", "sharp": "<PERSON><PERSON><PERSON>", "round": "<PERSON><PERSON><PERSON><PERSON>", "arrowheads": "Ixfawen n tenccabt", "arrowhead_none": "Ulac", "arrowhead_arrow": "Taneccabt", "arrowhead_bar": "Afeggag", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "<PERSON><PERSON><PERSON>", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Tiddi n tsefsit", "fontFamily": "Tawacult n tsefsiyin", "addWatermark": "<PERSON><PERSON><PERSON> \"Yettwaxdem s Excalidraw\"", "handDrawn": "<PERSON><PERSON><PERSON> s ufus", "normal": "<PERSON><PERSON><PERSON>", "code": "Tangalt", "small": "Meẓẓi", "medium": "<PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON><PERSON><PERSON>", "veryLarge": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>as", "solid": "Aččuran", "hachure": "Azerreg", "zigzag": "", "crossHatch": "Azerreg anmidag", "thin": "A<PERSON><PERSON><PERSON>", "bold": "Azuran", "left": "<PERSON><PERSON><PERSON>", "center": "<PERSON>mmast", "right": "<PERSON><PERSON><PERSON><PERSON>", "extraBold": "<PERSON><PERSON><PERSON>", "architect": "Amasdag", "artist": "Anaẓur", "cartoonist": "Amefɣul", "fileTitle": "Isem n ufaylu", "colorPicker": "Amafran n yini", "canvasColors": "Yettwaseqdec di teɣzut n usuneɣ", "canvasBackground": "Agilal n teɣzut n usuneɣ", "drawingCanvas": "Taɣzut n usuneɣ", "layers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actions": "Tigawin", "language": "<PERSON><PERSON><PERSON><PERSON>", "liveCollaboration": "<PERSON><PERSON><PERSON><PERSON> s srid...", "duplicateSelection": "Sisleg", "untitled": "War azwel", "name": "<PERSON><PERSON>", "yourName": "Isem-ik (im)", "madeWithExcalidraw": "Yettwaxdem s Excalidraw", "group": "<PERSON><PERSON><PERSON>", "ungroup": "Kkess asegrew i tefrayt", "collaborators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showGrid": "Beqqeḍ aferrug", "addToLibrary": "<PERSON><PERSON> te<PERSON>", "removeFromLibrary": "Kkes si temkar<PERSON>it", "libraryLoadingMessage": "<PERSON><PERSON> n temkar<PERSON>it…", "libraries": "<PERSON><PERSON><PERSON>", "loadingScene": "<PERSON>ali n usayes…", "align": "Reyyec", "alignTop": "Areyyec uksawen", "alignBottom": "Areyyec ukessar", "alignLeft": "<PERSON><PERSON><PERSON> s <PERSON>", "alignRight": "Areyyec s ayfus", "centerVertically": "Di tlemmast s ibeddi", "centerHorizontally": "Di tlemmast s uglawi", "distributeHorizontally": "Freq s uglawi", "distributeVertically": "<PERSON>e<PERSON> s yibeddi", "flipHorizontal": "<PERSON><PERSON><PERSON>", "flipVertical": "<PERSON><PERSON><PERSON>", "viewMode": "Askar n tmuɣli", "share": "Bḍ<PERSON>", "showStroke": "<PERSON><PERSON><PERSON><PERSON><PERSON> amelqaḍ n yini n yizirig", "showBackground": "<PERSON><PERSON><PERSON><PERSON><PERSON> amelqaḍ n yini n ugilal", "toggleTheme": "<PERSON><PERSON><PERSON>l", "personalLib": "<PERSON><PERSON><PERSON><PERSON> tud<PERSON>", "excalidrawLib": "Tamkarḍit n Excalidraw", "decreaseFontSize": "<PERSON><PERSON><PERSON> tiddi n tsefsit", "increaseFontSize": "Sali tiddi n tsefsit", "unbindText": "<PERSON><PERSON><PERSON>", "bindText": "<PERSON><PERSON> s anagbar", "createContainerFromText": "", "link": {"edit": "Ẓreg aseɣwen", "editEmbed": "", "create": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "createEmbed": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "Ẓreg izirig", "exit": "Ffeɣ seg umaẓrag n yizirig"}, "elementLock": {"lock": "Sek<PERSON>", "unlock": "<PERSON><PERSON><PERSON>", "lockAll": "Sekkeṛ akk", "unlockAll": "Serreḥ akk"}, "statusPublished": "Yeffeɣ-d", "sidebarLock": "Eǧǧ afeggag n yidis yeldi", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "Ulac if<PERSON>n <PERSON> yakan...", "hint_emptyLibrary": "Fren aferdis di teɣzut nusuneɣ akken at-ternuḍ dagi, neɣ sbedd tamkarḍit seg usarsay azayez, ukessar-agi.", "hint_emptyPrivateLibrary": "Fren aferdis di teɣzut nusuneɣ akken at-ternuḍ dagi."}, "buttons": {"clearReset": "Ales awennez n teɣzut n usuneɣ", "exportJSON": "<PERSON><PERSON><PERSON>", "exportImage": "<PERSON><PERSON><PERSON> tugna...", "export": "<PERSON><PERSON> di...", "copyToClipboard": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON> deg ufaylu amiran", "saveAs": "<PERSON><PERSON> am", "load": "Ldi", "getShareableLink": "<PERSON><PERSON>-d ase<PERSON>wen n beṭṭu", "close": "<PERSON><PERSON>", "selectLanguage": "<PERSON><PERSON> tutlayt", "scrollBackToContent": "<PERSON><PERSON><PERSON> s agbur", "zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "Simẓi", "resetZoom": "Ales awennez n <PERSON>m<PERSON>er", "menu": "<PERSON><PERSON><PERSON>", "done": "Ifukk", "edit": "Ẓreg", "undo": "Sefsex", "redo": "Err-d", "resetLibrary": "Ales awennez n temkarḍit", "createNewRoom": "Snulfu-d <PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "fullScreen": "<PERSON>g<PERSON><PERSON>", "darkMode": "<PERSON><PERSON> imsulles", "lightMode": "Askar afaw", "zenMode": "Askar Zen", "objectsSnapMode": "", "exitZenMode": "Ffeɣ seg uskar Zen", "cancel": "Sefsex", "clear": "<PERSON><PERSON><PERSON>", "remove": "Kkes", "embed": "", "publishLibrary": "Ẓreg", "submit": "<PERSON><PERSON>", "confirm": "Sentem", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "<PERSON><PERSON><PERSON> ad isfeḍ akk taɣzut n usuneɣ. Tetḥeqqeḍ?", "couldNotCreateShareableLink": "D awezɣi asnulfu n useɣwen n beṭṭu.", "couldNotCreateShareableLinkTooBig": "<PERSON> awezɣi asnulfu n useɣwen n beṭṭu. <PERSON><PERSON><PERSON><PERSON> aṭas", "couldNotLoadInvalidFile": "D awezɣi asali n ufaylu arme<PERSON>tu", "importBackendFailed": "<PERSON><PERSON><PERSON><PERSON> seg uɣawas n deffir ur teddi ara.", "cannotExportEmptyCanvas": "D awezɣi asifeḍ n teɣzut n usuneɣ tilemt.", "couldNotCopyToClipboard": "<PERSON><PERSON><PERSON> an<PERSON>.", "decryptFailed": "D awezɣi tukksa n uwgelhen i yisefka.", "uploadedSecurly": "<PERSON><PERSON> s uw<PERSON>hen ixef s ixef, a<PERSON><PERSON> yeb<PERSON>a ad d-yini belli aqeddac n Excalidraw akked medden ur zmiren ara ad ɣren agbur.", "loadSceneOverridePrompt": "<PERSON><PERSON> n wunuɣ uffiɣ ad isemselsi agbur-inek (m) yellan. Tebɣiḍ ad tkemmeleḍ?", "collabStopOverridePrompt": "<PERSON><PERSON><PERSON> n tɣimit ad yesefsex unuɣ-inek (m) yettwaḥ<PERSON>zen yakan s wudem adigan. Tetḥeqqeḍ?\n(Ma tebɣiḍ ad teǧǧeḍ unuɣ-inek (m) adigan, mdel iccer n yiminig, deg umḍiq.)", "errorAddingToLibrary": "<PERSON><PERSON><PERSON> ara <PERSON> u<PERSON> tem<PERSON>", "errorRemovingFromLibrary": "Ulamek ara yettwakkes uferdis si temkarḍit", "confirmAddLibrary": "<PERSON><PERSON><PERSON> ad<PERSON> ta<PERSON> (win) {{numShapes}} ɣer temkar<PERSON>it-inek (m). Tetḥeqqeḍ?", "imageDoesNotContainScene": "Tugna-agi tettban-d ur tesɛi ara isefka n usayes. Tesremdeḍ aseddu n usayes deg usifeḍ?", "cannotRestoreFromImage": "<PERSON><PERSON><PERSON> ul<PERSON>k ara d-yettwarr seg ufaylu-agi n tugna", "invalidSceneUrl": "Ulamek taktert n usayes seg URL i d-ittunefken. Ahat mačči d tameɣtut neɣ ur tegbir ara isefka JSON n Excalidraw.", "resetLibrary": "<PERSON><PERSON><PERSON> ad isfeḍ tamkarḍit-inek•m. <PERSON>eqqeḍ?", "removeItemsFromsLibrary": "Ad tekkseḍ {{count}} n uferdis (en) si temkarḍit?", "invalidEncryptionKey": "Tasarut n uwgelhen isefk ad tesɛu 22 n yiekkilen. Amɛiwen srid yensa.", "collabOfflineWarning": "Ulac tuqqna n internet.\nIbedilen-ik ur ttwaklasen ara!"}, "errors": {"unsupportedFileType": "Anaw n ufaylu ur yettwasefrak ara.", "imageInsertError": "D awezɣi tugra n tugna. <PERSON><PERSON><PERSON><PERSON> tikkelt-nniḍen ardeqqal...", "fileTooBig": "<PERSON><PERSON><PERSON><PERSON> meqqer aṭas. T<PERSON>di tafellayt yurgen d {{maxSize}}.", "svgImageInsertError": "D awezɣi tugra n tugna SVG. Acraḍ SVG yettban-d d armeɣtu.", "failedToFetchImage": "", "invalidSVGString": "SVG armeɣtu.", "cannotResolveCollabServer": "<PERSON><PERSON><PERSON> tuqqna s aqedd<PERSON> n umyalel. Ma ulac uɣilif ales asali n usebter sakin eɛreḍ tikkelt-nniḍen.", "importLibraryError": "Ur d-ssalay ara tamkar<PERSON>it", "collabSaveFailed": "Ulamek asekles deg uzadur n yisefka deg ugilal. Ma ikemmel wugur, isefk ad teskelseḍ afaylu s wudem adigan akken ad tetḥeqqeḍ ur tesruḥuyeḍ ara amahil-inek•inem.", "collabSaveFailed_sizeExceeded": "Ulamek asekles deg uzadur n yisefka deg ugilal, taɣ<PERSON>t n usuneɣ tettban-d temqer aṭas. Isefk ad teskelseḍ afaylu s wudem adigan akken ad tetḥeqqeḍ ur tesruḥuyeḍ ara amahil-inek•inem.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "<PERSON><PERSON><PERSON> ad d-iglu s truẓi n<bold><PERSON><PERSON><PERSON><PERSON> n uḍris</bold>deg wunuɣen-inek.", "line3": "Ad k-nsemter ad tsexsiḍ aɣewwar-agi. Tzemreḍ ad tḍefreḍ<link>isurifen-agi</link> ɣef wamek ara txedmeḍ.", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON> <PERSON><PERSON>", "rectangle": "<PERSON><PERSON>", "diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ellipse": "Taglayt", "arrow": "Taneccabt", "line": "<PERSON><PERSON><PERSON>", "freedraw": "<PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "library": "<PERSON><PERSON><PERSON><PERSON>", "lock": "Eǧǧ afecku n tefrayt yermed mbaɛd asuneɣ", "penMode": "Askar n yimru - gdel tanalit", "link": "Rnu/leqqem ase<PERSON>wen i talɣa yett<PERSON>nen", "eraser": "<PERSON><PERSON><PERSON>", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "<PERSON><PERSON><PERSON> (afecku n usmutti n tmuɣli)", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "Tigawin n teɣzut n usuneɣ", "selectedShapeActions": "Tigawin n talɣa yettwafernen", "shapes": "Talɣiwin"}, "hints": {"canvasPanning": "Akken ad tesmuttiḍ taɣzut n usuneɣ, ṭ<PERSON><PERSON> ṛ<PERSON>uda n umumed, neɣ seqdec afecku <PERSON>s", "linearElement": "Ssit akken ad tebduḍ aṭas n tenqi<PERSON>, zuɣ<PERSON> i yiwen n yizirig", "freeDraw": "<PERSON><PERSON><PERSON> yerna <PERSON>, se<PERSON><PERSON> ticki tfuke<PERSON>", "text": "Tixidest: t<PERSON><PERSON><PERSON><PERSON> daɣen ad ternuḍ aḍris s usiti snat n tikkal anida tebɣiḍ s ufecku n tefrayt", "embeddable": "", "text_selected": "Ssit snat n tikkal neɣ ssed taqeffalt Kcem akken ad tẓergeḍ aḍris", "text_editing": "Ssit Escape neɣ CtrlOrCmd+ENTER akken ad tfakkeḍ asiẓreg", "linearElementMulti": "Ssit ɣef tenqiḍt taneggarut neɣ ssed taqeffalt Escape neɣ taqeffalt Kcem akken ad tfakkeḍ", "lockAngle": "T<PERSON><PERSON><PERSON>ḍ ad tḥettmeḍ tiɣmert s tuṭṭfa n tqeffalt SHIFT", "resize": "T<PERSON><PERSON>reḍ ad tḥette<PERSON>ḍ assaɣ s tuṭṭfa n tqeffalt SHIFT mi ara tettbeddileḍ tiddi,\nma teṭṭfeḍ ALT abeddel n tiddi ad yili si tlemmast", "resizeImage": "Tze<PERSON><PERSON>ḍ ad talseḍ tiddi s tilelli s tuṭṭfa n SHIFT,\nṭṭef ALT akken ad talseḍ tiddi si tlemmast", "rotate": "T<PERSON><PERSON><PERSON><PERSON> ad tḥette<PERSON>ḍ tiɣemmar s tuṭṭfa n SHIFT di tuzzya", "lineEditor_info": "Ssed ɣef CtrlOrCmd yerna ssit snat n tikkal neɣ ssed ɣef CtrlOrCmd + Kcem akken ad tẓergeḍ tineqqiḍin", "lineEditor_pointSelected": "Ssed taqeffalt kkes akken ad tekkseḍ tanqiḍ (tinqiḍin),\nCtrlOrCmd+D akken ad tsiselgeḍ, neɣ zuɣer akken ad tesmuttiḍ", "lineEditor_nothingSelected": "Fren tanqiḍt akken ad tẓergeḍ (ṭṭef SHIFT akken ad tferneḍ aṭas),\nneɣ ṭṭef Alt akken ad ternuḍ tinqiḍin timaynutin", "placeImage": "Ssit akken ad tserseḍ tugna, neɣ ssit u zuɣer akken ad tesbaduḍ tiddi-ines s ufus", "publishLibrary": "Siẓreg tamkarḍit-inek•inem", "bindTextToElement": "Ssed ɣef kcem akken ad ternuḍ aḍris", "deepBoxSelect": "Ṭṭef CtrlOrCmd akken ad tferneḍ s telqey, yerna ad trewleḍ i uzuɣer", "eraserRevert": "Ssed Alt akken ad tsefsxeḍ iferdisen yettwacerḍen i tukksa", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Ulamek abeqqeḍ n teskant", "canvasTooBig": "Taɣzut n usuneɣ tezmer ad tili temeqqer aṭas.", "canvasTooBigTip": "Tixidest: eɛ<PERSON><PERSON> ad tesqerbeḍ ciṭ iferdisen yembaɛaden."}, "errorSplash": {"headingMain": "<PERSON><PERSON><PERSON>-d tuccḍa. Eɛreḍ <button>asali n usebter tikkelt-nniḍen.</button>", "clearCanvasMessage": "Ma yella tulsa n usali ur tefri ara ugur, eɛ<PERSON><PERSON> <button>asfaḍ n teɣzut n usuneɣ.</button>", "clearCanvasCaveat": " <PERSON><PERSON><PERSON> ad d-iglu s usṛuḥu n umahil ", "trackedToSentry": "Tucc<PERSON>a akked umesmagi {{eventId}} tettwasekles deg unagraw-nneɣ.", "openIssueMessage": "Nḥuder aṭas akken ur nseddu ara talɣut n usayes-inek (m) di tuccḍa. Ma yella asayes-inek (m) ma<PERSON><PERSON>i d amaẓlay, ttxil-k (m) xemmem ad ḍefreḍ <button>a<PERSON><PERSON>u n weḍfar n yibugen.</button> Ma ulac uɣilif seddu talɣut ukessar-agi s wenɣal akked usenṭeḍ di GitHub issue.", "sceneContent": "Agbur n usayes:"}, "roomDialog": {"desc_intro": "Tzemreḍ ad d-teɛerḍeḍ medden ɣer usayes-inek (m) amiran akken ad ttekkin yid-k.", "desc_privacy": "<PERSON>r tq<PERSON>q ara, tiɣ<PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> aw<PERSON>hen ixef s ixef, dɣa ayen ara tsunɣeḍ ad iqqim d amaẓlay. Ula d aqeddac-nneɣ ur yezmir ara ad iwali acu txeddemeḍ.", "button_startSession": "Bdu tiɣimit", "button_stopSession": "<PERSON><PERSON> tiɣimit", "desc_inProgressIntro": "Tiɣimit n umɛawen s srid tetteddu akka tura.", "desc_shareLink": "<PERSON><PERSON>u aseɣwen-agi akked medden ukud tebɣiḍ ad temɛawaneḍ:", "desc_exitSession": "A<PERSON><PERSON> n tɣimit ad k (m) yesenser si texxamt, maca ad tizmireḍ ad tkemmeleḍ amahil s usayes, s wudem adigan. Ẓer belli ayagi ur yettḥaz ara imdanen-nniḍen, yerna ad izmiren ad kemmelen ad mɛawanen di tsuffeɣt-nnsen.", "shareTitle": "<PERSON><PERSON> ɣer tɣimit n umɛiwen s srid n Excalidraw"}, "errorDialog": {"title": "Tuccḍa"}, "exportDialog": {"disk_title": "<PERSON><PERSON> deg u<PERSON>", "disk_details": "Sekles isefka n usayes deg ufaylu ansi ara tizmireḍ ad d-tketreḍ areḍqal.", "disk_button": "<PERSON><PERSON> deg ufaylu", "link_title": "<PERSON><PERSON><PERSON><PERSON> n beṭṭu", "link_details": "Sifeḍ am useɣwen n tɣuri kan.", "link_button": "<PERSON><PERSON><PERSON> deg <PERSON>wen", "excalidrawplus_description": "<PERSON><PERSON> asay<PERSON>-inek•inem di tallunt n umahil Excalidraw+.", "excalidrawplus_button": "<PERSON><PERSON><PERSON>", "excalidrawplus_exportError": "<PERSON><PERSON><PERSON> asifeḍ ɣer <PERSON>+ akka tura..."}, "helpDialog": {"blog": "Ɣeṛ ablug-nneɣ", "click": "ssit", "deepSelect": "<PERSON><PERSON><PERSON> s telqey", "deepBoxSelect": "<PERSON><PERSON><PERSON> s telqey s tnaka, yerna ad tyrewle<PERSON> i uzuɣer", "curvedArrow": "Taneccabt izelgen", "curvedLine": "<PERSON><PERSON><PERSON>", "documentation": "<PERSON><PERSON><PERSON><PERSON>", "doubleClick": "ssit snat n tikkal", "drag": "<PERSON><PERSON><PERSON>", "editor": "Amaẓrag", "editLineArrowPoints": "Ẓreg tinqiḍin n yizirig/taneccabt", "editText": "Ẓreg aḍris/rnu tabzimt", "github": "Tufiḍ-d ugur? Azen-aɣ-d", "howto": "Ḍfer imniren-nneɣ", "or": "neɣ", "preventBinding": "<PERSON><PERSON><PERSON> tuqqna n tneccabin", "tools": "<PERSON><PERSON><PERSON>", "shortcuts": "Inegzumen n unasiw", "textFinish": "Fak asiẓreg (amaẓrag n uḍris)", "textNewLine": "<PERSON><PERSON> ajer<PERSON> amaynut (amaẓrag n uḍris)", "title": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON><PERSON>", "zoomToFit": "Simɣur akken ad twliḍ akk iferdisen", "zoomToSelection": "<PERSON><PERSON><PERSON><PERSON>", "toggleElementLock": "Sekkeṛ/kkes asekker i tefrayt", "movePageUpDown": "<PERSON><PERSON><PERSON> as<PERSON>ter d asawen/a<PERSON><PERSON>", "movePageLeftRight": "<PERSON><PERSON><PERSON> as<PERSON>ter s azel<PERSON>/ayfus"}, "clearCanvasDialog": {"title": "<PERSON><PERSON><PERSON> taɣzut n usuneɣ"}, "publishDialog": {"title": "Suffeɣ-d tamkar<PERSON>it", "itemName": "Isem n uferdis", "authorName": "<PERSON><PERSON> n umeskar", "githubUsername": "Isem n useqdac n GitHub", "twitterUsername": "Isem n useqdac n Twitter", "libraryName": "Is<PERSON> n temkar<PERSON>it", "libraryDesc": "Aglam n temkarḍit", "website": "Asmel n web", "placeholder": {"authorName": "Isem neɣ isem n useqdac inek•inem", "libraryName": "Isem n temkarḍit-inek•inem", "libraryDesc": "Aglam n temkarḍit-inek•inem akken ad tɛiwneḍ medden ad fehmen aseqdec-inec", "githubHandle": "<PERSON><PERSON> n useqdac n <PERSON><PERSON><PERSON><PERSON> ( d <PERSON><PERSON><PERSON><PERSON>) akken ad tizmireḍ ad tisẓrigeḍ tamkarḍit ticki tuzneḍ-tt i uselken", "twitterHandle": "<PERSON><PERSON> n useqdac n Twitter (d <PERSON><PERSON><PERSON><PERSON>) akken ad nẓer anwa ara nsenmer deg udellel di Twitter", "website": "<PERSON><PERSON><PERSON><PERSON> ɣer usmel-inek•inem neɣ waye<PERSON> (d <PERSON><PERSON><PERSON><PERSON>)"}, "errors": {"required": "<PERSON><PERSON><PERSON><PERSON>", "website": "Sekcem URL ameɣtu"}, "noteDescription": "Azen tamkarḍit-inek•inem akken ad teddu di <link>akaram aza<PERSON>z n temkarḍit</link>i yimdanen-nniḍen ara isqedcen deg wunuɣen-nnsen.", "noteGuidelines": "Tamkarḍit teḥwaǧ ad tettwaqbel s ufus qbel. Ma ulac uɣilif ɣer <link>i<PERSON><PERSON><PERSON></link> send ad tazneḍ. Tesriḍ amiḍan n GitHub akken ad tmmeslayeḍ yerna ad tgeḍ ibeddilen ma yelaq, maca mačči d ayen yettwaḥetmen.", "noteLicense": "Mi tuzneḍ ad tqebleḍ akken tamkarḍit ad d-teffeɣ s <link>Turagt MIT, </link>ayen yebɣan ad d-yini belli yal yiwen izmer ad ten-iseqdec war tilist.", "noteItems": "Yal aferdis n temkarḍit isefk ad isɛu isem-is i yiman-is akken ad yili wamek ara yettusizdeg. Iferdisen-agi n temkarḍit ad ddun:", "atleastOneLibItem": "Ma ulac uɣilif fern ma drus yiwen n uferdis n temkarḍit akken ad tebduḍ", "republishWarning": "Tamawt: kra n yiferdisen yettwafernen ttwacerḍen ffeɣen-d/ttwaznen. Isefk ad talseḍ tuzzna n yiferdisen anagar mi ara tleqqemeḍ tamkarḍit neɣ tuzzna yellan."}, "publishSuccessDialog": {"title": "Tamkarḍit tettwazen", "content": "<PERSON><PERSON><PERSON><PERSON>-ik•im {{authorName}}. Tamkar<PERSON>it-inek•inem tettwazen i weselken. Tzemreḍ ad tḍefreḍ aẓayer<link>dagi</link>"}, "confirmDialog": {"resetLibrary": "Ales awennez n temkarḍit", "removeItemsFromLib": "Kkes iferdisen yet<PERSON>nen si temkarḍit"}, "imageExportDialog": {"header": "", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "", "scale": "", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "Unuɣen-inek (m) ttuwgelhnen seg yixef s ixef dɣa iqeddacen n Excalidraw werǧin ad ten-walin. ", "link": "Amagrad ɣef uwgelhen ixef s ixef di Excalidraw"}, "stats": {"angle": "Tiɣmeṛt", "element": "<PERSON><PERSON><PERSON>", "elements": "<PERSON><PERSON><PERSON><PERSON>", "height": "<PERSON><PERSON><PERSON>", "scene": "<PERSON><PERSON><PERSON>", "selected": "Yettwafren", "storage": "Aḥraz", "title": "", "total": "<PERSON><PERSON><PERSON>", "version": "<PERSON><PERSON><PERSON>", "versionCopy": "Sit ad tneɣleḍ", "versionNotAvailable": "Ur inuḥ ulqem", "width": "<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "<PERSON><PERSON><PERSON><PERSON>", "copyStyles": "<PERSON><PERSON><PERSON><PERSON>.", "copyToClipboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "copyToClipboardAsPng": "{{exportSelection}} yettwan<PERSON><PERSON> ɣer tecfawit am PNG\n({{exportColorScheme}})", "fileSaved": "<PERSON><PERSON><PERSON><PERSON>.", "fileSavedToFilename": "<PERSON><PERSON>wasek<PERSON> di {filename}", "canvas": "taɣzut n usuneɣ", "selection": "<PERSON><PERSON><PERSON>", "pasteAsSingleElement": "", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "<PERSON><PERSON><PERSON>", "black": "", "white": "", "red": "", "pink": "", "grape": "", "violet": "", "gray": "", "blue": "", "cyan": "", "teal": "", "green": "", "yellow": "", "orange": "", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "Akk isefka-inek•inem ttwakelsen s wudem adigan deg yiminig-inek•inem.", "center_heading_plus": "Tebɣiḍ ad tedduḍ ɣer Excalidraw+ deg umḍiq?", "menuHint": "<PERSON><PERSON><PERSON>, ismenyifen, tutlayin, ..."}, "defaults": {"menuHint": "<PERSON><PERSON><PERSON>, ismenyifen, d wayen-nni<PERSON>en...", "center_heading": "", "toolbarHint": "Fren afecku tebdu<PERSON> asune<PERSON>!", "helpHint": "<PERSON><PERSON><PERSON><PERSON> akked tallelt"}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}