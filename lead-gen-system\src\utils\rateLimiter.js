import { RateLimiterMemory } from 'rate-limiter-flexible';
import { logger } from './logger.js';

// Rate limiters for different services
const rateLimiters = {
  // Companies House API - 600 requests per 5 minutes
  'companies-house': new RateLimiterMemory({
    keyPrefix: 'companies-house',
    points: 600, // Number of requests
    duration: 300, // Per 5 minutes (300 seconds)
  }),
  
  // Apollo.io API - varies by plan, conservative limit
  'apollo': new RateLimiterMemory({
    keyPrefix: 'apollo',
    points: 100, // Number of requests
    duration: 60, // Per minute
  }),
  
  // Hunter.io API - varies by plan
  'hunter': new RateLimiterMemory({
    keyPrefix: 'hunter',
    points: 100, // Number of requests
    duration: 60, // Per minute
  }),
  
  // NeverBounce API - 1000 requests per hour
  'neverbounce': new RateLimiterMemory({
    keyPrefix: 'neverbounce',
    points: 1000, // Number of requests
    duration: 3600, // Per hour (3600 seconds)
  }),
  
  // HubSpot API - 100 requests per 10 seconds
  'hubspot': new RateLimiterMemory({
    keyPrefix: 'hubspot',
    points: 100, // Number of requests
    duration: 10, // Per 10 seconds
  }),
  
  // SendGrid API - varies by plan, conservative limit
  'sendgrid': new RateLimiterMemory({
    keyPrefix: 'sendgrid',
    points: 1000, // Number of requests
    duration: 60, // Per minute
  }),
  
  // OpenAI API - 3500 requests per minute for GPT-4
  'openai': new RateLimiterMemory({
    keyPrefix: 'openai',
    points: 3500, // Number of requests
    duration: 60, // Per minute
  }),
  
  // General API rate limiter
  'general': new RateLimiterMemory({
    keyPrefix: 'general',
    points: 100, // Number of requests
    duration: 60, // Per minute
  })
};

class RateLimiterService {
  /**
   * Consume a rate limit point for a specific service
   * @param {string} service - Service name (e.g., 'companies-house', 'apollo')
   * @param {string} key - Optional key for user-specific limiting
   * @returns {Promise<void>}
   */
  async consume(service, key = 'default') {
    const limiter = rateLimiters[service] || rateLimiters['general'];
    
    try {
      await limiter.consume(key);
      logger.debug(`Rate limit consumed for ${service}:${key}`);
    } catch (rejRes) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      logger.warn(`Rate limit exceeded for ${service}:${key}. Retry after ${secs} seconds`);
      
      // Wait for the required time
      await new Promise(resolve => setTimeout(resolve, rejRes.msBeforeNext));
      
      // Retry the consumption
      await limiter.consume(key);
      logger.info(`Rate limit retry successful for ${service}:${key}`);
    }
  }
  
  /**
   * Get remaining points for a service
   * @param {string} service - Service name
   * @param {string} key - Optional key for user-specific limiting
   * @returns {Promise<Object>} Remaining points and reset time
   */
  async getRemaining(service, key = 'default') {
    const limiter = rateLimiters[service] || rateLimiters['general'];
    
    try {
      const res = await limiter.get(key);
      return {
        remaining: res ? res.remainingHits : limiter.points,
        resetTime: res ? new Date(Date.now() + res.msBeforeNext) : null
      };
    } catch (error) {
      logger.error(`Error getting rate limit info for ${service}:${key}`, error);
      return { remaining: 0, resetTime: null };
    }
  }
  
  /**
   * Reset rate limit for a service
   * @param {string} service - Service name
   * @param {string} key - Optional key for user-specific limiting
   * @returns {Promise<void>}
   */
  async reset(service, key = 'default') {
    const limiter = rateLimiters[service] || rateLimiters['general'];
    
    try {
      await limiter.delete(key);
      logger.info(`Rate limit reset for ${service}:${key}`);
    } catch (error) {
      logger.error(`Error resetting rate limit for ${service}:${key}`, error);
    }
  }
  
  /**
   * Add a custom rate limiter
   * @param {string} name - Limiter name
   * @param {Object} options - Rate limiter options
   */
  addLimiter(name, options) {
    rateLimiters[name] = new RateLimiterMemory({
      keyPrefix: name,
      ...options
    });
    logger.info(`Added custom rate limiter: ${name}`);
  }
  
  /**
   * Get all available rate limiters
   * @returns {Array<string>} List of available limiters
   */
  getAvailableLimiters() {
    return Object.keys(rateLimiters);
  }
  
  /**
   * Batch consume multiple services
   * @param {Array<Object>} services - Array of {service, key} objects
   * @returns {Promise<void>}
   */
  async batchConsume(services) {
    const promises = services.map(({ service, key = 'default' }) => 
      this.consume(service, key)
    );
    
    await Promise.all(promises);
  }
}

export const rateLimiter = new RateLimiterService();
export default rateLimiter;
