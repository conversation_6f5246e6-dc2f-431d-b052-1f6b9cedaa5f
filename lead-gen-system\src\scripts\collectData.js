#!/usr/bin/env node

import { config, validateConfig } from '../config/index.js';
import { companiesHouseService } from '../services/companiesHouseService.js';
import { logger } from '../utils/logger.js';
import fs from 'fs/promises';
import path from 'path';

class DataCollector {
  constructor() {
    this.outputDir = './data';
    this.ensureOutputDir();
  }

  async ensureOutputDir() {
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
    } catch (error) {
      logger.error('Error creating output directory:', error);
    }
  }

  /**
   * Collect Multi Academy Trust data from Companies House
   */
  async collectMATData() {
    logger.info('🏢 Starting MAT data collection from Companies House...');
    
    const allMATs = [];
    const searchQueries = [
      'multi academy trust',
      'academy trust',
      'academies trust',
      'education trust',
      'schools trust'
    ];

    try {
      for (const query of searchQueries) {
        logger.info(`Searching for: "${query}"`);
        
        let startIndex = 0;
        let hasMoreResults = true;
        
        while (hasMoreResults) {
          const results = await companiesHouseService.searchMATs(query, 100, startIndex);
          
          if (results.mats.length === 0) {
            hasMoreResults = false;
            break;
          }
          
          allMATs.push(...results.mats);
          logger.info(`Found ${results.mats.length} MATs in this batch. Total so far: ${allMATs.length}`);
          
          startIndex += 100;
          
          // Check if we've reached the end
          if (startIndex >= results.total_results) {
            hasMoreResults = false;
          }
          
          // Add delay to respect rate limits
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Remove duplicates based on company number
      const uniqueMATs = this.removeDuplicates(allMATs, 'company_number');
      logger.info(`Found ${uniqueMATs.length} unique MATs after deduplication`);

      // Save basic MAT data
      await this.saveToFile('mats_basic.json', uniqueMATs);
      
      return uniqueMATs;
    } catch (error) {
      logger.error('Error collecting MAT data:', error);
      throw error;
    }
  }

  /**
   * Collect detailed information for each MAT
   */
  async collectDetailedMATData(mats) {
    logger.info('📋 Collecting detailed MAT information...');
    
    const detailedMATs = [];
    const batchSize = 10; // Process in small batches to avoid overwhelming the API
    
    for (let i = 0; i < mats.length; i += batchSize) {
      const batch = mats.slice(i, i + batchSize);
      logger.info(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(mats.length/batchSize)}`);
      
      const batchPromises = batch.map(async (mat) => {
        try {
          const detailedData = await companiesHouseService.getComprehensiveMatData(mat.company_number);
          return {
            ...mat,
            detailed: detailedData,
            collectedAt: new Date().toISOString()
          };
        } catch (error) {
          logger.error(`Error collecting detailed data for ${mat.company_number}:`, error.message);
          return {
            ...mat,
            detailed: null,
            error: error.message,
            collectedAt: new Date().toISOString()
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      detailedMATs.push(...batchResults);
      
      // Save progress after each batch
      await this.saveToFile('mats_detailed_progress.json', detailedMATs);
      
      // Add delay between batches
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Save final detailed data
    await this.saveToFile('mats_detailed.json', detailedMATs);
    
    return detailedMATs;
  }

  /**
   * Download UK Schools data from GIAS
   */
  async collectSchoolsData() {
    logger.info('🏫 Collecting UK Schools data...');
    
    // Note: GIAS data is available as CSV downloads
    // For now, we'll provide instructions for manual download
    // In a production system, you might want to automate this with web scraping
    
    const instructions = {
      message: "UK Schools data needs to be downloaded manually from GIAS",
      steps: [
        "1. Visit: https://get-information-schools.service.gov.uk/Downloads",
        "2. Download 'Establishment fields CSV'",
        "3. Download 'Establishment links CSV' (for MAT relationships)",
        "4. Save files to ./data/schools/ directory",
        "5. Run the school data processing script"
      ],
      files_needed: [
        "edubasealldata.csv",
        "links_edubasealldata.csv"
      ]
    };
    
    await this.saveToFile('schools_download_instructions.json', instructions);
    
    logger.info('📋 Schools data download instructions saved to schools_download_instructions.json');
    logger.info('Please download the required files manually and then run the school processing script');
    
    return instructions;
  }

  /**
   * Remove duplicates from array based on a key
   */
  removeDuplicates(array, key) {
    const seen = new Set();
    return array.filter(item => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  }

  /**
   * Save data to JSON file
   */
  async saveToFile(filename, data) {
    try {
      const filepath = path.join(this.outputDir, filename);
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      logger.info(`💾 Data saved to ${filepath}`);
    } catch (error) {
      logger.error(`Error saving to ${filename}:`, error);
      throw error;
    }
  }

  /**
   * Generate collection summary
   */
  async generateSummary(mats, detailedMATs) {
    const summary = {
      collection_date: new Date().toISOString(),
      total_mats_found: mats.length,
      detailed_data_collected: detailedMATs.filter(mat => mat.detailed).length,
      errors: detailedMATs.filter(mat => mat.error).length,
      top_mats_by_size: detailedMATs
        .filter(mat => mat.detailed?.company?.accounts?.next_due)
        .slice(0, 10)
        .map(mat => ({
          name: mat.title,
          company_number: mat.company_number,
          status: mat.company_status,
          incorporation_date: mat.date_of_creation
        })),
      collection_stats: {
        avg_processing_time: '~2 seconds per MAT',
        rate_limit_hits: 0, // This would be tracked in a real implementation
        api_calls_made: detailedMATs.length * 3 // Approximate
      }
    };
    
    await this.saveToFile('collection_summary.json', summary);
    return summary;
  }
}

// Main execution function
async function main() {
  logger.info('🚀 Starting UK Schools Lead Generation Data Collection');
  
  // Validate configuration
  if (!validateConfig()) {
    process.exit(1);
  }
  
  const collector = new DataCollector();
  
  try {
    // Step 1: Collect basic MAT data
    const mats = await collector.collectMATData();
    
    // Step 2: Collect detailed MAT data (limit to first 50 for testing)
    const matsToProcess = config.development.testMode ? mats.slice(0, 10) : mats;
    const detailedMATs = await collector.collectDetailedMATData(matsToProcess);
    
    // Step 3: Collect schools data instructions
    await collector.collectSchoolsData();
    
    // Step 4: Generate summary
    const summary = await collector.generateSummary(mats, detailedMATs);
    
    logger.info('✅ Data collection completed successfully!');
    logger.info(`📊 Summary: ${summary.total_mats_found} MATs found, ${summary.detailed_data_collected} detailed records collected`);
    
  } catch (error) {
    logger.error('❌ Data collection failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
