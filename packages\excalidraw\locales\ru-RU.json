{"labels": {"paste": "Вставить", "pasteAsPlaintext": "Вставить как обычный текст", "pasteCharts": "Вставить диаграммы", "selectAll": "Выбрать всё", "multiSelect": "Добавить элемент в выделенный фрагмент", "moveCanvas": "Переместить холст", "cut": "Вырезать", "copy": "Копировать", "copyAsPng": "Скопировать в буфер обмена как PNG", "copyAsSvg": "Скопировать в буфер обмена как SVG", "copyText": "Скопировать в буфер обмена как текст", "copySource": "Копировать источник в буфер обмена", "convertToCode": "Преобразовать в код", "bringForward": "Переместить вперед", "sendToBack": "На задний план", "bringToFront": "На передний план", "sendBackward": "Переместить назад", "delete": "Удалить", "copyStyles": "Скопировать стили", "pasteStyles": "Вставить стили", "stroke": "Обводка", "background": "Фон", "fill": "Заливка", "strokeWidth": "Тол<PERSON>ина штриха", "strokeStyle": "Стиль обводки", "strokeStyle_solid": "Сплошная", "strokeStyle_dashed": "Пунктирная", "strokeStyle_dotted": "Точечная", "sloppiness": "Стиль обводки", "opacity": "Непрозрачность", "textAlign": "Выравнивание текста", "edges": "Края", "sharp": "Острые", "round": "Скругленные", "arrowheads": "Стрелка", "arrowhead_none": "Нет", "arrowhead_arrow": "Cтрелка", "arrowhead_bar": "Черта", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Треугольник", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Размер шрифта", "fontFamily": "Семейство шрифтов", "addWatermark": "Добавить «Создано в Excalidraw»", "handDrawn": "От руки", "normal": "Обычный", "code": "<PERSON>од", "small": "Малый", "medium": "Средний", "large": "Больш<PERSON>й", "veryLarge": "Очень большой", "solid": "Однотонная", "hachure": "Штрихованная", "zigzag": "Зиг<PERSON>аг", "crossHatch": "Перекрестная", "thin": "Тонкая", "bold": "Жирная", "left": "Слева", "center": "Центр", "right": "Справа", "extraBold": "Очень жирная", "architect": "Архитектор", "artist": "Художник", "cartoonist": "Карика<PERSON>у<PERSON><PERSON><PERSON>т", "fileTitle": "Имя файла", "colorPicker": "Выбор цвета", "canvasColors": "Используется на холсте", "canvasBackground": "Фон холста", "drawingCanvas": "Полотно", "layers": "Слои", "actions": "Действия", "language": "Язык", "liveCollaboration": "Он<PERSON><PERSON><PERSON>н взаимодействие...", "duplicateSelection": "Дубли<PERSON>а<PERSON>", "untitled": "Безымянный", "name": "Имя", "yourName": "Ваше имя", "madeWithExcalidraw": "Сделано в Excalidraw", "group": "Сгруппировать выделение", "ungroup": "Разделить выделение", "collaborators": "Участники", "showGrid": "Показать сетку", "addToLibrary": "Добавить в библиотеку", "removeFromLibrary": "Удалить из библиотеки", "libraryLoadingMessage": "Загрузка библиотеки…", "libraries": "Просмотреть библиотеки", "loadingScene": "Загрузка сцены…", "align": "Выровнять", "alignTop": "Выровнять по верхнему краю", "alignBottom": "Выровнять по нижнему краю", "alignLeft": "Выровнять по левому краю", "alignRight": "Выровнять по правому краю", "centerVertically": "Центрировать по вертикали", "centerHorizontally": "Центрировать по горизонтали", "distributeHorizontally": "Распределить по горизонтали", "distributeVertically": "Распределить по вертикали", "flipHorizontal": "Переворот по горизонтали", "flipVertical": "Переворот по вертикали", "viewMode": "Вид", "share": "Поделиться", "showStroke": "Показать выбор цвета обводки", "showBackground": "Показать выбор цвета фона", "toggleTheme": "Переключить тему", "personalLib": "Личная библиотека", "excalidrawLib": "Библиотека Excalidraw", "decreaseFontSize": "Уменьшить шрифт", "increaseFontSize": "Увеличить шрифт", "unbindText": "Отвязать текст", "bindText": "Привязать текст к контейнеру", "createContainerFromText": "Поместить текст в контейнер", "link": {"edit": "Редактировать ссылку", "editEmbed": "", "create": "Создать ссылку", "createEmbed": "", "label": "Ссылка", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "Редактирование строки", "exit": "Выход из редактора строки"}, "elementLock": {"lock": "Блокировать", "unlock": "Разблокировать", "lockAll": "Заблокировать все", "unlockAll": "Разблокировать все"}, "statusPublished": "Опубликовано", "sidebarLock": "Держать боковую панель открытой", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "Взять образец цвета с холста", "textToDiagram": "Текст в диаграмму", "prompt": ""}, "library": {"noItems": "Пока ничего не добавлено...", "hint_emptyLibrary": "Выберите объект на холсте, чтобы добавить его сюда, или установите библиотеку из публичного репозитория ниже.", "hint_emptyPrivateLibrary": "Выберите объект на холсте, чтобы добавить его сюда."}, "buttons": {"clearReset": "Очистить холст и сбросить цвет фона", "exportJSON": "Сохранить в", "exportImage": "Экспортировать изображение...", "export": "Сохранить как...", "copyToClipboard": "Скопировать в буфер обмена", "save": "Сохранить в текущий файл", "saveAs": "Сохранить как", "load": "Открыть", "getShareableLink": "Получить доступ по ссылке", "close": "Закрыть", "selectLanguage": "Выбрать язык", "scrollBackToContent": "Вернуться к содержимому", "zoomIn": "Увеличить", "zoomOut": "Уменьшить", "resetZoom": "Сбросить масштаб", "menu": "<PERSON>е<PERSON><PERSON>", "done": "Готово", "edit": "Изменить", "undo": "Шаг назад", "redo": "Шаг вперед", "resetLibrary": "Сброс библиотеки", "createNewRoom": "Создать новую комнату", "fullScreen": "Полный экран", "darkMode": "Темная тема", "lightMode": "Светлая тема", "zenMode": "<PERSON>ежим Дзен", "objectsSnapMode": "Привязка к объектам", "exitZenMode": "Выключить режим концентрации внимания", "cancel": "Отменить", "clear": "Очистить", "remove": "Удалить", "embed": "", "publishLibrary": "Опубликовать", "submit": "Отправить", "confirm": "Подтвердить", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "Это очистит весь холст. Вы уверены?", "couldNotCreateShareableLink": "Не удалось создать общедоступную ссылку.", "couldNotCreateShareableLinkTooBig": "Нельзя создать ссылку, чтобы поделиться. Сцена слишком большая", "couldNotLoadInvalidFile": "Не удалось загрузить недопустимый файл", "importBackendFailed": "Не удалось импортировать из бэкэнда.", "cannotExportEmptyCanvas": "Не может экспортировать пустой холст.", "couldNotCopyToClipboard": "Не удалось скопировать в буфер обмена.", "decryptFailed": "Не удалось расшифровать данные.", "uploadedSecurly": "Загружаемые данные защищена сквозным шифрованием, что означает, что сервер Excalidraw и третьи стороны не могут прочитать содержимое.", "loadSceneOverridePrompt": "Загрузка рисунка приведёт к замене имеющегося содержимого. Вы хотите продолжить?", "collabStopOverridePrompt": "Остановка сессии перезапишет ваш предыдущий, локально сохранённый рисунок. Вы уверены? \n\n(Если вы хотите оставить ваш локальный рисунок, просто закройте вкладку браузера)", "errorAddingToLibrary": "Не удалось добавить объект в библиотеку", "errorRemovingFromLibrary": "Не удалось удалить объект из библиотеки", "confirmAddLibrary": "Будет добавлено {{numShapes}} фигур в вашу библиотеку. Продолжить?", "imageDoesNotContainScene": "Это изображение не содержит данных сцены. Вы включили встраивание сцены во время экспорта?", "cannotRestoreFromImage": "Сцена не может быть восстановлена из этого изображения", "invalidSceneUrl": "Невозможно импортировать сцену с предоставленного URL. Неверный формат, или не содержит верных Excalidraw JSON данных.", "resetLibrary": "Это очистит вашу библиотеку. Вы уверены?", "removeItemsFromsLibrary": "Удалить {{count}} объект(ов) из библиотеки?", "invalidEncryptionKey": "Ключ шифрования должен состоять из 22 символов. Одновременное редактирование отключено.", "collabOfflineWarning": "Отсутствует интернет-соединение.\nВаши изменения не будут сохранены!"}, "errors": {"unsupportedFileType": "Неподдерживаемый тип файла.", "imageInsertError": "Не удалось вставить изображение. Попробуйте позже...", "fileTooBig": "Очень большой файл. Максимально разрешенный размер {{maxSize}}.", "svgImageInsertError": "Не удалось вставить изображение SVG. Разметка SVG выглядит недействительной.", "failedToFetchImage": "Не удалось получить изображение.", "invalidSVGString": "Некорректный SVG.", "cannotResolveCollabServer": "Не удалось подключиться к серверу совместного редактирования. Перезагрузите страницу и повторите попытку.", "importLibraryError": "Не удалось загрузить библиотеку", "collabSaveFailed": "Не удалось сохранить в базу данных. Если проблема повторится, нужно будет сохранить файл локально, чтобы быть уверенным, что вы не потеряете вашу работу.", "collabSaveFailed_sizeExceeded": "Не удалось сохранить в базу данных. Похоже, что холст слишком большой. Нужно сохранить файл локально, чтобы быть уверенным, что вы не потеряете вашу работу.", "imageToolNotSupported": "Изображения отключены.", "brave_measure_text_error": {"line1": "Похоже, вы используете браузер Brave с включенной опцией <bold>Агрессивно блокировать отслеживание</bold>.", "line2": "Это может привести к поломке <bold>Текстовых объектов</bold> на рисунке.", "line3": "Мы настоятельно рекомендуем отключить эту настройку. Для этого нужно выполнить <link>эти шаги</link>.", "line4": "Если отключение этой настройки не исправит отображение текстовых объектов, создайте <issueLink>issue</issueLink> на нашем GitHub или напишите нам в <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "", "iframe": "Элементы IFrame не могут быть добавлены в библиотеку.", "image": ""}, "asyncPasteFailedOnRead": "Не удалось вставить (невозможно прочитать из системного буфера обмена).", "asyncPasteFailedOnParse": "Не удалось вставить.", "copyToSystemClipboardFailed": "Не удалось скопировать в буфер обмена."}, "toolBar": {"selection": "Выделение области", "image": "Вставить изображение", "rectangle": "Прямоугольник", "diamond": "Ромб", "ellipse": "Элли<PERSON><PERSON>", "arrow": "Cтрелка", "line": "Линия", "freedraw": "Чертить", "text": "Текст", "library": "Библиотека", "lock": "Сохранять выбранный инструмент активным после рисования", "penMode": "Режим пера - предотвращение касания", "link": "Добавить/обновить ссылку для выбранной фигуры", "eraser": "Л<PERSON>с<PERSON><PERSON><PERSON>", "frame": "", "magicframe": "", "embeddable": "", "laser": "Лазерная указка", "hand": "Рука (перемещение холста)", "extraTools": "", "mermaidToExcalidraw": "Из Mermaid в Excalidraw", "magicSettings": "Параметры AI"}, "headings": {"canvasActions": "Операции холста", "selectedShapeActions": "Операции выбранной фигуры", "shapes": "Фигуры"}, "hints": {"canvasPanning": "Чтобы двигать холст, удерживайте колесо мыши или пробел во время перетаскивания, или используйте инструмент \"Рука\"", "linearElement": "Нажмите, чтобы начать несколько точек, перетащите для одной линии", "freeDraw": "Нажмите и перетаскивайте, отпустите по завершении", "text": "Совет: при выбранном инструменте выделения дважды щёлкните в любом месте, чтобы добавить текст", "embeddable": "", "text_selected": "Дважды щелкните мышью или нажмите ENTER, чтобы редактировать текст", "text_editing": "Нажмите Escape либо Ctrl или Cmd + ENTER для завершения редактирования", "linearElementMulti": "Кликните на последней точке или нажмите Escape или Enter чтобы закончить", "lockAngle": "Вы можете ограничить угол удерживая SHIFT", "resize": "Вы можете ограничить пропорции, удерживая SHIFT во время изменения размеров,\nудерживайте ALT чтобы изменить размер из центра", "resizeImage": "Вы можете свободно изменять размеры, удерживая кнопку SHIFT,\nудерживайте кнопку ALT, чтобы изменять размер относительно центра", "rotate": "Вы можете ограничить углы, удерживая SHIFT во время вращения", "lineEditor_info": "Удерживайте CtrlOrCmd и дважды кликните или нажмите CtrlOrCmd + Enter для редактирования точек", "lineEditor_pointSelected": "Нажмите Delete для удаления точки (точек),\nCtrl+D или Cmd+D для дублирования, перетащите для перемещения", "lineEditor_nothingSelected": "Выберите точку для редактирования (удерживайте SHIFT выбора нескольких точек),\nили удерживайте Alt и кликните для добавления новых точек", "placeImage": "Щелкните, чтобы разместить изображение, или нажмите и перетащите, чтобы установить его размер вручную", "publishLibrary": "Опубликовать свою собственную библиотеку", "bindTextToElement": "Нажмите Enter для добавления текста", "deepBoxSelect": "Удерживайте Ctrl или Cmd для глубокого выделения, чтобы предотвратить перетаскивание", "eraserRevert": "Удерживайте Alt, чтобы вернуть элементы, отмеченные для удаления", "firefox_clipboard_write": "Эта функция может быть включена при изменении значения флага \"dom.events.asyncClipboard.clipboardItem\" на \"true\". Чтобы изменить флаги браузера в Firefox, посетите страницу \"about:config\".", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Не удается отобразить предпросмотр", "canvasTooBig": "Сцена слишком большая.", "canvasTooBigTip": "Совет: попробуйте сблизить элементы рисунка."}, "errorSplash": {"headingMain": "Возникла ошибка. Попробуйте <button>перезагрузить страницу.</button>", "clearCanvasMessage": "Если перезагрузка страницы не помогла, попробуйте <button>очистить холст.</button>", "clearCanvasCaveat": " Текущая работа будет утеряна ", "trackedToSentry": "Ошибка с идентификатором {{eventId}} отслеживается в нашей системе.", "openIssueMessage": "Для безопасности информация о вашей сцене не включена в ошибку. Если в сцене нет ничего конфиденциального, пожалуйста следуйте нашим <button>баг трекере.</button> Пожалуйста, приложите информацию ниже, скопировав и вставив её, в issue GitHub.", "sceneContent": "Содержание сцены:"}, "roomDialog": {"desc_intro": "Вы можете пригласить людей в текущую сцену для совместной работы.", "desc_privacy": "Не беспокойтесь — во время сеанса используется сквозное шифрование. Всё, что вы нарисуете, останется конфиденциальным и не будет доступно даже нашему серверу.", "button_startSession": "Начать сеанс", "button_stopSession": "Завершить сеанс", "desc_inProgressIntro": "Сеанс совместной работы запущен.", "desc_shareLink": "Поделитесь этой ссылкой со всеми участниками:", "desc_exitSession": "Завершив сеанс, вы выйдете из комнаты, но сможете продолжить работать с документом локально. Это не повлияет на работу других пользователей — они смогут продолжить совместную работу с их версией документа.", "shareTitle": "Присоединиться к активной совместной сессии на Excalidraw"}, "errorDialog": {"title": "Ошибка"}, "exportDialog": {"disk_title": "Сохранить на диск", "disk_details": "Экспортировать данные сцены в файл, из которого можно импортировать позже.", "disk_button": "Сохранить в файл", "link_title": "Поделитесь ссылкой", "link_details": "Экспорт ссылки только для чтения.", "link_button": "Экспорт в ссылку", "excalidrawplus_description": "Сохраните сцену в ваше рабочее пространство Excalidraw+.", "excalidrawplus_button": "Экспорт", "excalidrawplus_exportError": "Не удалось экспортировать в Excalidraw+ на данный момент..."}, "helpDialog": {"blog": "Прочитайте наш блог", "click": "нажать", "deepSelect": "Глубокое выделение", "deepBoxSelect": "Глубокое выделение рамкой, и предотвращение перетаскивания", "curvedArrow": "Изогнутая стрелка", "curvedLine": "Изогнутая линия", "documentation": "Документация", "doubleClick": "двойной клик", "drag": "перетащить", "editor": "Редактор", "editLineArrowPoints": "Редактировать концы линий/стрелок", "editText": "Редактировать текст / добавить метку", "github": "Нашли проблему? Отправьте", "howto": "Следуйте нашим инструкциям", "or": "или", "preventBinding": "Предотвращать привязку стрелок", "tools": "Инструменты", "shortcuts": "Горячие клавиши", "textFinish": "Закончить редактирование (текстовый редактор)", "textNewLine": "Добавить новую строку (текстовый редактор)", "title": "Помощь", "view": "Просмотр", "zoomToFit": "Отмастштабировать, чтобы поместились все элементы", "zoomToSelection": "Увеличить до выделенного", "toggleElementLock": "Заблокировать/разблокировать выделение", "movePageUpDown": "Сдвинуть страницу вверх/вниз", "movePageLeftRight": "Сдвинуть страницу вправо/влево"}, "clearCanvasDialog": {"title": "Очисти<PERSON>ь холст"}, "publishDialog": {"title": "Опубликовать библиотеку", "itemName": "Название объекта", "authorName": "Имя автора", "githubUsername": "Имя пользователя GitHub", "twitterUsername": "Имя пользователя в Twitter", "libraryName": "Название библиотеки", "libraryDesc": "Описание библиотеки", "website": "Веб-сайт", "placeholder": {"authorName": "Ваше имя или имя пользователя", "libraryName": "Название вашей библиотеки", "libraryDesc": "Описание вашей библиотеки, которое поможет людям понять её назначение", "githubHandle": "Имя пользовател<PERSON> GitHub (необязательно), чтобы вы смогли редактировать библиотеку после её отправки на проверку", "twitterHandle": "Имя пользователя в Twitter (необязательно), чтобы мы знали, кого упомянуть при продвижении в Twitter", "website": "Ссылка на ваш личный или какой-то другой сайт (необязательно)"}, "errors": {"required": "Обязательно", "website": "Введите допустимый URL-адрес"}, "noteDescription": "Отправить вашу библиотеку для включения в <link>хранилище публичных библиотек</link>, чтобы другие люди могли использовать объекты из вашей библиотеки в своих рисунках.", "noteGuidelines": "Библиотека должна быть подтверждена вручную. Пожалуйста, прочтите <link>рекомендации</link> перед отправкой. Вам понадобится учетная запись GitHub, чтобы общаться и вносить изменения при необходимости, но это не обязательно.", "noteLicense": "Выполняя отправку, вы соглашаетесь с тем, что библиотека будет опубликована под <link>лицензией MIT, </link>, что, вкратце, означает, что каждый может использовать её без ограничений.", "noteItems": "Каждый объект в библиотеке должен иметь свое собственное имя, чтобы по нему можно было фильтровать. Следующие объекты библиотеки будут включены:", "atleastOneLibItem": "Пожалуйста, выберите хотя бы один объект в библиотеке, чтобы начать", "republishWarning": "Примечание: некоторые из выбранных элементов помечены как уже опубликованные/отправленные. Вы должны повторно отправить элементы только при обновлении существующей библиотеки или сдаче работы."}, "publishSuccessDialog": {"title": "Библиотека отправлена", "content": "Благода<PERSON>и<PERSON> вас, {{authorName}}. Ваша библиотека была отправлена на проверку. Вы можете отслеживать статус<link>здесь</link>"}, "confirmDialog": {"resetLibrary": "Сброс библиотеки", "removeItemsFromLib": "Удалить выбранные объекты из библиотеки"}, "imageExportDialog": {"header": "Экспортировать изображение", "label": {"withBackground": "Фон", "onlySelected": "Только выделенное", "darkMode": "Темная тема", "embedScene": "Встроить сцену", "scale": "Масш<PERSON><PERSON><PERSON>", "padding": "Отступ"}, "tooltip": {"embedScene": "Сцена будет сохранена в PNG/SVG файл так, чтобы всю сцену можно будет восстановить из этого файла. Это увеличит размер файла."}, "title": {"exportToPng": "Экспорт в PNG", "exportToSvg": "Экспорт в SVG", "copyPngToClipboard": "Скопировать PNG в буфер обмена"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Скопировать в буфер обмена"}}, "encrypted": {"tooltip": "Ваши данные защищены сквозным (End-to-end) шифрованием. Серверы Excalidraw никогда не получат доступ к ним.", "link": "Запись блога о сквозном шифровании в Excalidraw"}, "stats": {"angle": "Угол", "element": "Элемент", "elements": "Элементы", "height": "Высота", "scene": "Сцены", "selected": "Выб<PERSON><PERSON>н", "storage": "Храни<PERSON><PERSON><PERSON>е", "title": "Статистика для ботаников", "total": "Всего", "version": "Версия", "versionCopy": "Копировать", "versionNotAvailable": "Версия не доступна", "width": "Ши<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Добавлено в библиотеку", "copyStyles": "Скопированы стили.", "copyToClipboard": "Скопировано в буфер обмена.", "copyToClipboardAsPng": "{{exportSelection}} скопировано как PNG ({{exportColorScheme}})", "fileSaved": "<PERSON>айл со<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "fileSavedToFilename": "Сохранено в {filename}", "canvas": "холст", "selection": "выделение", "pasteAsSingleElement": "Используйте {{shortcut}}, чтобы вставить один объект,\nили вставьте в существующий текстовый редактор", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Прозрачный", "black": "Чёрн<PERSON>й", "white": "Белый", "red": "Красный", "pink": "Розовый", "grape": "Виноградный", "violet": "Фиолетовый", "gray": "Серый", "blue": "Синий", "cyan": "Го<PERSON><PERSON><PERSON><PERSON><PERSON>", "teal": "Бирюзовый", "green": "Зелёный", "yellow": "Жёлтый", "orange": "Оранжевый", "bronze": "Бронзовый"}, "welcomeScreen": {"app": {"center_heading": "Все ваши данные сохраняются локально в вашем браузере.", "center_heading_plus": "Хотите перейти на Excalidraw+?", "menuHint": "Экспорт, настройки, языки, ..."}, "defaults": {"menuHint": "Экспорт, настройки и другое...", "center_heading": "Диаграммы. Просто.", "toolbarHint": "Выберите инструмент и начните рисовать!", "helpHint": "Сочетания клавиш и помощь"}}, "colorPicker": {"mostUsedCustomColors": "Часто используемые пользовательские цвета", "colors": "Цвета", "shades": "Оттенки", "hexCode": "Шестнадцатеричный код", "noShades": "Нет доступных оттенков для этого цвета"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Экспортировать как изображение", "button": "Экспортировать как изображение", "description": ""}, "saveToDisk": {"title": "Сохранить на диск", "button": "Сохранить на диск", "description": ""}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Экспорт в Excalidraw+", "description": ""}}, "modal": {"loadFromFile": {"title": "Загрузить из файла", "button": "Загрузить из файла", "description": ""}, "shareableLink": {"title": "Загрузить по ссылке", "button": "", "description": ""}}}, "mermaid": {"title": "Из Mermaid в Excalidraw", "button": "Вставить", "description": "", "syntax": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mermaid", "preview": "Предпросмотр"}}