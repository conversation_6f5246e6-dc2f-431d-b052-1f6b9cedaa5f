# UK Schools Lead Generation System - Environment Variables

# =============================================================================
# UK GOVERNMENT APIs (FREE)
# =============================================================================

# Companies House API - Register at: https://developer.company-information.service.gov.uk/get-started/
COMPANIES_HOUSE_API_KEY=your_companies_house_key_here

# =============================================================================
# CONTACT ENRICHMENT APIs
# =============================================================================

# Apollo.io - Sign up at: https://www.apollo.io/
APOLLO_API_KEY=your_apollo_key_here

# Hunter.io - Sign up at: https://hunter.io/
HUNTER_API_KEY=your_hunter_key_here

# NeverBounce - Sign up at: https://neverbounce.com/
NEVERBOUNCE_API_KEY=your_neverbounce_key_here

# LinkedIn Sales Navigator (Optional)
LINKEDIN_ACCESS_TOKEN=your_linkedin_token_here

# =============================================================================
# CRM INTEGRATION
# =============================================================================

# HubSpot - Create account at: https://www.hubspot.com/
HUBSPOT_API_KEY=your_hubspot_key_here
HUBSPOT_PORTAL_ID=your_hubspot_portal_id

# Pipedrive (Alternative to HubSpot)
PIPEDRIVE_API_TOKEN=your_pipedrive_token_here
PIPEDRIVE_COMPANY_DOMAIN=your_company_domain

# =============================================================================
# EMAIL INFRASTRUCTURE
# =============================================================================

# SendGrid - Sign up at: https://sendgrid.com/
SENDGRID_API_KEY=your_sendgrid_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your Company Name

# Mailgun (Alternative to SendGrid)
MAILGUN_API_KEY=your_mailgun_key_here
MAILGUN_DOMAIN=your_mailgun_domain.com

# =============================================================================
# AI SERVICES
# =============================================================================

# OpenAI - Create account at: https://platform.openai.com/
OPENAI_API_KEY=your_openai_key_here

# =============================================================================
# DATABASE
# =============================================================================

# SQLite (Default - no setup required)
DATABASE_URL=./data/leads.db

# PostgreSQL (Production alternative)
# DATABASE_URL=postgresql://username:password@localhost:5432/lead_generation

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Server Configuration
PORT=3000
NODE_ENV=development

# Rate Limiting
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW_MS=900000

# Email Campaign Settings
EMAIL_BATCH_SIZE=50
EMAIL_DELAY_BETWEEN_BATCHES_MS=60000

# Data Collection Settings
DATA_COLLECTION_BATCH_SIZE=100
MAX_RETRIES=3

# =============================================================================
# COMPLIANCE & SECURITY
# =============================================================================

# GDPR Compliance
GDPR_CONSENT_REQUIRED=true
DATA_RETENTION_DAYS=730

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Google Analytics
GA_TRACKING_ID=your_ga_tracking_id

# Webhook URLs for notifications
SLACK_WEBHOOK_URL=your_slack_webhook_url
DISCORD_WEBHOOK_URL=your_discord_webhook_url

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Test Mode (prevents actual emails from being sent)
TEST_MODE=true

# Debug Logging
DEBUG_LEVEL=info

# Mock API Responses (for development)
MOCK_APIS=false
