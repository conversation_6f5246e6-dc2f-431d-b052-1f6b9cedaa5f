import winston from 'winston';
import { config } from '../config/index.js';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which logs to print based on environment
const level = () => {
  const env = config.nodeEnv || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: format
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: 'logs/combined.log',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  })
];

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
  // Don't exit on handled exceptions
  exitOnError: false
});

// Create a stream object for HTTP request logging
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  }
};

// Add method for API call logging
logger.apiCall = (service, endpoint, method = 'GET', statusCode = null, duration = null) => {
  const message = `API Call: ${service} ${method} ${endpoint}`;
  const meta = { service, endpoint, method, statusCode, duration };
  
  if (statusCode >= 400) {
    logger.error(message, meta);
  } else {
    logger.info(message, meta);
  }
};

// Add method for data processing logging
logger.dataProcessing = (operation, recordsProcessed, errors = 0) => {
  const message = `Data Processing: ${operation} - Processed: ${recordsProcessed}, Errors: ${errors}`;
  logger.info(message, { operation, recordsProcessed, errors });
};

// Add method for email campaign logging
logger.emailCampaign = (campaignId, action, count = null, details = null) => {
  const message = `Email Campaign ${campaignId}: ${action}`;
  const meta = { campaignId, action, count, details };
  logger.info(message, meta);
};

export default logger;
