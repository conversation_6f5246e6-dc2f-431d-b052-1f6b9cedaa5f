{"labels": {"paste": "Yapışdır", "pasteAsPlaintext": "<PERSON><PERSON>z mətn kimi yapış<PERSON>ırın", "pasteCharts": "Diaqramları yapışdırın", "selectAll": "Hamısını seç", "multiSelect": "Seçimə element əlavə edin", "moveCanvas": "Kanvası köçürün", "cut": "<PERSON><PERSON>s", "copy": "Kopyala", "copyAsPng": "PNG olaraq panoya kopyala", "copyAsSvg": "SVG olaraq panoya kopyala", "copyText": "Mətn olaraq panoya kopyala", "copySource": "", "convertToCode": "", "bringForward": "<PERSON><PERSON><PERSON>ı", "sendToBack": "G<PERSON><PERSON>ə göndərin", "bringToFront": "<PERSON><PERSON><PERSON> gətirin", "sendBackward": "G<PERSON><PERSON>ə göndərin", "delete": "Sil", "copyStyles": "<PERSON><PERSON><PERSON>", "pasteStyles": "<PERSON><PERSON><PERSON>", "stroke": "<PERSON><PERSON> rəngi", "background": "<PERSON><PERSON><PERSON> fon", "fill": "<PERSON><PERSON><PERSON>", "strokeWidth": "<PERSON><PERSON> eni", "strokeStyle": "<PERSON><PERSON> stili", "strokeStyle_solid": "Solid", "strokeStyle_dashed": "<PERSON><PERSON><PERSON>", "strokeStyle_dotted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sloppiness": "Səliqəsizlik", "opacity": "Şəffaflıq", "textAlign": "Mətni uyğunlaşdır", "edges": "<PERSON><PERSON><PERSON><PERSON>", "sharp": "<PERSON><PERSON><PERSON>", "round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arrowheads": "Ox ucları", "arrowhead_none": "<PERSON><PERSON>", "arrowhead_arrow": "Ox", "arrowhead_bar": "Çubuq", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Üçbucaq", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "Şrift ölçüsü", "fontFamily": "Şrift qrupu", "addWatermark": "\"Made with Excalidraw\" əlavə et", "handDrawn": "<PERSON>ll<PERSON> çəkilmiş", "normal": "Normal", "code": "Kod", "small": "Kiçik", "medium": "Orta", "large": "Böyük", "veryLarge": "Çox böyük", "solid": "Solid", "hachure": "Ştrix", "zigzag": "Ziqzaq", "crossHatch": "Çarpaz dəlik", "thin": "İncə", "bold": "Qalın", "left": "Sol", "center": "Mərkəz", "right": "Sağ", "extraBold": "Ekstra qalın", "architect": "<PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "cartoonist": "Karikaturaçı", "fileTitle": "<PERSON><PERSON> adı", "colorPicker": "<PERSON>əng seçən", "canvasColors": "Kanvas üzərində istifadə olunur", "canvasBackground": "Kanvas arxa fonu", "drawingCanvas": "Kanvas çəkmək", "layers": "Qatlar", "actions": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON>tlər", "language": "Dil", "liveCollaboration": "Canlı əməkdaşlıq...", "duplicateSelection": "Dublikat", "untitled": "Başlıqsız", "name": "Ad", "yourName": "Adınız", "madeWithExcalidraw": "Excalidraw ilə hazırlanmışdır", "group": "Qrup şəklində seçim", "ungroup": "Qrupsuz <PERSON>", "collaborators": "", "showGrid": "", "addToLibrary": "", "removeFromLibrary": "", "libraryLoadingMessage": "", "libraries": "", "loadingScene": "", "align": "", "alignTop": "", "alignBottom": "", "alignLeft": "", "alignRight": "", "centerVertically": "", "centerHorizontally": "", "distributeHorizontally": "", "distributeVertically": "", "flipHorizontal": "", "flipVertical": "", "viewMode": "", "share": "", "showStroke": "", "showBackground": "", "toggleTheme": "", "personalLib": "", "excalidrawLib": "", "decreaseFontSize": "", "increaseFontSize": "", "unbindText": "", "bindText": "", "createContainerFromText": "", "link": {"edit": "", "editEmbed": "", "create": "", "createEmbed": "", "label": "", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "", "exit": ""}, "elementLock": {"lock": "", "unlock": "", "lockAll": "", "unlockAll": ""}, "statusPublished": "", "sidebarLock": "", "selectAllElementsInFrame": "", "removeAllElementsFromFrame": "", "eyeDropper": "", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "", "hint_emptyLibrary": "", "hint_emptyPrivateLibrary": ""}, "buttons": {"clearReset": "", "exportJSON": "", "exportImage": "", "export": "", "copyToClipboard": "", "save": "", "saveAs": "", "load": "", "getShareableLink": "", "close": "", "selectLanguage": "", "scrollBackToContent": "", "zoomIn": "", "zoomOut": "", "resetZoom": "", "menu": "", "done": "", "edit": "", "undo": "", "redo": "", "resetLibrary": "", "createNewRoom": "", "fullScreen": "", "darkMode": "", "lightMode": "", "zenMode": "", "objectsSnapMode": "", "exitZenMode": "", "cancel": "", "clear": "", "remove": "", "embed": "", "publishLibrary": "", "submit": "", "confirm": "", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "", "couldNotCreateShareableLink": "", "couldNotCreateShareableLinkTooBig": "", "couldNotLoadInvalidFile": "", "importBackendFailed": "", "cannotExportEmptyCanvas": "", "couldNotCopyToClipboard": "", "decryptFailed": "", "uploadedSecurly": "", "loadSceneOverridePrompt": "", "collabStopOverridePrompt": "", "errorAddingToLibrary": "", "errorRemovingFromLibrary": "", "confirmAddLibrary": "", "imageDoesNotContainScene": "", "cannotRestoreFromImage": "", "invalidSceneUrl": "", "resetLibrary": "", "removeItemsFromsLibrary": "", "invalidEncryptionKey": "", "collabOfflineWarning": ""}, "errors": {"unsupportedFileType": "", "imageInsertError": "", "fileTooBig": "", "svgImageInsertError": "", "failedToFetchImage": "", "invalidSVGString": "", "cannotResolveCollabServer": "", "importLibraryError": "", "collabSaveFailed": "", "collabSaveFailed_sizeExceeded": "", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "", "line2": "", "line3": "", "line4": ""}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "", "image": "", "rectangle": "", "diamond": "", "ellipse": "", "arrow": "", "line": "", "freedraw": "", "text": "", "library": "", "lock": "", "penMode": "", "link": "", "eraser": "", "frame": "", "magicframe": "", "embeddable": "", "laser": "", "hand": "", "extraTools": "", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "", "selectedShapeActions": "", "shapes": ""}, "hints": {"canvasPanning": "", "linearElement": "", "freeDraw": "", "text": "", "embeddable": "", "text_selected": "", "text_editing": "", "linearElementMulti": "", "lockAngle": "", "resize": "", "resizeImage": "", "rotate": "", "lineEditor_info": "", "lineEditor_pointSelected": "", "lineEditor_nothingSelected": "", "placeImage": "", "publishLibrary": "", "bindTextToElement": "", "deepBoxSelect": "", "eraserRevert": "", "firefox_clipboard_write": "", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "", "canvasTooBig": "", "canvasTooBigTip": ""}, "errorSplash": {"headingMain": "", "clearCanvasMessage": "", "clearCanvasCaveat": "", "trackedToSentry": "", "openIssueMessage": "", "sceneContent": ""}, "roomDialog": {"desc_intro": "", "desc_privacy": "", "button_startSession": "", "button_stopSession": "", "desc_inProgressIntro": "", "desc_shareLink": "", "desc_exitSession": "", "shareTitle": ""}, "errorDialog": {"title": ""}, "exportDialog": {"disk_title": "", "disk_details": "", "disk_button": "", "link_title": "", "link_details": "", "link_button": "", "excalidrawplus_description": "", "excalidrawplus_button": "", "excalidrawplus_exportError": ""}, "helpDialog": {"blog": "", "click": "", "deepSelect": "", "deepBoxSelect": "", "curvedArrow": "", "curvedLine": "", "documentation": "", "doubleClick": "", "drag": "", "editor": "", "editLineArrowPoints": "", "editText": "", "github": "", "howto": "", "or": "", "preventBinding": "", "tools": "", "shortcuts": "", "textFinish": "", "textNewLine": "", "title": "", "view": "", "zoomToFit": "", "zoomToSelection": "", "toggleElementLock": "", "movePageUpDown": "", "movePageLeftRight": ""}, "clearCanvasDialog": {"title": ""}, "publishDialog": {"title": "", "itemName": "", "authorName": "", "githubUsername": "", "twitterUsername": "", "libraryName": "", "libraryDesc": "", "website": "", "placeholder": {"authorName": "", "libraryName": "", "libraryDesc": "", "githubHandle": "", "twitterHandle": "", "website": ""}, "errors": {"required": "", "website": ""}, "noteDescription": "", "noteGuidelines": "", "noteLicense": "", "noteItems": "", "atleastOneLibItem": "", "republishWarning": ""}, "publishSuccessDialog": {"title": "", "content": ""}, "confirmDialog": {"resetLibrary": "", "removeItemsFromLib": ""}, "imageExportDialog": {"header": "", "label": {"withBackground": "", "onlySelected": "", "darkMode": "", "embedScene": "", "scale": "", "padding": ""}, "tooltip": {"embedScene": ""}, "title": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}, "button": {"exportToPng": "", "exportToSvg": "", "copyPngToClipboard": ""}}, "encrypted": {"tooltip": "", "link": ""}, "stats": {"angle": "", "element": "", "elements": "", "height": "", "scene": "", "selected": "", "storage": "", "title": "", "total": "", "version": "", "versionCopy": "", "versionNotAvailable": "", "width": ""}, "toast": {"addedToLibrary": "", "copyStyles": "", "copyToClipboard": "", "copyToClipboardAsPng": "", "fileSaved": "", "fileSavedToFilename": "", "canvas": "", "selection": "", "pasteAsSingleElement": "", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "", "black": "", "white": "", "red": "", "pink": "", "grape": "", "violet": "", "gray": "", "blue": "", "cyan": "", "teal": "", "green": "", "yellow": "", "orange": "", "bronze": ""}, "welcomeScreen": {"app": {"center_heading": "", "center_heading_plus": "", "menuHint": ""}, "defaults": {"menuHint": "", "center_heading": "", "toolbarHint": "", "helpHint": ""}}, "colorPicker": {"mostUsedCustomColors": "", "colors": "", "shades": "", "hexCode": "", "noShades": ""}, "overwriteConfirm": {"action": {"exportToImage": {"title": "", "button": "", "description": ""}, "saveToDisk": {"title": "", "button": "", "description": ""}, "excalidrawPlus": {"title": "", "button": "", "description": ""}}, "modal": {"loadFromFile": {"title": "", "button": "", "description": ""}, "shareableLink": {"title": "", "button": "", "description": ""}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}