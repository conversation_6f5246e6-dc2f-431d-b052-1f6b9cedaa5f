.sidebar {
  height: 100%;
  width: 0;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: #111;
  overflow-x: hidden;
  transition: 0.5s;
  padding-top: 60px;

  &.open {
    width: 300px;
  }

  &-links {
    display: flex;
    flex-direction: column;
    padding: 20px;

    button {
      padding: 10px;
      margin: 10px;
      background: #faa2c1;
      color: #fff;
      border: none;
      cursor: pointer;
    }
  }
}

.sidebar a {
  padding: 8px 8px 8px 32px;
  text-decoration: none;
  font-size: 25px;
  color: #818181;
  display: block;
  transition: 0.3s;
}

.sidebar a:hover {
  color: #f1f1f1;
}

.sidebar .closebtn {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 36px;
  margin-left: 50px;
}

.openbtn {
  font-size: 20px;
  cursor: pointer;
  background-color: #111;
  color: white;
  padding: 10px 15px;
  border: none;
  display: flex;
  margin-left: 50px;
}
.sidebar-open {
  margin-left: 300px;
}
