{"labels": {"paste": "Colar", "pasteAsPlaintext": "Colar como texto sem formatação", "pasteCharts": "Colar g<PERSON>", "selectAll": "Selecionar tudo", "multiSelect": "Adicionar elemento à seleção", "moveCanvas": "Mover tela", "cut": "Recortar", "copy": "Copiar", "copyAsPng": "Copiar para a área de transferência como PNG", "copyAsSvg": "Copiar para a área de transferência como SVG", "copyText": "Copiar para área de transferência como texto", "copySource": "", "convertToCode": "", "bringForward": "Trazer para a frente", "sendToBack": "Enviar para o fundo", "bringToFront": "Trazer para o primeiro plano", "sendBackward": "Enviar para trás", "delete": "<PERSON><PERSON><PERSON>", "copyStyles": "Copiar os estilos", "pasteStyles": "Colar os estilos", "stroke": "Contorno", "background": "Fundo", "fill": "Preenchimento", "strokeWidth": "Espessura do traço", "strokeStyle": "Estilo de traço", "strokeStyle_solid": "<PERSON><PERSON><PERSON><PERSON>", "strokeStyle_dashed": "<PERSON><PERSON><PERSON>", "strokeStyle_dotted": "Pontil<PERSON><PERSON>", "sloppiness": "Precisão do traço", "opacity": "Opacidade", "textAlign": "Alinhamento do texto", "edges": "Ares<PERSON>", "sharp": "<PERSON><PERSON>", "round": "<PERSON><PERSON><PERSON><PERSON>", "arrowheads": "Pontas", "arrowhead_none": "<PERSON><PERSON><PERSON><PERSON>", "arrowhead_arrow": "Fle<PERSON>", "arrowhead_bar": "Barr<PERSON>", "arrowhead_circle": "", "arrowhead_circle_outline": "", "arrowhead_triangle": "Triângulo", "arrowhead_triangle_outline": "", "arrowhead_diamond": "", "arrowhead_diamond_outline": "", "fontSize": "<PERSON><PERSON><PERSON>", "fontFamily": "<PERSON><PERSON><PERSON><PERSON> da fonte", "addWatermark": "Adicionar \"Feito com Excalidraw\"", "handDrawn": "Manus<PERSON>rito", "normal": "Normal", "code": "Código", "small": "Pequeno", "medium": "Médio", "large": "Grande", "veryLarge": "<PERSON>ito grande", "solid": "<PERSON><PERSON><PERSON><PERSON>", "hachure": "<PERSON><PERSON><PERSON>", "zigzag": "Zigue-zague", "crossHatch": "<PERSON><PERSON><PERSON> cruzada", "thin": "Fino", "bold": "<PERSON><PERSON><PERSON><PERSON>", "left": "E<PERSON>rda", "center": "Centralizar", "right": "<PERSON><PERSON><PERSON>", "extraBold": "<PERSON><PERSON> es<PERSON>so", "architect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "artist": "Artista", "cartoonist": "Cartunista", "fileTitle": "Nome do arquivo", "colorPicker": "<PERSON><PERSON>or de cores", "canvasColors": "Usado na tela", "canvasBackground": "Fundo da tela", "drawingCanvas": "Tela de desenho", "layers": "Camadas", "actions": "Ações", "language": "Idioma", "liveCollaboration": "Colaboração ao vivo...", "duplicateSelection": "Duplicar", "untitled": "<PERSON><PERSON> tí<PERSON>lo", "name": "Nome", "yourName": "Seu nome", "madeWithExcalidraw": "Feito com Excalidraw", "group": "Agrupar se<PERSON>", "ungroup": "Desagrupar se<PERSON>ção", "collaborators": "Colaboradores", "showGrid": "Mostrar grade", "addToLibrary": "Adicionar à biblioteca", "removeFromLibrary": "Remover da biblioteca", "libraryLoadingMessage": "Carregando biblioteca…", "libraries": "Procurar bibliotecas", "loadingScene": "<PERSON>egando cena…", "align": "Alinhamento", "alignTop": "<PERSON><PERSON><PERSON> ao topo", "alignBottom": "<PERSON><PERSON><PERSON>", "alignLeft": "Alinhar à esquerda", "alignRight": "Alinhar à direita", "centerVertically": "Centralizar verticalmente", "centerHorizontally": "Centralizar horizontalmente", "distributeHorizontally": "Distribuir horizontalmente", "distributeVertically": "Distribuir verticalmente", "flipHorizontal": "Inverter horizontalmente", "flipVertical": "Inverter verticalmente", "viewMode": "Modo de visualização", "share": "Compartilhar", "showStroke": "Exibir seletor de cores do traço", "showBackground": "<PERSON><PERSON>r seletor de cores do fundo", "toggleTheme": "Alternar tema", "personalLib": "Biblioteca Pessoal", "excalidrawLib": "Biblioteca do Excalidraw", "decreaseFontSize": "<PERSON><PERSON><PERSON><PERSON> o tamanho da fonte", "increaseFontSize": "Aumentar o tamanho da fonte", "unbindText": "Desvincular texto", "bindText": "Vincular texto ao contêiner", "createContainerFromText": "Envolver texto em um contêiner", "link": {"edit": "Editar link", "editEmbed": "", "create": "Criar link", "createEmbed": "", "label": "Link", "labelEmbed": "", "empty": ""}, "lineEditor": {"edit": "<PERSON><PERSON> l<PERSON>", "exit": "<PERSON>r do <PERSON> de linha"}, "elementLock": {"lock": "Bloquear", "unlock": "Desb<PERSON>que<PERSON>", "lockAll": "Bloquear tudo", "unlockAll": "Desb<PERSON><PERSON>ar tudo"}, "statusPublished": "Publicado", "sidebarLock": "Manter barra lateral aberta", "selectAllElementsInFrame": "Selecionar todos os elementos no quadro", "removeAllElementsFromFrame": "Remover todos os elementos do quadro", "eyeDropper": "<PERSON><PERSON><PERSON><PERSON> cor da tela", "textToDiagram": "", "prompt": ""}, "library": {"noItems": "Nenhum item adicionado ainda...", "hint_emptyLibrary": "Selecione um item na tela para adicioná-lo aqui, ou instale uma biblioteca do repositório público, abaixo.", "hint_emptyPrivateLibrary": "Selecione um item na tela para adicioná-lo aqui."}, "buttons": {"clearReset": "Limpar o canvas e redefinir a cor de fundo", "exportJSON": "Exportar arquivo", "exportImage": "Exportar imagem...", "export": "<PERSON>var como...", "copyToClipboard": "Copiar para o clipboard", "save": "Salvar para o arquivo atual", "saveAs": "<PERSON><PERSON> como", "load": "Abrir", "getShareableLink": "Obter um link de compartilhamento", "close": "<PERSON><PERSON><PERSON>", "selectLanguage": "Selecionar idioma", "scrollBackToContent": "Voltar para o conteúdo", "zoomIn": "Aumentar zoom", "zoomOut": "Diminuir zoom", "resetZoom": "Redefinir zoom", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "resetLibrary": "Redefinir biblioteca", "createNewRoom": "Criar nova sala", "fullScreen": "Tela cheia", "darkMode": "<PERSON><PERSON> es<PERSON>ro", "lightMode": "<PERSON>do claro", "zenMode": "Modo Zen", "objectsSnapMode": "", "exitZenMode": "Sair do modo zen", "cancel": "<PERSON><PERSON><PERSON>", "clear": "Limpar", "remove": "Remover", "embed": "", "publishLibrary": "Publicar", "submit": "Enviar", "confirm": "Confirmar", "embeddableInteractionButton": ""}, "alerts": {"clearReset": "<PERSON>to irá limpar toda a tela. Você tem certeza?", "couldNotCreateShareableLink": "Não foi possível criar um link de compartilhamento.", "couldNotCreateShareableLinkTooBig": "Não foi possível criar um link compartilhável: a cena é muito grande", "couldNotLoadInvalidFile": "Não foi possível carregar o arquivo inválido", "importBackendFailed": "A importação do servidor falhou.", "cannotExportEmptyCanvas": "Não é possível exportar um canvas vazio.", "couldNotCopyToClipboard": "Não foi possível copiar para a área de transferência.", "decryptFailed": "Não foi possível descriptografar os dados.", "uploadedSecurly": "O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros não podem ler o conteúdo.", "loadSceneOverridePrompt": "Carregar um desenho externo substituirá o seu conteúdo existente. Deseja continuar?", "collabStopOverridePrompt": "Ao interromper a sessão, você substituirá seu desenho anterior, armazenado localmente. Você tem certeza?\n\n(Se você deseja manter seu desenho local, simplesmente feche a aba do navegador.)", "errorAddingToLibrary": "Não foi possível adicionar o item à biblioteca", "errorRemovingFromLibrary": "Não foi possível remover o item da biblioteca", "confirmAddLibrary": "<PERSON><PERSON> adici<PERSON> {{numShapes}} forma(s) à sua biblioteca. Tem certeza?", "imageDoesNotContainScene": "Esta imagem parece não conter dados de cenas. Você ativou a incorporação da cena durante a exportação?", "cannotRestoreFromImage": "Não foi possível restaurar a cena deste arquivo de imagem", "invalidSceneUrl": "Não foi possível importar a cena da URL fornecida. Ela está incompleta ou não contém dados JSON válidos do Excalidraw.", "resetLibrary": "Isto limpará a sua biblioteca. Você tem certeza?", "removeItemsFromsLibrary": "Excluir {{count}} item(ns) da biblioteca?", "invalidEncryptionKey": "A chave de encriptação deve ter 22 caracteres. A colaboração ao vivo está desabilitada.", "collabOfflineWarning": "Sem conexão com a internet disponível.\nSuas alterações não serão salvas!"}, "errors": {"unsupportedFileType": "Tipo de arquivo não suportado.", "imageInsertError": "Não foi possível inserir imagem. Tente novamente mais tarde...", "fileTooBig": "O arquivo é muito grande. O tamanho máximo permitido é {{maxSize}}.", "svgImageInsertError": "Não foi possível inserir a imagem SVG. A marcação SVG parece inválida.", "failedToFetchImage": "", "invalidSVGString": "SVG Inválido.", "cannotResolveCollabServer": "Não foi possível conectar-se ao servidor colaborativo. Por favor, recarregue a página e tente novamente.", "importLibraryError": "Não foi possível carregar a biblioteca", "collabSaveFailed": "Não foi possível salvar no banco de dados do servidor. Se os problemas persistirem, salve o arquivo localmente para garantir que não perca o seu trabalho.", "collabSaveFailed_sizeExceeded": "Não foi possível salvar no banco de dados do servidor, a tela parece ser muito grande. Se os problemas persistirem, salve o arquivo localmente para garantir que não perca o seu trabalho.", "imageToolNotSupported": "", "brave_measure_text_error": {"line1": "Parece que você está usando o navegador Brave com a configuração <bold>Bloquear Impressões Digitais</bold> no modo agressivo.", "line2": "<PERSON><PERSON> pode a<PERSON> que<PERSON> <bold>Elementos de Texto</bold> em seus desenhos.", "line3": "Recomendamos fortemente desativar essa configuração. Você pode acessar o <link>passo a passo</link> sobre como fazer isso.", "line4": "Se desativar essa configuração não corrigir a exibição de elementos de texto, por favor abra uma <issueLink>issue</issueLink> em nosso GitHub, ou mande uma mensagem em nosso <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "", "iframe": "", "image": ""}, "asyncPasteFailedOnRead": "", "asyncPasteFailedOnParse": "", "copyToSystemClipboardFailed": ""}, "toolBar": {"selection": "Se<PERSON><PERSON>", "image": "Inserir imagem", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "diamond": "Losango", "ellipse": "Elipse", "arrow": "Fle<PERSON>", "line": "<PERSON><PERSON>", "freedraw": "<PERSON><PERSON><PERSON>", "text": "Texto", "library": "Biblioteca", "lock": "Manter ativa a ferramenta selecionada após desenhar", "penMode": "<PERSON><PERSON> — impede o toque", "link": "Adicionar/Atualizar link para uma forma selecionada", "eraser": "Borracha", "frame": "Ferramenta de quadro", "magicframe": "", "embeddable": "", "laser": "", "hand": "Mão (ferramenta de rolagem)", "extraTools": "<PERSON><PERSON> fer<PERSON>as", "mermaidToExcalidraw": "", "magicSettings": ""}, "headings": {"canvasActions": "Ações da tela", "selectedShapeActions": "Ações das formas selecionadas", "shapes": "Formas"}, "hints": {"canvasPanning": "Para mover a tela, segure a roda do mouse ou a barra de espaço enquanto arrasta ou use a ferramenta de mão", "linearElement": "Clique para iniciar v<PERSON><PERSON>s pontos, arraste para uma única linha", "freeDraw": "Toque e arraste, solte quando terminar", "text": "Dica: você também pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de seleção", "embeddable": "", "text_selected": "C<PERSON> duplo ou tecle ENTER para editar o texto", "text_editing": "Pressione Esc ou Ctrl/Cmd+ENTER para encerrar a edição", "linearElementMulti": "Clique no último ponto ou pressione Escape ou Enter para terminar", "lockAngle": "Você pode restringir o ângulo segurando o SHIFT", "resize": "Você pode restringir proporções segurando SHIFT enquanto redimensiona,\nsegure ALT para redimensionar do centro", "resizeImage": "Você pode redimensionar livremente segurando SHIFT,\nsegure ALT para redimensionar a partir do centro", "rotate": "Você pode restringir os ângulos segurando SHIFT enquanto gira", "lineEditor_info": "Pressione CtrlOuCmd e duplo-clique ou pressione CtrlOuCmd + Enter para editar pontos", "lineEditor_pointSelected": "Pressione Delete para remover o(s) ponto(s),\nCtrl/Cmd+D para duplicar ou arraste para mover", "lineEditor_nothingSelected": "Selecione um ponto para editar (segure SHIFT para selecionar vários) ou segure Alt e clique para adicionar novos pontos", "placeImage": "Clique para colocar a imagem, ou clique e arraste para definir manualmente o seu tamanho", "publishLibrary": "Publicar sua própria biblioteca", "bindTextToElement": "Pressione Enter para adicionar o texto", "deepBoxSelect": "Segure Ctrl/Cmd para seleção profunda e para evitar arrastar", "eraserRevert": "Segure a tecla Alt para inverter os elementos marcados para exclusão", "firefox_clipboard_write": "Esse recurso pode ser ativado configurando a opção \"dom.events.asyncClipboard.clipboardItem\" como \"true\". Para alterar os sinalizadores do navegador no Firefox, visite a página \"about:config\".", "disableSnapping": ""}, "canvasError": {"cannotShowPreview": "Não é possível mostrar pré-visualização", "canvasTooBig": "A tela pode ser muito grande.", "canvasTooBigTip": "Dica: tente aproximar um pouco os elementos mais distantes."}, "errorSplash": {"headingMain": "Foi encontrado um erro. Tente <button>recarregar a página.</button>", "clearCanvasMessage": "Se recarregar a página não funcionar, tente <button>limpando a tela.</button>", "clearCanvasCaveat": " <PERSON><PERSON> em perda de trabalho ", "trackedToSentry": "O erro com o identificador {{eventId}} foi rastreado no nosso sistema.", "openIssueMessage": "Fomos muito cautelosos para não incluir suas informações de cena no erro. Se sua cena não for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informações abaixo, copiando e colando para a issue do GitHub.", "sceneContent": "<PERSON><PERSON><PERSON><PERSON>:"}, "roomDialog": {"desc_intro": "Você pode convidar pessoas para sua cena atual para colaborar com você.", "desc_privacy": "<PERSON>ão se preocupe, a sessão usa criptografia de ponta a ponta; portanto, o que você desenhar permanecerá privado. Nem mesmo nosso servidor poderá ver o que você cria.", "button_startSession": "<PERSON><PERSON><PERSON>", "button_stopSession": "<PERSON><PERSON>", "desc_inProgressIntro": "A sessão de colaboração ao vivo está agora em andamento.", "desc_shareLink": "Compartilhe este link com qualquer pessoa com quem você queira colaborar:", "desc_exitSession": "Interrompendo a sessão você irá se desconectar da sala, mas você poderá continuar trabalhando com a cena localmente. Observe que isso não afetará outras pessoas, e elas ainda poderão colaborar em suas versões.", "shareTitle": "Participe de uma sessão ao vivo de colaboração no Excalidraw"}, "errorDialog": {"title": "Erro"}, "exportDialog": {"disk_title": "Salvar no computador", "disk_details": "Exportar os dados da cena para um arquivo que você poderá importar mais tarde.", "disk_button": "Salvar em um arquivo", "link_title": "Link compartilhável", "link_details": "Exportar como link de apenas leitura.", "link_button": "Exportar link", "excalidrawplus_description": "Salvar a cena na sua área de trabalho Excalidraw+.", "excalidrawplus_button": "Exportar", "excalidrawplus_exportError": "Não é possível exportar para o Excalidraw+ neste momento..."}, "helpDialog": {"blog": "<PERSON><PERSON> o nosso blog", "click": "clicar", "deepSelect": "Seleção profunda", "deepBoxSelect": "Use a seleção profunda dentro da caixa para previnir arrastar", "curvedArrow": "Seta curva", "curvedLine": "Linha curva", "documentation": "Documentação", "doubleClick": "clique duplo", "drag": "arrastar", "editor": "Editor", "editLineArrowPoints": "<PERSON>ar linha/ponta da seta", "editText": "Editar texto / adicionar etiqueta", "github": "Encontrou algum problema? Nos informe", "howto": "Siga nossos guias", "or": "ou", "preventBinding": "Evitar fixação de seta", "tools": "Ferramentas", "shortcuts": "Atalhos de teclado", "textFinish": "<PERSON><PERSON><PERSON> edi<PERSON> (editor de texto)", "textNewLine": "Adicionar nova linha (editor de texto)", "title": "<PERSON><PERSON><PERSON>", "view": "Visualizar", "zoomToFit": "Ampliar para encaixar todos os elementos", "zoomToSelection": "Ampliar a seleção", "toggleElementLock": "Bloquear/desbloquear seleção", "movePageUpDown": "Mover a página para cima/baixo", "movePageLeftRight": "Mover a página para esquerda/direita"}, "clearCanvasDialog": {"title": "Limpar a tela"}, "publishDialog": {"title": "Publicar biblioteca", "itemName": "Nome do item", "authorName": "Nome do autor", "githubUsername": "Nome de usuário do GitHub", "twitterUsername": "Nome de usuário do <PERSON>", "libraryName": "Nome da Biblioteca", "libraryDesc": "Descrição da biblioteca", "website": "Site", "placeholder": {"authorName": "Seu nome ou nome de usuário", "libraryName": "Nome da sua biblioteca", "libraryDesc": "Descrição para ajudar as pessoas a entenderem o uso da sua da sua biblioteca", "githubHandle": "Identificador do GitHub (opcional), para que você possa editar a biblioteca depois de enviar para revisão", "twitterHandle": "Nome de usuário do Twitter (opcional), para que saibamos quem deve ser creditado se promovermos no Twitter", "website": "Link para o seu site pessoal ou outro lugar (opcional)"}, "errors": {"required": "Obrigatório", "website": "Informe uma URL válida"}, "noteDescription": "Envie sua biblioteca para ser incluída no <link>repositório de biblioteca pública</link>para outras pessoas usarem em seus desenhos.", "noteGuidelines": "A biblioteca precisa ser aprovada manualmente primeiro. Por favor leia o <link>orientações</link> antes de enviar. Você precisará de uma conta do GitHub para se comunicar e fazer alterações quando solicitado, mas não é estritamente necessário.", "noteLicense": "Ao enviar, você concorda que a biblioteca será publicada sob a <link>Licença MIT, </link>o que, em suma, significa que qualquer pessoa pode utilizá-los sem restrições.", "noteItems": "Cada item da biblioteca deve ter seu próprio nome para que seja filtrável. <PERSON><PERSON> seguintes itens da biblioteca serão incluídos:", "atleastOneLibItem": "Por favor, selecione pelo menos um item da biblioteca para começar", "republishWarning": "Nota: alguns dos itens selecionados estão marcados como já publicado/enviado. Você só deve reenviar itens ao atualizar uma biblioteca existente ou submissão."}, "publishSuccessDialog": {"title": "Biblioteca enviada", "content": "O<PERSON>gado {{authorName}}. Sua biblioteca foi enviada para análise. Você pode acompanhar o status<link>aqui</link>"}, "confirmDialog": {"resetLibrary": "Redefinir biblioteca", "removeItemsFromLib": "Remover itens selecionados da biblioteca"}, "imageExportDialog": {"header": "Exportar imagem", "label": {"withBackground": "Fundo", "onlySelected": "<PERSON>nte selecion<PERSON>s", "darkMode": "<PERSON><PERSON> es<PERSON>ro", "embedScene": "Incorporar cena", "scale": "Escala", "padding": "Margem interna"}, "tooltip": {"embedScene": "Os dados da cena serão salvos no arquivo PNG/SVG exportado para que a cena possa ser restaurada a partir dele.\nIsso aumentará o tamanho do arquivo exportado."}, "title": {"exportToPng": "Exportar como PNG", "exportToSvg": "Exportar como SVG", "copyPngToClipboard": "Copiar PNG para área de transferência"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Copiar para a área de transferência"}}, "encrypted": {"tooltip": "Seus desenhos são criptografados de ponta a ponta, então os servidores do Excalidraw nunca os verão.", "link": "Publicação de blog com criptografia de ponta a ponta no Excalidraw"}, "stats": {"angle": "<PERSON><PERSON><PERSON>", "element": "Elemento", "elements": "Elementos", "height": "Altura", "scene": "<PERSON><PERSON>", "selected": "Selecionado", "storage": "Armazenamento", "title": "Estatísticas para nerds", "total": "Total", "version": "Vers<PERSON>", "versionCopy": "Clique para copiar", "versionNotAvailable": "Versão não disponível", "width": "<PERSON><PERSON><PERSON>"}, "toast": {"addedToLibrary": "Adicionado à biblioteca", "copyStyles": "Estilos copiados.", "copyToClipboard": "Copiado para área de transferência.", "copyToClipboardAsPng": "{{exportSelection}} copiado para a área de transferência como PNG ({{exportColorScheme}})", "fileSaved": "Arquivo salvo.", "fileSavedToFilename": "Salvo em {filename}", "canvas": "tela", "selection": "se<PERSON>ção", "pasteAsSingleElement": "Use {{shortcut}} para colar como um único elemento,\nou cole em um editor de texto já existente", "unableToEmbed": "", "unrecognizedLinkFormat": ""}, "colors": {"transparent": "Transparente", "black": "Preto", "white": "Branco", "red": "Vermelho", "pink": "<PERSON>", "grape": "<PERSON><PERSON>", "violet": "<PERSON><PERSON>", "gray": "Cinza", "blue": "Azul", "cyan": "<PERSON><PERSON>", "teal": "Verde-azulado", "green": "Verde", "yellow": "<PERSON><PERSON>", "orange": "<PERSON><PERSON>", "bronze": "Bronze"}, "welcomeScreen": {"app": {"center_heading": "Todos os dados são salvos localmente no seu navegador.", "center_heading_plus": "Você queria ir para o Excalidraw+ em vez disso?", "menuHint": "Exportar, preferências, idiomas..."}, "defaults": {"menuHint": "Exportar, preferências e mais...", "center_heading": "Diagramas, Feito. Simples.", "toolbarHint": "Escolha uma ferramenta e comece a desenhar!", "helpHint": "Atalhos e ajuda"}}, "colorPicker": {"mostUsedCustomColors": "Cores personalizadas mais usadas", "colors": "Cores", "shades": "Tons", "hexCode": "Código hexadecimal", "noShades": "Sem tons disponíveis para essa cor"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Exportar como imagem", "button": "Exportar como imagem", "description": "Exportar os dados da cena para um arquivo que você poderá importar mais tarde."}, "saveToDisk": {"title": "Salvar no computador", "button": "Salvar no computador", "description": "Exportar os dados da cena para um arquivo que você poderá importar mais tarde."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Exportar para Excalidraw+", "description": "Salvar a cena na sua área de trabalho Excalidraw+."}}, "modal": {"loadFromFile": {"title": "Carregar de arquivo", "button": "Carregar de arquivo", "description": "Carregar de um arquivo irá <bold> substituir o conteúdo existente</bold>.<br></br><PERSON><PERSON><PERSON> pode salvar seu desenho primeiro usando uma das opções abaixo."}, "shareableLink": {"title": "<PERSON><PERSON><PERSON> de um link", "button": "Substituir meu conteúdo", "description": "Carregar um desenho externo irá <bold> substituir seu conteúdo existente</bold>.<br></br><PERSON><PERSON><PERSON> pode salvar seu desenho antes utilizando uma das opções abaixo."}}}, "mermaid": {"title": "", "button": "", "description": "", "syntax": "", "preview": ""}}