{"labels": {"paste": "Klistra in", "pasteAsPlaintext": "Klistra som oformaterad text", "pasteCharts": "Klistra in diagram", "selectAll": "<PERSON><PERSON> alla", "multiSelect": "Lägg till element till markering", "moveCanvas": "Flytta canvas", "cut": "<PERSON><PERSON><PERSON> ut", "copy": "<PERSON><PERSON><PERSON>", "copyAsPng": "Kopiera till urklipp som PNG", "copyAsSvg": "Kopiera till urklipp som SVG", "copyText": "<PERSON><PERSON><PERSON> till u<PERSON><PERSON><PERSON> som text", "copySource": "<PERSON><PERSON>ra källa till urklipp", "convertToCode": "Konvertera till kod", "bringForward": "<PERSON><PERSON> f<PERSON>", "sendToBack": "Flytta underst", "bringToFront": "Flytta främst", "sendBackward": "<PERSON><PERSON><PERSON>", "delete": "<PERSON> bort", "copyStyles": "<PERSON><PERSON><PERSON> stil", "pasteStyles": "Klistra in stil", "stroke": "<PERSON><PERSON>", "background": "Bakgrund", "fill": "Fyllnad", "strokeWidth": "Linjebredd", "strokeStyle": "Linjestil", "strokeStyle_solid": "Solid", "strokeStyle_dashed": "<PERSON><PERSON><PERSON>", "strokeStyle_dotted": "<PERSON><PERSON>", "sloppiness": "Slarvighet", "opacity": "Genomskinlighet", "textAlign": "Textjustering", "edges": "<PERSON><PERSON>", "sharp": "<PERSON><PERSON><PERSON>", "round": "Rund", "arrowheads": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arrowhead_none": "Inga", "arrowhead_arrow": "<PERSON>l", "arrowhead_bar": "<PERSON><PERSON><PERSON>", "arrowhead_circle": "<PERSON><PERSON><PERSON>", "arrowhead_circle_outline": "Cirkel (kontur)", "arrowhead_triangle": "<PERSON><PERSON><PERSON>", "arrowhead_triangle_outline": "Triangel (kontur)", "arrowhead_diamond": "<PERSON><PERSON><PERSON>", "arrowhead_diamond_outline": "Diamant (kontur)", "fontSize": "Teckenstorlek", "fontFamily": "Teckensnitt", "addWatermark": "<PERSON><PERSON><PERSON> till \"Skapad med Excalidraw\"", "handDrawn": "Handritad", "normal": "Normal", "code": "Kod", "small": "<PERSON>ten", "medium": "Medium", "large": "St<PERSON>", "veryLarge": "<PERSON><PERSON> stor", "solid": "Solid", "hachure": "Skraffering", "zigzag": "Sicksack", "crossHatch": "Skraffera med kors", "thin": "<PERSON><PERSON>", "bold": "Fet", "left": "<PERSON><PERSON><PERSON><PERSON>", "center": "Centrera", "right": "<PERSON><PERSON><PERSON>", "extraBold": "Extra fet", "architect": "Arkitekt", "artist": "Artist", "cartoonist": "Serietecknare", "fileTitle": "Filnamn", "colorPicker": "Färgväljare", "canvasColors": "Anv<PERSON><PERSON> på canvas", "canvasBackground": "Canvas-b<PERSON><PERSON><PERSON><PERSON>", "drawingCanvas": "Ritar canvas", "layers": "Lager", "actions": "Åtgärder", "language": "Språk", "liveCollaboration": "Samarbeta live...", "duplicateSelection": "Du<PERSON><PERSON><PERSON>", "untitled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "yourName": "Ditt namn", "madeWithExcalidraw": "Skapad med Excalidraw", "group": "Gruppera markering", "ungroup": "Avgruppera markering", "collaborators": "Medarbetare", "showGrid": "Visa rutnät", "addToLibrary": "Lägg till i biblioteket", "removeFromLibrary": "Ta bort från bibliotek", "libraryLoadingMessage": "Laddar bibliotek…", "libraries": "Bläddra i bibliotek", "loadingScene": "Laddar skiss…", "align": "<PERSON><PERSON>", "alignTop": "<PERSON>era ö<PERSON>", "alignBottom": "<PERSON><PERSON>", "alignLeft": "Justera vänster", "alignRight": "<PERSON><PERSON>", "centerVertically": "<PERSON><PERSON> vertikalt", "centerHorizontally": "<PERSON><PERSON>", "distributeHorizontally": "Fö<PERSON><PERSON> ho<PERSON>", "distributeVertically": "Fördela vertikalt", "flipHorizontal": "<PERSON><PERSON><PERSON> ho<PERSON>", "flipVertical": "Vänd vertikalt", "viewMode": "Visningsläge", "share": "Dela", "showStroke": "Visa färgväljare för linjefärg", "showBackground": "Visa färgväljare för bakgrundsfärg", "toggleTheme": "<PERSON><PERSON><PERSON><PERSON> tema", "personalLib": "Personligt bibliotek", "excalidrawLib": "Excalidraw bibliotek", "decreaseFontSize": "<PERSON><PERSON>", "increaseFontSize": "<PERSON><PERSON>", "unbindText": "<PERSON><PERSON><PERSON> bort text", "bindText": "Bind texten till behållaren", "createContainerFromText": "Radbryt text i en avgränsad yta", "link": {"edit": "<PERSON><PERSON><PERSON> länk", "editEmbed": "Redigera länk & bädda in", "create": "Skapa länk", "createEmbed": "Skapa länk & bädda in", "label": "<PERSON><PERSON><PERSON>", "labelEmbed": "Länka & bädda in", "empty": "Ingen länk är angiven"}, "lineEditor": {"edit": "<PERSON><PERSON><PERSON> linje", "exit": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "elementLock": {"lock": "<PERSON><PERSON><PERSON>", "unlock": "<PERSON><PERSON><PERSON> upp", "lockAll": "<PERSON><PERSON><PERSON> alla", "unlockAll": "<PERSON><PERSON><PERSON> upp alla"}, "statusPublished": "Publicerad", "sidebarLock": "<PERSON><PERSON><PERSON> sidofältet öppet", "selectAllElementsInFrame": "Markera alla element i rutan", "removeAllElementsFromFrame": "Ta bort alla element från rutan", "eyeDropper": "<PERSON><PERSON><PERSON><PERSON> färg från canvas", "textToDiagram": "Text till diagram", "prompt": "<PERSON><PERSON><PERSON>"}, "library": {"noItems": "Inga objekt tillagda ännu...", "hint_emptyLibrary": "<PERSON><PERSON><PERSON><PERSON> ett objekt på canvasen för att lägga till det här, eller installera ett bibliotek från det publika arkivet, nedan.", "hint_emptyPrivateLibrary": "<PERSON><PERSON><PERSON><PERSON> ett objekt på canvasen för att lägga till det här."}, "buttons": {"clearReset": "<PERSON><PERSON><PERSON><PERSON><PERSON> canvasen", "exportJSON": "Exportera till fil", "exportImage": "Exportera bild...", "export": "Spara till...", "copyToClipboard": "Kopiera till urklipp", "save": "Spara till aktuell fil", "saveAs": "Spara som", "load": "Öppna", "getShareableLink": "Hämta delbar länk", "close": "Stäng", "selectLanguage": "<PERSON><PERSON><PERSON><PERSON>", "scrollBackToContent": "Bläddra tillbaka till innehållet", "zoomIn": "Zooma in", "zoomOut": "Zooma ut", "resetZoom": "Återställ zoom", "menu": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "edit": "Rediger<PERSON>", "undo": "Å<PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON> om", "resetLibrary": "Återställ bibliotek", "createNewRoom": "Skapa ett nytt rum", "fullScreen": "Helskärm", "darkMode": "Mörkt läge", "lightMode": "Ljust läge", "zenMode": "Zen-läge", "objectsSnapMode": "<PERSON><PERSON><PERSON> mot objekt", "exitZenMode": "Gå ur zen-läge", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "Ren<PERSON>", "remove": "<PERSON> bort", "embed": "Växla inbäddning", "publishLibrary": "Publicera", "submit": "<PERSON><PERSON><PERSON>", "confirm": "Bekräfta", "embeddableInteractionButton": "<PERSON><PERSON><PERSON> för att interagera"}, "alerts": {"clearReset": "<PERSON><PERSON> rensar hela <PERSON>en. Är du säker?", "couldNotCreateShareableLink": "Kunde inte skapa delbar länk.", "couldNotCreateShareableLinkTooBig": "Kunde inte skapa delbar länk: skissen är för stor", "couldNotLoadInvalidFile": "Kunde inte ladda ogiltig fil", "importBackendFailed": "Importering f<PERSON><PERSON><PERSON> backend misslyck<PERSON>.", "cannotExportEmptyCanvas": "Kan inte exportera tom canvas.", "couldNotCopyToClipboard": "Kunde inte kopiera till urklipp.", "decryptFailed": "Kunde inte avkryptera data.", "uploadedSecurly": "Uppladdning har säkrats med kryptering från ände till ände. vilket innebär att Excalidraw server och tredje part inte kan läsa innehållet.", "loadSceneOverridePrompt": "Laddning av extern skiss kommer att ersätta ditt befintliga innehåll. Vill du fortsätta?", "collabStopOverridePrompt": "Att stoppa sessionen kommer att skriva över din föregående, lokalt lagrade skiss. Är du säker?\n\n(Om du vill behålla din lokala skiss, stäng bara webbläsarfliken istället.)", "errorAddingToLibrary": "Kunde inte lägga till objekt i biblioteket", "errorRemovingFromLibrary": "Kunde inte ta bort objekt från biblioteket", "confirmAddLibrary": "<PERSON><PERSON> kommer att lägga till {{numShapes}} form(er) till ditt bibliotek. Är du säker?", "imageDoesNotContainScene": "Den här bilden verkar inte innehålla någon skissdata. Har du aktiverat inbäddning av skiss under export?", "cannotRestoreFromImage": "Skiss kunde inte återställas från denna bild<PERSON>l", "invalidSceneUrl": "Det gick inte att importera skiss från den angivna webbadressen. Antingen har den fel format, eller så innehåller den ingen giltig Excalidraw JSON data.", "resetLibrary": "<PERSON>ta kommer att rensa ditt bibliotek. Är du säker?", "removeItemsFromsLibrary": "Ta bort {{count}} objekt från biblioteket?", "invalidEncryptionKey": "Krypteringsnyckeln måste vara 22 tecken. Livesamarbetet är inaktiverat.", "collabOfflineWarning": "Ingen internetanslutning tillgänglig.\nDina ändringar kommer inte att sparas!"}, "errors": {"unsupportedFileType": "Filtypen stöds inte.", "imageInsertError": "Kunde inte infoga bild. Försök igen senare...", "fileTooBig": "<PERSON>n är för stor. Maximal tillåten storlek är {{maxSize}}.", "svgImageInsertError": "Kunde inte infoga SVG-bild. SVG-koden ser ogiltig ut.", "failedToFetchImage": "Kunde inte hämta bilden.", "invalidSVGString": "Ogiltig SVG.", "cannotResolveCollabServer": "Det gick inte att ansluta till samarbets-servern. Ladda om sidan och försök igen.", "importLibraryError": "Kunde inte ladda bibliotek", "collabSaveFailed": "Det gick inte att spara i backend-databasen. Om problemen kvarstår bör du spara filen lokalt för att se till att du inte förlorar ditt arbete.", "collabSaveFailed_sizeExceeded": "Det gick inte att spara till backend-databasen, whiteboarden verkar vara för stor. Du bör spara filen lokalt för att du inte ska förlora ditt arbete.", "imageToolNotSupported": "Bilder <PERSON>r inak<PERSON>.", "brave_measure_text_error": {"line1": "Det ser ut som om du använder Brave-webbläsaren med <bold>Aggressivt Blockera fingeravtryck</bold> inställningen aktiverad.", "line2": "Detta kan resultera i trasiga <bold>Textelement</bold> i dina ritningar.", "line3": "Vi rekommenderar starkt att du inaktiverar den här inställningen. Du kan följa <link>des<PERSON> steg</link> för att inaktivera den.", "line4": "Om inaktivering av denna inställning inte åtgärdar visningen av textelement, öppna ett <issueLink>ärende</issueLink> på v<PERSON><PERSON>, eller skriv till oss på <discordLink>Discord</discordLink>"}, "libraryElementTypeError": {"embeddable": "Inbäddbara element kan inte läggas till i biblioteket.", "iframe": "IFrame-element kan inte läggas till i biblioteket.", "image": "<PERSON><PERSON><PERSON> för att lägga till bilder till biblioteket kommer snart!"}, "asyncPasteFailedOnRead": "Kunde inte klistra in (kunde inte läsa från urk<PERSON>).", "asyncPasteFailedOnParse": "Kunde inte klistra in.", "copyToSystemClipboardFailed": "Kunde inte kopiera till urklipp."}, "toolBar": {"selection": "<PERSON><PERSON>", "image": "Infoga bild", "rectangle": "<PERSON><PERSON><PERSON><PERSON>", "diamond": "<PERSON><PERSON><PERSON>", "ellipse": "Ellips", "arrow": "<PERSON>l", "line": "<PERSON><PERSON>", "freedraw": "<PERSON>", "text": "Text", "library": "Bibliotek", "lock": "Håll valt verktyg aktivt efter ritande", "penMode": "Pennläge - förhindra touch", "link": "Lägg till / Uppdatera länk för en vald form", "eraser": "<PERSON><PERSON><PERSON><PERSON>", "frame": "Rutverktyg", "magicframe": "Trådram till kod", "embeddable": "<PERSON><PERSON>dda in (web)", "laser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hand": "Hand (panoreringsverktyg)", "extraTools": "Fler verktyg", "mermaidToExcalidraw": "Mermaid till Excalidraw", "magicSettings": "AI-inställningar"}, "headings": {"canvasActions": "Canvas-åtgärder", "selectedShapeActions": "Valda formåtgärder", "shapes": "Former"}, "hints": {"canvasPanning": "<PERSON><PERSON><PERSON> att flytta whiteboarden, håll mush<PERSON>let eller mellanslagstangenten medan du drar eller använd handverktyget", "linearElement": "<PERSON><PERSON><PERSON> för att starta flera punkter, dra för en linje", "freeDraw": "<PERSON><PERSON><PERSON> och dra, slä<PERSON> när du är klar", "text": "Tips: du kan ocks<PERSON> lägga till text genom att dubbelklicka var som helst med markeringsverktyget", "embeddable": "Klicka-dra för att skapa en webbplats-inbäddning", "text_selected": "Dubbelklicka eller tryck ENTER för att redigera text", "text_editing": "Tryck Escape eller CtrlOrCmd + ENTER för att slutföra redigeringen", "linearElementMulti": "Klicka på sista punkten eller tryck Escape eller Enter för att avsluta", "lockAngle": "Du kan begränsa vinkeln genom att hålla SKIFT", "resize": "Du kan behålla proportioner genom att hålla SHIFT medan du ändrar storlek,\nhåller du ALT ändras storlek relativt mitten", "resizeImage": "Du kan ändra storlek fritt genom att hålla SHIFT,\nhåll ALT för att ändra storlek från mitten", "rotate": "Du kan begrä<PERSON><PERSON> vinklar genom att hålla SHIFT medan du roterar", "lineEditor_info": "<PERSON><PERSON><PERSON> Ctrl/Cmd och dubbelklicka eller tryck på Ctrl/Cmd + <PERSON>ter för att redigera punkter", "lineEditor_pointSelected": "<PERSON>ck på Ta bort för att ta bort punkt(er), Ctrl + D eller Cmd + D för att duplicera, eller dra för att flytta", "lineEditor_nothingSelected": "<PERSON><PERSON>lj en punkt att redigera (håll SHIFT för att välja flera),\nel<PERSON> håll ned Alt och klicka för att lägga till nya punkter", "placeImage": "<PERSON><PERSON><PERSON> för att placera bilden, el<PERSON> klicka och dra för att ställa in dess storlek manuellt", "publishLibrary": "Publicera ditt eget bibliotek", "bindTextToElement": "Tryck på Enter för att lägga till text", "deepBoxSelect": "<PERSON><PERSON><PERSON><PERSON> eller Cmd för att djupvälja, och för att förhindra att dra", "eraserRevert": "Håll Alt för att återställa de element som är markerade för borttagning", "firefox_clipboard_write": "<PERSON>na funktion kan sannolikt aktiveras genom att ställa in \"dom.events.asyncClipboard.clipboardItem\" flaggan till \"true\". <PERSON><PERSON><PERSON> att ändra webbläsarens flaggor i Firefox, besö<PERSON> \"about:config\" sidan.", "disableSnapping": "<PERSON><PERSON><PERSON> Ctrl eller Cmd för att inaktivera fästning"}, "canvasError": {"cannotShowPreview": "Kan inte visa förhandsgranskning", "canvasTooBig": "<PERSON><PERSON><PERSON> kan vara för stor.", "canvasTooBigTip": "Tips: prova att flytta de mest avlägsna elementen lite närmare varandra."}, "errorSplash": {"headingMain": "Ett fel uppstod. Försök <button>med att läsa in sidan på nytt.</button>", "clearCanvasMessage": "Om omladdning inte fungerar, <PERSON><PERSON><PERSON><PERSON><PERSON> <button>rensa canvasen.</button>", "clearCanvasCaveat": " <PERSON>ta kommer att leda till förlust av arbete ", "trackedToSentry": "Felet med identifieraren {{eventId}} spårades på vårt system.", "openIssueMessage": "Vi var mycket försiktiga med att inte inkludera din skissinformation om felet. Om din skiss inte är privat, vänligen överväga att följa upp på vår <button>buggspårare.</button> Vänligen inkludera information nedan genom att kopiera och klistra in i GitHub-problemet.", "sceneContent": "Skissinnehåll:"}, "roomDialog": {"desc_intro": "Du kan bjuda in personer till din nuvarande skiss för att samarbeta med dig.", "desc_privacy": "<PERSON><PERSON> dig inte, sessionen anv<PERSON><PERSON> kryptering från ände till ände, så vad du än ritar kommer att förbli privat. Inte ens vår server kommer att kunna se vad du skissar.", "button_startSession": "<PERSON><PERSON> sessionen", "button_stopSession": "Stoppa session", "desc_inProgressIntro": "<PERSON>u på<PERSON>r en live-samarbetssession.", "desc_shareLink": "<PERSON>a denna länk med någon du vill samarbeta med:", "desc_exitSession": "Att avbryta sessionen kommer att koppla bort dig från rummet, men du kommer att kunna fortsätta arbeta med skissen, lokalt. Observera att detta inte påverkar and<PERSON> m<PERSON>, och de kommer fortfarande att kunna samarbeta på deras version.", "shareTitle": "Delta i en live-samarbetssession på Excalidraw"}, "errorDialog": {"title": "<PERSON><PERSON>"}, "exportDialog": {"disk_title": "Spara till disk", "disk_details": "Exportera skissdata till en fil som du kan importera från senare.", "disk_button": "Spara till fil", "link_title": "<PERSON><PERSON> länk", "link_details": "Exportera som en skrivskyddad länk.", "link_button": "Exportera till länk", "excalidrawplus_description": "Spara skissen till din Excalidraw+ arbetsyta.", "excalidrawplus_button": "Exportera", "excalidrawplus_exportError": "Det gick inte att exportera till Excalidraw+ just nu..."}, "helpDialog": {"blog": "<PERSON><PERSON><PERSON> v<PERSON> blogg", "click": "klicka", "deepSelect": "<PERSON><PERSON><PERSON><PERSON>", "deepBoxSelect": "<PERSON><PERSON><PERSON><PERSON> inom boxen, och fö<PERSON><PERSON>dra att dra", "curvedArrow": "<PERSON><PERSON><PERSON><PERSON> pil", "curvedLine": "<PERSON><PERSON>j<PERSON> linje", "documentation": "Dokumentation", "doubleClick": "dubbelklicka", "drag": "dra", "editor": "Redigerare", "editLineArrowPoints": "<PERSON><PERSON>a linje-/pilpunkter", "editText": "Redigera text / lägg till etikett", "github": "Hittat ett problem? Rapportera", "howto": "Följ våra guider", "or": "eller", "preventBinding": "<PERSON><PERSON><PERSON><PERSON><PERSON> pil<PERSON>", "tools": "Verktyg", "shortcuts": "Tangentbordsgenvägar", "textFinish": "<PERSON><PERSON><PERSON><PERSON><PERSON> red<PERSON> (text)", "textNewLine": "<PERSON><PERSON><PERSON> till ny rad (text)", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Visa", "zoomToFit": "Zooma för att rymma alla element", "zoomToSelection": "Zooma till markering", "toggleElementLock": "Lås/L<PERSON> upp valda", "movePageUpDown": "Flytta sida upp/ner", "movePageLeftRight": "Flytta sida vänster/höger"}, "clearCanvasDialog": {"title": "<PERSON><PERSON> canvas"}, "publishDialog": {"title": "Publicera bibliotek", "itemName": "Objektnamn", "authorName": "Upphovsmannens namn", "githubUsername": "GitHub-användarnamn", "twitterUsername": "Twitter-användarnamn", "libraryName": "Biblioteksnamn", "libraryDesc": "Biblioteksbeskrivning", "website": "Webbp<PERSON>s", "placeholder": {"authorName": "Ditt namn eller användarnamn", "libraryName": "Namn på ditt bibliotek", "libraryDesc": "Beskrivning av ditt bibliotek för att hjälpa människor att förstå dess användning", "githubHandle": "Github-anv<PERSON><PERSON><PERSON><PERSON>n (valfritt), så att du kan redigera biblioteket när du har skickat in det för granskning", "twitterHandle": "Twitter-användarnamn (valfritt), så vi vet vem att kreditera när du marknadsför på Twitter", "website": "Länk till din personliga webbplats eller någon annan (valfritt)"}, "errors": {"required": "Obligatoriskt", "website": "Ange en giltig URL"}, "noteDescription": "Skicka ditt bibliotek för att inkluderas i <link>det offentliga bibliotekets arkiv</link>för andra människor att använda i sina skisser.", "noteGuidelines": "Biblioteket måste godkännas manuellt först. Vänligen läs <link>riktlinjerna</link> innan du skickar in. Du behöver ett GitHub-konto för att kommunicera och göra ändringar om så önskas, men det krävs inte.", "noteLicense": "Genom att skicka in godkänner du att biblioteket kommer att publiceras under <link>MIT-licens, </link>vilket kort sagt betyder att vem som helst kan använda det utan restriktioner.", "noteItems": "Varje objekt måste ha sitt eget namn så att det är filtrerbart. Följande objekt kommer att inkluderas:", "atleastOneLibItem": "<PERSON><PERSON><PERSON><PERSON> minst ett biblioteksobjekt för att komma igång", "republishWarning": "Obs: några av de markerade objekten är redan markerade som publicerade/skickade. Du bör endast skicka objekt igen när du uppdaterar ett befintligt bibliotek eller inlämning."}, "publishSuccessDialog": {"title": "Bibliotek inskickat", "content": "Tack {{authorName}}. Ditt bibliotek har skickats för granskning. Du kan följa status<link>här</link>"}, "confirmDialog": {"resetLibrary": "Återställ bibliotek", "removeItemsFromLib": "Ta bort markerade objekt från biblioteket"}, "imageExportDialog": {"header": "Exportera bild", "label": {"withBackground": "Bakgrund", "onlySelected": "Endast markerade", "darkMode": "Mörkt läge", "embedScene": "<PERSON><PERSON>dda in skiss", "scale": "<PERSON><PERSON><PERSON>", "padding": "Utfyllnad"}, "tooltip": {"embedScene": "Skissdata kommer att sparas i den exporterade PNG/SVG-filen så att skissen kan återställas från den.\nKommer att öka exporterad filstorlek."}, "title": {"exportToPng": "Exportera till PNG", "exportToSvg": "Exportera till SVG", "copyPngToClipboard": "Kopiera PNG till urklipp"}, "button": {"exportToPng": "PNG", "exportToSvg": "SVG", "copyPngToClipboard": "Kopiera till urklipp"}}, "encrypted": {"tooltip": "<PERSON><PERSON> skisser är krypterade från ände till ände så Excalidraws servrar kommer aldrig att se dem.", "link": "Blogginlägg om kryptering från ände till ände i Excalidraw"}, "stats": {"angle": "<PERSON><PERSON>", "element": "Element", "elements": "Element", "height": "<PERSON><PERSON><PERSON><PERSON>", "scene": "Skiss", "selected": "Valda", "storage": "<PERSON><PERSON><PERSON>", "title": "Statistik för nördar", "total": "Totalt", "version": "Version", "versionCopy": "<PERSON><PERSON><PERSON> för att kopiera", "versionNotAvailable": "Versionen är inte tillgänglig", "width": "Bredd"}, "toast": {"addedToLibrary": "Tillagd i biblioteket", "copyStyles": "<PERSON><PERSON><PERSON> stilar.", "copyToClipboard": "Kopierad till urklipp.", "copyToClipboardAsPng": "Kopierade {{exportSelection}} till urklipp som PNG\n({{exportColorScheme}})", "fileSaved": "Fil sparad.", "fileSavedToFilename": "Sparad till {filename}", "canvas": "canvas", "selection": "markering", "pasteAsSingleElement": "Använd {{shortcut}} för att klistra in som ett enda element,\neller klistra in i en befintlig textredigerare", "unableToEmbed": "Att bädda in denna webbadress är för närvarande inte tillåtet. Skapa en problemrapport på GitHub för att begära att webbadressen vitlistas.", "unrecognizedLinkFormat": "Länken du bäddade in matchar inte det förväntade formatet. Försök klistra in 'embed'-strängen som tillhandahålls av källwebbplatsen"}, "colors": {"transparent": "Genomskinlig", "black": "<PERSON><PERSON><PERSON>", "white": "Vit", "red": "<PERSON><PERSON><PERSON>", "pink": "<PERSON>", "grape": "<PERSON>", "violet": "<PERSON><PERSON>", "gray": "Grå", "blue": "Blå", "cyan": "Turkos", "teal": "Blågrön", "green": "<PERSON><PERSON><PERSON><PERSON>", "yellow": "Gul", "orange": "Orange", "bronze": "Brons"}, "welcomeScreen": {"app": {"center_heading": "All data sparas lokalt i din webbläsare.", "center_heading_plus": "Ville du gå till Excalidraw+ istället?", "menuHint": "Exportera, inställningar, språk, ..."}, "defaults": {"menuHint": "Exportera, inställningar och mer...", "center_heading": "Förenklade. Diagram.", "toolbarHint": "Välj ett verktyg & bö<PERSON>ja rita!", "helpHint": "Genvägar & hjälp"}}, "colorPicker": {"mostUsedCustomColors": "Mest frekvent använda anpassade färger", "colors": "<PERSON><PERSON><PERSON>", "shades": "<PERSON><PERSON><PERSON>", "hexCode": "Hex-kod", "noShades": "Inga nyanser tillgängliga för denna färg"}, "overwriteConfirm": {"action": {"exportToImage": {"title": "Exportera som bild", "button": "Exportera som bild", "description": "Exportera scendata som en bild från vilken du kan importera senare."}, "saveToDisk": {"title": "Spara till disk", "button": "Spara till disk", "description": "Exportera scendata till en fil från vilken du kan importera senare."}, "excalidrawPlus": {"title": "Excalidraw+", "button": "Exportera till Excalidraw+", "description": "Spara skissen till din Excalidraw+ arbetsyta."}}, "modal": {"loadFromFile": {"title": "<PERSON><PERSON><PERSON> in från fil", "button": "<PERSON><PERSON><PERSON> in från fil", "description": "Laddar från en fil kommer <bold>ersätta ditt befintliga innehåll</bold>.<br></br>Du kan säkerhetskopiera din ritning först med hjälp av ett av alternativen nedan."}, "shareableLink": {"title": "<PERSON><PERSON><PERSON> in från länk", "button": "<PERSON><PERSON><PERSON><PERSON> mitt innehåll", "description": "Inläsning av en extern ritning kommer <bold>ersätta ditt befintliga innehåll</bold>.<br></br>Du kan säkerhetskopiera din ritning först genom att använda ett av alternativen nedan."}}}, "mermaid": {"title": "Mermaid till Excalidraw", "button": "Infoga", "description": "<PERSON><PERSON><PERSON> n<PERSON> stöds endast <flowchartLink>Flödesdiagram</flowchartLink>,<sequenceLink> Sekvensdiagram </sequenceLink> och <classLink>Klassdiagram</classLink>. De andra typerna kommer att återges som bild i Excalidraw.", "syntax": "Mermaid-syntax", "preview": "Förhandsgranska"}}