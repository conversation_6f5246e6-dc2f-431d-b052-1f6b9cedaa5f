{"api_key": "A7FrUhXh5XrOtv2kv1pcPw", "test_date": "2025-06-20T11:00:01.449Z", "available_endpoints": [{"name": "Health Check", "method": "GET", "path": "/auth/health", "status": 200}, {"name": "Organization Enrichment", "method": "GET", "path": "/organizations/enrich", "status": 200, "rate_limit": "199"}, {"name": "Contacts Search", "method": "POST", "path": "/contacts/search", "status": 200, "rate_limit": "199"}, {"name": "Accounts Search", "method": "POST", "path": "/accounts/search", "status": 200, "rate_limit": "199"}], "unavailable_endpoints": [{"name": "People Search", "method": "POST", "path": "/mixed_people/search", "error_status": 403, "error_message": "api/v1/mixed_people/search is not accessible with this api_key"}, {"name": "People Search Alt", "method": "POST", "path": "/people/search", "error_status": 403, "error_message": "api/v1/people/search is not accessible with this api_key"}, {"name": "Organization Search", "method": "POST", "path": "/organizations/search", "error_status": 403, "error_message": "api/v1/organizations/search is not accessible with this api_key"}, {"name": "People Enrichment", "method": "POST", "path": "/people/enrich", "error_status": 403, "error_message": "api/v1/people/enrich is not accessible with this api_key"}, {"name": "<PERSON><PERSON>", "method": "GET", "path": "/email_finder", "error_status": 404, "error_message": "Request failed with status code 404"}, {"name": "Email Veri<PERSON>r", "method": "GET", "path": "/email_verifier", "error_status": 404, "error_message": "Request failed with status code 404"}, {"name": "Usage Stats", "method": "POST", "path": "/usage_stats", "error_status": 404, "error_message": "Request failed with status code 404"}, {"name": "Users List", "method": "GET", "path": "/users", "error_status": 404, "error_message": "Not Found"}], "working_features": []}